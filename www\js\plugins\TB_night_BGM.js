(function() {
    var _Game_Map_autoplay = Game_Map.prototype.autoplay;

    Game_Map.prototype.autoplay = function() {
        console.log("Game_Map: BGM volume {" + $gameVariables.value(5) + "}");
        if ($dataMap.note.includes("夜固定BGM") && $gameVariables.value(5) == 1) {
            AudioManager.playBgm({name: "gozen<PERSON><PERSON>of<PERSON>ui", volume: 100, pitch: 100, pan: 0});
        } else {
            if ($dataMap.note.includes("BGM固定")) {
                $gameSwitches.setValue(8, false);
            }
            if ($dataMap.autoplayBgm && !$gameSwitches.value(8)) {
                AudioManager.playBgm($dataMap.bgm);
            }
            if ($dataMap.autoplayBgs && !$gameSwitches.value(8)) {
                AudioManager.playBgs($dataMap.bgs);
            }
        }
    };

})();
