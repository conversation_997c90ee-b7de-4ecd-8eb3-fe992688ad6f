/*:
 * @plugindesc ver.1.0.1 非表示の装備スロットを実装します
 * <AUTHOR>
 *
 * @help
 * 以下のタグをアクターメモ欄に記述することで
 * 指定した番号の装備スロットを非表示にします。
 * <KNS_HiddenSlot: 1, 2>
 * 
 * 装備スロット名はlogsheet.csvに「Equip Type (装備タイプID)」の
 * 行を追加することで指定可能です。
 * Equip Type 1,武器,Weapon,,,,
 * Equip Type 2,衣装,Clothes,,,,
 * Equip Type 3,頭衣装,Head,,,,
 * Equip Type 4,タイツ,Tights,,,,
 * Equip Type 5,下着,Underwear,,,,
 * Equip Type 6,指輪,Ring,,,,
 * 
 * ■更新履歴
 * ver.1.0.0(2023-09-18)
 * - デモ
 * ver.1.0.1(2023-11-09)
 * - 装備封印の対応漏れに対応
 */

const KNS_HiddenSlot = {
    name: "KNS_HiddenSlot",
    hideTagName: "KNS_HiddenSlot"
};

(function(){
    //===================================================
    // alias Game_Actor
    //===================================================
    Game_Actor.prototype.knsGetHiddenSlot = function(){
        const dactor = this.actor();
        if (dactor.meta[KNS_HiddenSlot.hideTagName] === undefined){
            return [];
        }else{
            return dactor.meta[KNS_HiddenSlot.hideTagName].split(",").map(
                function(index){ return Math.floor(index); }
            );
        }
    }

    Game_Actor.prototype.knsGetRealEquipSlot = function(){
        const meta = this.knsGetHiddenSlot();
        return this.equipSlots().filter(function(_, i){
            return !meta.includes(i);
        });
    }

    //===================================================
    // alias Window_EquipSlot
    //===================================================
    Window_EquipSlot.prototype.maxItems = function() {
        return this._actor ? this._actor.knsGetRealEquipSlot().length : 0;
    };

    Window_EquipSlot.prototype.knsGetRealIndex = function(index=this.index()) {
        if (this._actor){
            let realIndex = index;
            this._actor.knsGetHiddenSlot().forEach(function(id){
                if (realIndex >= id){ realIndex += 1; }
            });
            return realIndex;
        }else{
            return index;
        }
    };
    
    Window_EquipSlot.prototype.item = function() {
        return this._actor ? this._actor.equips()[this.knsGetRealIndex()] : null;
    };

    Window_EquipSlot.prototype.isEnabled = function(index) {
        const realIndex = this.knsGetRealIndex(index);
        return this._actor ? this._actor.isEquipChangeOk(realIndex) : false;
    };
    
    Window_EquipSlot.prototype.slotName = function(index) {
        var slots = this._actor.equipSlots();
        return this._actor ? $LogSheetCSV.Get("Equip Type " + slots[index]) : '';
    };
    
    Window_EquipSlot.prototype.update = function() {
        Window_Selectable.prototype.update.call(this);
        if (this._itemWindow) {
            this._itemWindow.setSlotId(this.knsGetRealIndex());
        }
    };
    
    Window_EquipSlot.prototype.drawItem = function(index) {
        if (this._actor) {
            var rect = this.itemRectForText(index);
            this.changeTextColor(this.systemColor());
            this.changePaintOpacity(this.isEnabled(index));
            const realIndex = this.knsGetRealIndex(index);
            this.drawText(this.slotName(realIndex), rect.x, rect.y, 138, this.lineHeight());
            this.drawItemName(this._actor.equips()[realIndex], rect.x + 138, rect.y);
            this.changePaintOpacity(true);
        }
    };
    
    //===================================================
    // alias Scene_Equip
    //===================================================
    Scene_Equip.prototype.onItemOk = function() {
        SoundManager.playEquip();
        this.actor().changeEquip(this._slotWindow.knsGetRealIndex(), this._itemWindow.item());
        this._slotWindow.activate();
        this._slotWindow.refresh();
        this._itemWindow.deselect();
        this._itemWindow.refresh();
        this._statusWindow.refresh();
    };
    
})();