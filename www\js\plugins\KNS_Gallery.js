/*:
 * @plugindesc ver.1.0.2 ギャラリー画面を実装します。
 * <AUTHOR>
 * 
 * @param Map
 * @text ■マップ
 * 
 * @param InitMapId
 * @text 回想開始マップID
 * @parent Map
 * @type map
 * @default 8
 * 
 * @param InitMapX
 * @text 回想マップX
 * @parent Map
 * @type number
 * @default 1
 * 
 * @param InitMapY
 * @text 回想マップY
 * @parent Map
 * @type number
 * @default 1
 * 
 * @param InitTransparent
 * @text 回想開始時プレイヤー透過状態
 * @parent Map
 * @type boolean
 * @default true
 * 
 * @param Music
 * @text ■音声
 * 
 * @param SceneBgmName
 * @text 回想シーンBGM
 * @parent Music
 * @type file
 * @dir audio/bgm
 * @default Castle2
 * 
 * @param SceneBgmVolume
 * @text 回想シーンBGM音量
 * @parent Music
 * @type number
 * @default 80
 * 
 * @param SceneBgmPitch
 * @text 回想シーンBGMピッチ
 * @parent Music
 * @type number
 * @default 100
 * 
 * @param terminateAudioFadeTime
 * @text 終了時音声フェード時間
 * @parent Music
 * @type number
 * @default 2000
 * @desc ミリ秒で指定（1秒=1000）
 * 
 * @param System
 * @text ■システム
 * 
 * @param TitleCommand
 * @text タイトルコマンド「ギャラリー」
 * @parent System
 * @default Gallery
 * 
 * @param SwitchInGallery
 * @text 回想中スイッチ
 * @parent System
 * @type switch
 * @default 1
 * 
 * @param VarCharaId
 * @text 回想キャラID
 * @parent System
 * @type variable
 * @default 1
 * 
 * @param VarSceneId
 * @text 回想シーンID
 * @parent System
 * @type variable
 * @default 2
 * 
 * @param CommonEventId
 * @text 回想開始処理コモン
 * @parent System
 * @type common_event
 * @default 1000
 * 
 * @param ImgBackground
 * @text ギャラリーシーン背景
 * @parent System
 * @type file
 * @dir img/Gallery_Mode
 * @default unavailable
 * 
 * 
 * @param CharaWindow
 * @text ■キャラウィンドウ
 * @type struct<WindowInfo>
 * @default {"x":"0","y":"0","width":"240","height":"960","maxCols":"1","spacing":"8","spacingVertical":"16"}
 * 
 * @param CharaList
 * @text キャラIDと対応画像
 * @parent CharaWindow
 * @type struct<CharaData>[]
 * @default ["{\"CharaId\":\"1\",\"CharaImage\":\"chara1\"}","{\"CharaId\":\"2\",\"CharaImage\":\"chara2\"}"]
 * 
 * 
 * @param SceneWindow
 * @text ■シーンウィンドウ
 * @type struct<WindowInfo>
 * @default {"x":"240","y":"0","width":"1040","height":"960","maxCols":"3","spacing":"24","spacingVertical":"16"}
 * 
 * 
 * @param ImgUnavailableScene
 * @text 未開放シーン画像
 * @parent SceneWindow
 * @type file
 * @dir img/Gallery_Mode
 * @default unavailable
 * 
 * 
 * @help
 * ※このプラグインはKNS_GlobalInfo以降に設置してください。
 * ギャラリー画面と回想時の処理を提供します。
 * 
 * 【提供するスクリプト】
 * KNS_Gallery.getProgress(キャラID, シーンID);
 * 　キャラIDとシーンIDで指定された回想の進行度を返します。
 * 例）$gameVariables.setValue(100, KNS_Gallery.getProgress(1, 2));
 * 
 * KNS_Gallery.setProgress(キャラID, シーンID, 進行度);
 * 　キャラIDとシーンIDで指定された回想の進行度を変更します。
 * 　指定された進行度が現行の値よりも小さいときは何も行いません。
 * 例）KNS_Gallery.setProgress(1, 2, 5);
 * 
 * 【提供するプラグインコマンド】
 * KNS_Gallery terminate
 * 　回想中スイッチがONであれば回想を終了しギャラリー画面に復帰します。
 * 
 * ver.1.0.0(2023-07-24)
 * - デモ
 * ver.1.0.1(2023-08-06)
 * - デモ
 * ver.1.0.2(2023-08-08)
 * - タイトルコマンドの「ギャラリー」の位置をオプションの上に変更
 * - 回想終了時音声のフェードアウト処理を追加しました
 * - 回想シーンにBGMを指定できるよう変更しました
 */
/*~struct~CharaData:
 * @param CharaId
 * @text キャラID
 * @type number
 * @default 1
 * 
 * @param CharaImage
 * @text キャラ画像
 * @type file
 * @dir img/Gallery_Mode
 * @default chara1
 */
/*~struct~WindowInfo:
 * @param x
 * @text X座標
 * @type number
 * @default 0
 * @min -65536
 * @max 65535
 * 
 * @param y
 * @text Y座標
 * @type number
 * @default 0
 * @min -65536
 * @max 65535
 * 
 * @param width
 * @text ウィンドウ横幅
 * @type number
 * @default 1
 * @min 1
 * @max 65535
 * 
 * @param height
 * @text ウィンドウ縦幅
 * @type number
 * @default 1
 * @min 1
 * @max 65535
 * 
 * @param maxCols
 * @text 最大横並び個数
 * @type number
 * @default 3
 * 
 * @param spacing
 * @text 横パディング
 * @type number
 * @default 24
 * 
 * @param spacingVertical
 * @text 縦パディング
 * @type number
 * @default 16
 */

//=======================================================
// new KNS_Gallery
//=======================================================
const KNS_Gallery = {
    name: "KNS_Gallery",
    param: null,
    maxProgress: 99,
    parseNumber: function(obj, key){
        return obj[key] = Number(obj[key]);
    },
    parseWindowInfo: function(parent, key){
        const obj = JsonEx.parse(parent[key]);
        this.parseNumber(obj, 'x');
        this.parseNumber(obj, 'y');
        this.parseNumber(obj, 'width');
        this.parseNumber(obj, 'height');

        this.parseNumber(obj, 'maxCols');
        this.parseNumber(obj, 'spacing');
        this.parseNumber(obj, 'spacingVertical');
        return parent[key] = obj;
    },
    getScene: function(charaId, sceneId){
        if ($csvGalleryList[charaId]){
            return $csvGalleryList[charaId][sceneId] || null;
        }
        return null;
    },
    getProgressInfo: function(){
        let info = KNS_GlobalInfo.read(this.name);
        if (info === undefined){
            KNS_GlobalInfo.write(this.name, info = {});
        }
        return info;
    },
    getProgress: function(charaId, sceneId){
        const scene = this.getScene(charaId, sceneId);
        if (scene){
            const info = this.getProgressInfo();
            if (info[charaId] === undefined){
                info[charaId] = {};
            }
            if (info[charaId][sceneId] === undefined){
                return scene.initProgress;
            }else{
                return info[charaId][sceneId];
            }
        }else{
            console.log(`回想[${charaId}, ${sceneId}]が存在しません！`)
            return -1;
        }
    },
    setProgress: function(charaId, sceneId, value){
        const scene = this.getScene(charaId, sceneId);
        if (scene){
            value = Math.min(Math.max(0, value), this.maxProgress);
            let curValue = this.getProgress(charaId, sceneId);
            if (curValue !== -1 && curValue < value){
                this.getProgressInfo()[charaId][sceneId] = value;
            }
        }else{
            console.log(`回想[${charaId}, ${sceneId}]が存在しないため進行度を変更できません！`)
        }
    },
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.parseNumber(this.param, 'SwitchInGallery');
    this.parseNumber(this.param, 'VarCharaId');
    this.parseNumber(this.param, 'VarSceneId');

    this.parseNumber(this.param, 'InitMapId');
    this.parseNumber(this.param, 'InitMapX');
    this.parseNumber(this.param, 'InitMapY');
    this.parseNumber(this.param, 'terminateAudioFadeTime');

    this.parseNumber(this.param, 'SceneBgmVolume');
    this.parseNumber(this.param, 'SceneBgmPitch');
    
    this.param.InitTransparent = this.param.InitTransparent === 'true';

    this.param.CharaList = (JsonEx.parse(this.param.CharaList) || []).map(function(json){
        const obj = JsonEx.parse(json);
        obj.CharaId    = Number(obj.CharaId);
        obj.CharaImage = String(obj.CharaImage);
        return obj;
    });

    this.parseWindowInfo(this.param, 'CharaWindow');
    this.parseWindowInfo(this.param, 'SceneWindow');
}).call(KNS_Gallery);

//=======================================================
// new Window_KnsGalleryListBase
//=======================================================
class Window_KnsGalleryListBase extends Window_Selectable{
    initialize(){
        const info = this.getWindowInfo();
        super.initialize(info.x, info.y, info.width, info.height);
        info.itemHeight = Math.floor(
            (this.itemWidth() - info.spacing) * 0.75
        ) + info.spacingVertical;
        this.select(0);
        this.activate();
    }
    maxCols(){ return this.getWindowInfo().maxCols; }
    itemHeight(){ return this.getWindowInfo().itemHeight || 0; }
    spacing(){ return 0; }
    maxItems(){ return this.knsGetAllData().length; }
    knsGetItem(index=this.index()){ return this.knsGetAllData()[index]; }
    drawItem(index){
        const img = this.knsGetImage(index);
        if (img){
            const rect = this.itemRect(index);
            const info = this.getWindowInfo();
            const wid = info.spacing;
            const hei = info.spacingVertical;
            this.contents.blt(img, 0, 0, img.width, img.height,
                rect.x + wid / 2, rect.y + hei / 2,
                rect.width - wid, rect.height - hei
            );
        }
    }
    isCurrentItemEnabled(){
        return this.knsIsEnabled(this.index());
    }

    getWindowInfo(){ return null; }
    knsGetAllData(){ return []; }
    knsGetImage(index){
        return null;
    }
    knsSelectLastGallery(finder){
        const index = this.knsGetAllData().findIndex(finder);
        this.select(index === -1 ? 0 : index);
    }
    knsIsEnabled(index){
        return true;
    }
}

//=======================================================
// new Window_KnsGalleryCharaList < Window_KnsGalleryListBase
//=======================================================
class Window_KnsGalleryCharaList extends Window_KnsGalleryListBase{
    getWindowInfo(){ return KNS_Gallery.param.CharaWindow; }
    knsGetAllData(){ return KNS_Gallery.param.CharaList; }
    knsGetImage(index){
        const item = this.knsGetItem(index);
        if (item && item.CharaImage){
            return ImageManager.loadGallery(item.CharaImage);
        }
        return super.knsGetImage(index);
    }
    knsSelectLastGallery(charaId){
        super.knsSelectLastGallery(function(item){ return charaId === item.CharaId; });
    }

    updateHelp(){
        const item = this.knsGetItem();
        if (item){ this._helpWindow.knsSetCharaId(item.CharaId); }
    }
    knsPreloadImages(){
        this.knsGetAllData().forEach(function(_, index){
            this.knsGetImage(index);
            this.select(index);
        }, this);
    }
};

//=======================================================
// new Window_KnsGallerySceneList < Window_KnsGalleryListBase
//=======================================================
class Window_KnsGallerySceneList extends Window_KnsGalleryListBase{
    initialize(){
        super.initialize();
        this._knsCharaId = 0;
        this._knsLastIndex = [];
    }

    getWindowInfo(){ return KNS_Gallery.param.SceneWindow; }
    knsGetAllData(){
        if ($csvGalleryList[this._knsCharaId]){
            return $csvGalleryList[this._knsCharaId].allData;
        }else{
            return [];
        }
    }
    knsGetImage(index){
        if (!this.knsIsEnabled(index)){
            return ImageManager.loadGallery(KNS_Gallery.param.ImgUnavailableScene);
        }
        const item = this.knsGetItem(index);
        if (item && item.imgPath){
            return ImageManager.loadGallery(item.imgPath);
        }
        return super.knsGetImage(index);
    }
    knsSelectLastGallery(sceneId){
        super.knsSelectLastGallery(function(item){ return sceneId === item.sceneId; });
    }
    knsSetCharaId(charaId){
        if (this._knsCharaId !== charaId){
            this._knsCharaId = charaId;
            this.refresh();
        }
    }
    knsIsEnabled(index){
        const item = this.knsGetItem(index);
        return item && KNS_Gallery.getProgress(item.charaId, item.sceneId) > 0;
    }
    selectLast(index){
        this.select(this._knsLastIndex[index] || 0);
    }
    saveSelectLast(index){
        this._knsLastIndex[index] = this.index();
    }
};

//=======================================================
// new Scene_KnsGallery
//=======================================================
class Scene_KnsGallery extends Scene_Base{
    prepare(backToGallery){
        this._backToGallery = backToGallery;
    }
    create(){
        super.create();
        this.createBackground();
        this.createWindowLayer();
        this.knsCreateCharaWindow();
        this.knsCreateSceneWindow();
        this._knsCharaWindow.knsPreloadImages();
    }
    createBackground(){
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadGallery(
            KNS_Gallery.param.ImgBackground
        );
        this.addChild(this._backgroundSprite);
    }

    knsCreateCharaWindow(){
        this._knsCharaWindow = new Window_KnsGalleryCharaList();
        this._knsCharaWindow.setHandler("ok",     this.knsOnCharaOk.bind(this));
        this._knsCharaWindow.setHandler("cancel", this.knsOnCharaCancel.bind(this));
        this.addWindow(this._knsCharaWindow);
    }
    knsOnCharaOk(){
        this._knsSceneWindow.activate();
        this._knsSceneWindow.selectLast(this._knsCharaWindow.index());
    }
    knsOnCharaCancel(){
        this.startFadeOut(this.slowFadeSpeed());
        SceneManager.goto(Scene_Title);
    }

    knsCreateSceneWindow(){
        this._knsSceneWindow = new Window_KnsGallerySceneList();
        this._knsSceneWindow.setHandler("ok",     this.knsOnSceneOk.bind(this));
        this._knsSceneWindow.setHandler("cancel", this.knsOnSceneCancel.bind(this));
        this._knsCharaWindow.setHelpWindow(this._knsSceneWindow);
        this.addWindow(this._knsSceneWindow);
    }
    knsOnSceneOk(){
        DataManager.setupNewGame();
        $gamePlayer.reserveTransfer(
            KNS_Gallery.param.InitMapId,
            KNS_Gallery.param.InitMapX,
            KNS_Gallery.param.InitMapY
        );
        $gamePlayer.setTransparent(KNS_Gallery.param.InitTransparent);
    
        $gameSwitches.setValue(KNS_Gallery.param.SwitchInGallery, true);
        const item = this._knsSceneWindow.knsGetItem();
        $gameVariables.setValue(KNS_Gallery.param.VarCharaId, item.charaId);
        $gameVariables.setValue(KNS_Gallery.param.VarSceneId, item.sceneId);
        $gameTemp.reserveCommonEvent(KNS_Gallery.param.CommonEventId);

        SoundManager.playLoad();
        this.fadeOutAll();
        SceneManager.goto(Scene_Map);
    }
    knsOnSceneCancel(){
        this._knsSceneWindow.saveSelectLast(this._knsCharaWindow.index());
        this._knsSceneWindow.deselect();
        this._knsCharaWindow.activate();
    }

    start(){
        super.start();
        this._knsCharaWindow.refresh();
        if (this._backToGallery){
            this._knsCharaWindow.knsSelectLastGallery($gameVariables.value(KNS_Gallery.param.VarCharaId));
            this._knsCharaWindow.deactivate();
            this._knsSceneWindow.knsSelectLastGallery($gameVariables.value(KNS_Gallery.param.VarSceneId));
        }else{
            this._knsCharaWindow.select(0);
            this._knsSceneWindow.deactivate();
            this._knsSceneWindow.deselect();
        }
        this.startFadeIn(this.slowFadeSpeed());
        if (KNS_Gallery.param.SceneBgmName){
            AudioManager.playBgm({
                name: KNS_Gallery.param.SceneBgmName,
                volume: KNS_Gallery.param.SceneBgmVolume,
                pitch: KNS_Gallery.param.SceneBgmPitch,
                pan: 0
            });
        }
    }
};

(function(){
    //=======================================================
    // alias ImageManager
    //=======================================================
    ImageManager.loadGallery = function(filename, hue) {
        return this.loadBitmap('img/Gallery_Mode/', filename, hue, true);
    };

    //=======================================================
    // alias Game_Interpreter
    //=======================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_Gallery.name){ return; }
        switch ((args[0] || "").toLowerCase()){
            case 'getprogress':{
                $gameVariables.setValue(Math.floor(args[1]), KNS_Gallery.getProgress(
                    Math.floor(args[2]), Math.floor(args[3])
                ));
                break;
            }
            case 'setprogress':{
                KNS_Gallery.setProgress(
                    Math.floor(args[1]), Math.floor(args[2]), Math.floor(args[3])
                );
                break;
            }
            case 'terminate':{
                if ($gameSwitches.value(KNS_Gallery.param.SwitchInGallery)){
                    const time = KNS_Gallery.param.terminateAudioFadeTime;
                    AudioManager.fadeOutBgm(time);
                    AudioManager.fadeOutBgs(time);
                    AudioManager.fadeOutMe(time);
                    AudioManager.stopSe();
                    SceneManager.goto(Scene_KnsGallery);
                    SceneManager.prepareNextScene(true);
                }
                break;
            }
        }
    };

    //=======================================================
    // alias Window_TitleCommand
    //=======================================================
    const _Window_TitleCommand_makeCommandList = Window_TitleCommand.prototype.makeCommandList;
    Window_TitleCommand.prototype.makeCommandList = function() {
        _Window_TitleCommand_makeCommandList.apply(this, arguments);

        const optIndex = this._list.findIndex(function(item){
            return item.symbol === 'options';
        });
        const last = this._list.slice(optIndex);
        this._list = this._list.slice(0, optIndex);

        this.addCommand(KNS_Gallery.param.TitleCommand, 'kns_gallery');
        this._list = this._list.concat(last);
    };

    //=======================================================
    // alias Scene_Title
    //=======================================================
    const _Scene_Title_createCommandWindow = Scene_Title.prototype.createCommandWindow;
    Scene_Title.prototype.createCommandWindow = function(){
        _Scene_Title_createCommandWindow.apply(this, arguments);
        this._commandWindow.setHandler('kns_gallery', this.knsCommandGallery.bind(this));
    }

    Scene_Title.prototype.knsCommandGallery = function(){
        this._commandWindow.close();
        this.fadeOutAll();
        SceneManager.push(Scene_KnsGallery);
        SceneManager.prepareNextScene(false);
    }

})();