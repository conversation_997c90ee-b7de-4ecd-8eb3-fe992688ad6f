/*:
 * @plugindesc Exports Common Event IDs and Names to a CSV file. Ideal for opening with Excel. Named "CommonEvents.csv".
 * <AUTHOR> Name
 *
 * @help This plugin exports all common event IDs and their names to a CSV file.
 * No plugin commands are necessary.
 */

var TB_Export = TB_Export || {};

TB_Export.exportCommonEvents = function() {
    var data = "ID,Name\n"; // CSV Header
    $dataCommonEvents.forEach(function(commonEvent) {
        if (commonEvent) { // Check if the common event exists
            data += commonEvent.id + "," + commonEvent.name + "\n";
        }
    });

    // Save the CSV data to a file (this part is platform-dependent and might not work in all environments)
    var fs = require('fs');
    var path = require('path');
    var filePath = path.join(path.dirname(process.mainModule.filename), 'CommonEvents.csv');
    fs.writeFile(filePath, data, function(err) {
        if (err) {
            console.error('Failed to export Common Events:', err);
        } else {
            console.log('Common Events exported successfully.');
        }
    });
};

// Export common events when the plugin is loaded
TB_Export.exportCommonEvents();
