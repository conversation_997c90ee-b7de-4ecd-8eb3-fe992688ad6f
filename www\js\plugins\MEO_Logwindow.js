/*:
    <AUTHOR>
    @plugindesc LogWindow Window

    @param Position

    @param X
    @type number
    @default 0
    @parent Position

    @param Y
    @type number
    @default 660
    @parent Position

    @param Size

    @param Width
    @type number
    @default 640
    @parent Size

    @param Height
    @type number
    @default 300
    @parent Size

    @param Style

    @param FontSize
    @type number
    @default 24
    @parent Style

    @param Margin
    @type number
    @default 9
    @parent Style

    @param WindowSkin
    @type file
    @dir img/system
    @default WindowEmpty
    @parent Style

    @param Background
    @type file
    @dir img/pictures
    @default LogWindowBackground
    @parent Style

    @param Alpha
    @type number
    @decimals 3
    @default 0.6
    @parent Style

    @param Scroll

    @param Speed
    @type number
    @decimals 3
    @default 2.750
    @parent Scroll

    @param Acceleration
    @type number
    @decimals 3
    @default 0.075
    @parent Scroll
*/

let $LogWindow,$UtilWindow_LogWindow;{PluginManager.parameters("LogWindow");let t=function(t,i,o){let n,e=[],s="",d=[],l=[],r=[/^(c)\[\d+\]/,/^i\[\d+\]/,/^\n+/,/^[ ]+/,/^([\u3000-\u303F]|[\u3040-\u309F]|[\u30A0-\u30FF]|[\uFF00-\uFFEF]|[\u4E00-\u9FAF]|[\u2605-\u2606]|[\u2190-\u2195]|\u203B)/,/^([\wäöüß]+)/,/^([\u2000-\u206F\u2E00-\u2E7F\\�'!"#$%&()*+,\-.\/:;<=>?@\[\]^_`{|}~]+)/,/^[^ |^\n]+/],h=t.length;for(let i=0;i<h;i++)for(let o=0;o<r.length;o++)if(n=r[o].exec(t)){t=t.slice(n[0].length),i+=n[0].length-1,d.push(n[0]),l.push(o-2);break}let u=0,w=!1;for(let t=0;t<d.length;t++){let o=d[t],n=l[t],r=$UtilWindow_LogWindow.contents.measureTextWidth(o);-2===n?s+=o:(-1===n&&(r=Window_Base._iconWidth),((u+=r)>i||0===n)&&(u=r,e.push(s),s="",w=!0),w?(n>=2&&(s+=o),1===n&&(u=0)):0!==n&&(s+=o)),w=!1}return e.push(s),e},i=function(t){let i,o,n=/c\[\d+\]/g;for(let e=0;e<t.length;e++){let s=0;for(o&&(t[e]=o+t[e]);null!==(i=n.exec(t[e]))&&(o=i[0],!(++s>10)););}};function LogWindow(){this.Limit=40,this.History=[],this.Width=0,this.Sliced=0,this.Visible=!0}LogWindow.prototype.Add=function(o){let n=t($UtilWindow.convertEscapeCharacters($UtilWindow.convertEscapeCharacters(o)),this.Width,32);i(n);for(const t of n)this.History.push(t);if(this.History.length>=this.Limit){let t=this.History.length-this.Limit;this.History=this.History.slice(t),this.Sliced=t}},LogWindow.prototype.Clear=function(){this.History=[]},LogWindow.prototype.Sync=function(){$UtilWindow_LogWindow&&$LogWindow_CurrentWindow&&($UtilWindow_LogWindow.standardFontSize=$LogWindow_CurrentWindow.standardFontSize,$UtilWindow_LogWindow.contents.fontSize=$LogWindow_CurrentWindow.contents.fontSize,this.Visible)},$LogWindow=new LogWindow;let o=Scene_Title.prototype.start;Scene_Title.prototype.start=function(){o.call(this),$UtilWindow_LogWindow=new Window_UtilWindow(0,0,Graphics.width,Graphics.height)}}