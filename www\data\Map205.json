{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Dイベ（従者）ルート", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 8, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 11"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 15]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(53, 4, 7)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 10, 0, 3, 1]}, {"code": 111, "indent": 2, "parameters": [4, 10, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 3]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 11, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 12, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 5]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 13, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 6]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 14, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 7]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 15, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 8]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 16, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 9]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 17, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 10]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 18, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 11]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 19, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 12]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 21, 0]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 13]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["n = $gameVariables.value(54)"]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(205, n, 1)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_no_follower\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "汎用", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "PM", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************************"]}, {"code": 118, "indent": 0, "parameters": ["抽選"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"ev_random_yari1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"ev_random_yari2\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"ev_random\")"]}, {"code": 118, "indent": 0, "parameters": ["ダイス"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 108, "indent": 1, "parameters": ["*************************************************"]}, {"code": 408, "indent": 1, "parameters": ["イベントテストモードON"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************"]}, {"code": 111, "indent": 1, "parameters": [12, "$dataMap.note.includes(\"アビサル・ウッズ\")"]}, {"code": 102, "indent": 2, "parameters": [["8"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "8"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 8, 8]}, {"code": 119, "indent": 3, "parameters": ["イベント再生"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataMap.note.includes(\"熱帯エリア\")"]}, {"code": 102, "indent": 2, "parameters": [["7"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "7"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 7]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["2", "3", "4", "5"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "2"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "3"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 3]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "4"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [3, "5"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 5]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["6"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "6"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 0, 6]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["Random"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "Random"]}, {"code": 119, "indent": 3, "parameters": ["ランダム"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*************************************************"]}, {"code": 408, "indent": 1, "parameters": ["イベントテストモードOFF"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************"]}, {"code": 118, "indent": 1, "parameters": ["ランダム"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 2, 6]}, {"code": 111, "indent": 1, "parameters": [12, "$dataMap.note.includes(\"熱帯エリア\")"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 7, 7]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataMap.note.includes(\"アビサル・ウッズ\")"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 8, 8]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["イベント再生"]}, {"code": 355, "indent": 0, "parameters": ["var ev = $gameMap.event(this._eventId).eventId();"]}, {"code": 655, "indent": 0, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(205, ev, n)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["1.司祭とPMの会話"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-9"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["2.主人公の傷"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 255], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [1, 3504, 0, 0, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-107"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 250, "indent": 1, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_202-212"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [3504, 3504, 1, 0, 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["3.生理？"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["4.<PERSON>"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 205, 3, 5, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 205, 3, 5, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-36"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 205, 3, 5, 40, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 205, 3, 5, 50, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[183]>=100)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41-43"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[183]>=100)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51-52"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["5.ロングキスグッバイ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 205, 3, 6, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 205, 3, 6, 200, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(s[1131])"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["司祭のほうを見る"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 8, 1]}, {"code": 108, "indent": 2, "parameters": ["アイテムゲット"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_110"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 6, 1]}, {"code": 126, "indent": 3, "parameters": [2, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 125, "indent": 3, "parameters": [0, 0, 5]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_111"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["何もない"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_120-121"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(s[1131])"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["キス"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [29, 29, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 16)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-203"]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 16)"]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"onmitsu\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_250-252"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_253-256"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_280-283"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [952]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["6.暑い……（火山用）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Fire2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 170], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-6"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["8.虫（アビサル・ウッズ）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 3}, {"id": 13, "name": "ダンジョンイベント：ヤリゾー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************************"]}, {"code": 118, "indent": 0, "parameters": ["抽選"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"ev_random_yari1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"ev_random_yari2\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"ev_random\")"]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモード用"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["2", "3", "4", "5"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "2"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "3"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 0, 3]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "4"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 0, 4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "5"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 0, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["6"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "6"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 0, 6]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["Random"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Random"]}, {"code": 119, "indent": 2, "parameters": ["ダイス"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["ダイス"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 2, 6]}, {"code": 111, "indent": 1, "parameters": [12, "$dataMap.note.includes(\"アビサル・ウッズ\")"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 2, 7]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["イベント　最近旦那とはどうですか？は"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾーに寝取らせた経験がある場合のみ進行"]}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 0, 2]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 2, 0]}, {"code": 119, "indent": 3, "parameters": ["ダイス"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント再生"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["イベント再生"]}, {"code": 355, "indent": 0, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(205, 13, n)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["2.デカケツ奥さん"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 117, "indent": 0, "parameters": [61]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモードの場合選択表示"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"relationship_current\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"relationship1\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(63, \"relationship2\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(64, \"relationship3\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(65, \"relationship4\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(66, \"relationship5\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\v[64]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[65]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[66]"]}, {"code": 119, "indent": 2, "parameters": ["関係性５"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性５"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10001"]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10002"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10003"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["関係性１"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11001"]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11002-11004"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["2.旦那とはどう？"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map42_ev2_p1_d2_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map42_ev2_p1_d2_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20001"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map42_ev2_p1_d2_2a\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_map42_ev2_p1_d2_2b\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_20002"]}, {"code": 119, "indent": 2, "parameters": ["イベント２終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -1"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["イベントテストモードの場合選択表示"]}, {"code": 111, "indent": 1, "parameters": [0, 36, 0]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"relationship_current\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"relationship1\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(63, \"relationship2\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(64, \"relationship3\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(65, \"relationship4\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(66, \"relationship5\")"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 3, "parameters": ["関係性１"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[63]"]}, {"code": 119, "indent": 3, "parameters": ["関係性１"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [3, "\\v[64]"]}, {"code": 119, "indent": 3, "parameters": ["関係性１"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[65]"]}, {"code": 119, "indent": 3, "parameters": ["関係性１"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[66]"]}, {"code": 119, "indent": 3, "parameters": ["関係性５"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["処理開始"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 5, 1]}, {"code": 118, "indent": 2, "parameters": ["関係性５"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21001"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21002"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21003"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [29, 29, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 12]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [213]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21003-21007"]}, {"code": 245, "indent": 2, "parameters": [{"name": "teman_crazy", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 2, "parameters": [213]}, {"code": 231, "indent": 2, "parameters": [81, "cutin_pussy1_hand", 0, 1, 44, 45, 50, 50, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21008-21015"]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 2, "parameters": [161, 161, 0, 0, 6]}, {"code": 122, "indent": 2, "parameters": [162, 162, 0, 0, -1]}, {"code": 122, "indent": 2, "parameters": [171, 171, 0, 4, "`[手マン判定(${$gameActors.actor(21).name()})]`"]}, {"code": 117, "indent": 2, "parameters": [13]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21016"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 2, "parameters": [212]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 250, "indent": 2, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [25, 25, 0, 0, 2]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 117, "indent": 2, "parameters": [73]}, {"code": 231, "indent": 2, "parameters": [16, "stand-heroine-a<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-squirt", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21017-21020"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [29, 29, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [23, 27, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["関係性１"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_22001-22012"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 118, "indent": 1, "parameters": ["イベント２終了"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20003"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20004"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20005"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["2.ヤリゾースメル"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモードの場合選択表示"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"relationship_current\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"relationship1\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(63, \"relationship2\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(64, \"relationship3\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(65, \"relationship4\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(66, \"relationship5\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 119, "indent": 2, "parameters": ["関係性２"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\v[64]"]}, {"code": 119, "indent": 2, "parameters": ["関係性３"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[65]"]}, {"code": 119, "indent": 2, "parameters": ["関係性４"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[66]"]}, {"code": 119, "indent": 2, "parameters": ["関係性５"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["処理"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 1, 2]}, {"code": 118, "indent": 1, "parameters": ["関係性１"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-107"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性２"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-208"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性３"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-208"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性４"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_401-407"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性５"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_501-505"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [-1, 2, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_507"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_509-512"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["5.司祭こける"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 111, "indent": 0, "parameters": [0, 32, 1]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, true]}, {"code": 117, "indent": 0, "parameters": [62]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモードの場合選択表示"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"relationship_current\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"relationship1\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(63, \"relationship2\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(64, \"relationship3\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(65, \"relationship4\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(66, \"relationship5\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 119, "indent": 2, "parameters": ["関係性２"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\v[64]"]}, {"code": 119, "indent": 2, "parameters": ["関係性３"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[65]"]}, {"code": 119, "indent": 2, "parameters": ["関係性４"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[66]"]}, {"code": 119, "indent": 2, "parameters": ["関係性５"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性１以下"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 1, 2]}, {"code": 118, "indent": 1, "parameters": ["関係性１"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_103-107"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性２"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-204"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性３"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性３"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300-304"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [62]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 205, 13, 5, 305, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_306-308"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 30]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性４"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性４"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_400-405"]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_410-412"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 50]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性５"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性５"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_500-505"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_510-513"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [62]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_520"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_521"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_530-532"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 6]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 13, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_540-542"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 100]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_900-901"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["6.司祭のケツを揉む"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 111, "indent": 0, "parameters": [0, 32, 1]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"relationship_current\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"relationship1\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(63, \"relationship2\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(64, \"relationship3\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(65, \"relationship4\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(66, \"relationship5\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 119, "indent": 2, "parameters": ["関係性１"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\v[64]"]}, {"code": 119, "indent": 2, "parameters": ["関係性３"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[65]"]}, {"code": 119, "indent": 2, "parameters": ["関係性４"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[66]"]}, {"code": 119, "indent": 2, "parameters": ["関係性５"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル２以下（デカケツ奥さんのイベントを呼び出す）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 2]}, {"code": 118, "indent": 1, "parameters": ["関係性１"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(205, 13, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル３"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性３"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 121, "indent": 1, "parameters": [47, 47, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-33"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル４"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性４"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 121, "indent": 1, "parameters": [47, 47, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41-43"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル５"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["関係性５"]}, {"code": 121, "indent": 1, "parameters": [47, 47, 0]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51-53"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["5.司祭こける"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 3}]}