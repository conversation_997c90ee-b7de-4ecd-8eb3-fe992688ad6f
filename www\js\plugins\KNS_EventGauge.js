/*:
 * <AUTHOR> 
 * @plugindesc ver.1.0.0 イベント上にゲージを表示します
 * @target MV
 * 
 * @param GaugeTypes
 * @type struct<gaugeType>[]
 * @default ["{\"name\":\"sampleGauge1\",\"unit\":\"%\",\"backgroundColor\":\"#202041\",\"color1\":\"#e07f40\",\"color2\":\"#f0c040\",\"showMax\":\"false\",\"width\":\"96\",\"height\":\"10\",\"padding\":\"6\",\"fontSize\":\"22\",\"maxDigit\":\"3\"}","{\"name\":\"sampleGauge2\",\"unit\":\"%\",\"backgroundColor\":\"#202041\",\"color1\":\"#40e07f\",\"color2\":\"#40f0c0\",\"showMax\":\"true\",\"width\":\"128\",\"height\":\"16\",\"padding\":\"6\",\"fontSize\":\"22\",\"maxDigit\":\"4\"}"]
 * 
 * @help
 * ※このプラグインはKNS_EquipParameter以下に設置してください。
 * 
 * ■プラグインコマンド
 * KNS_EventGauge show イベントID|"this"|"player" 値変数ID 最大値変数ID "top"|"bottom" ゲージ名
 * 　指定したイベントに「ゲージ名」のゲージを表示し値の変数を指定します。
 * 　イベントIDに"this"を指定すると実行中のイベントが対象になります。
 * 例）
 * 　KNS_EventGauge show 3 1 2 top sampleGauge1
 * 　　イベント3番に変数1, 2の値でsampleGauge1のゲージを表示します。
 * 
 * 　KNS_EventGauge show this 3 4 bottom sampleGauge2
 * 　　実行中のイベントに変数3, 4の値でsampleGauge2のゲージを表示します。
 * 
 * 
 * KNS_EventGauge update イベントID|"this"|"player"|"all"
 * 　指定したイベントのゲージを更新します。
 * 　イベントIDに"all"を指定するとマップ上のすべてのイベントが対象になります。
 * 
 * 例）
 * 　KNS_EventGauge update all
 * 　　すべてのイベントのゲージを更新します。
 * 
 * KNS_EventGauge hide イベントID|"this"|"player"|"all"
 * 　指定したイベントのゲージを削除します。
 * 
 */
/*~struct~gaugeType:
 * @param name
 * @text ゲージ名
 * @type string
 * @default gauge1
 * 
 * @param unit
 * @text 数値単位
 * @type string
 * @default %
 * 
 * @param backgroundColor
 * @text ゲージ背景色
 * @type string
 * @default #202041
 * 
 * @param color1
 * @text ゲージ色１
 * @type string
 * @default #e07f40
 * 
 * @param color2
 * @text ゲージ色２
 * @type string
 * @default #f0c040
 * 
 * @param showMax
 * @text 最大値を表示
 * @type boolean
 * @default false
 * 
 * @param width
 * @text ゲージ幅(px)
 * @type number
 * @default 96
 * 
 * @param height
 * @text ゲージ高さ(px)
 * @type number
 * @default 10
 * 
 * @param padding
 * @text キャラとの間隔(px)
 * @type number
 * @min -65536
 * @max 65535
 * @default 6
 * 
 * @param fontSize
 * @text フォントサイズ(px)
 * @type number
 * @default 22
 * 
 * @param maxDigit
 * @text 最大桁数
 * @type number
 * @default 3
 */

const KNS_EventGauge = {
    name: 'KNS_EventGauge',
    param: null,
    parseEvents: function(eventId, key){
        switch (key){
            case 'this':   return [$gameMap.event(eventId)];
            case 'player': return [$gamePlayer];
            case 'all':    return $gameMap.events().concat($gamePlayer);
            default:       return [$gameMap.event(this.parseVariable(key))];
        }
    },
    parseVariable: function(arg){
        if (/\\v\[(\d+)\]/.test(arg)){
            return $gameVariables.value(Number(RegExp.$1));
        }
        return Number(arg);
    },
    findGaugeType: function(name){
        return this.param.GaugeTypes.find(function(gaugeType){
            return gaugeType.name === name;
        });
    }
};
(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.GaugeTypes = JsonEx.parse(this.param.GaugeTypes).map(function(json){
        const obj = JsonEx.parse(json);
        obj.name = String(obj.name);
        obj.unit = String(obj.unit);
        obj.backgroundColor = String(obj.backgroundColor);
        obj.color1 = String(obj.color1);
        obj.color2 = String(obj.color2);
        obj.showMax = obj.showMax === 'true';
        obj.width = Number(obj.width);
        obj.height = Number(obj.height);
        obj.padding = Number(obj.padding);
        obj.fontSize = Number(obj.fontSize);
        obj.maxDigit = Number(obj.maxDigit);
        return obj;
    });

    //=============================================
    // alias Game_Interpreter
    //=============================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command === KNS_EventGauge.name) {
            switch (args[0]){
                case 'show':{
                    let [v1, v2, pos, gaugeType] = args.slice(2, args.length);
                    v1 = KNS_EventGauge.parseVariable(v1);
                    v2 = KNS_EventGauge.parseVariable(v2);

                    KNS_EventGauge.parseEvents(this._eventId, args[1]).forEach(function(ev){
                        ev.knsShowEventGauge(v1, v2, pos, gaugeType);
                    });
                    break;
                }
                case 'update':{
                    KNS_EventGauge.parseEvents(this._eventId, args[1]).forEach(function(ev){
                        ev.knsUpdateEventGauge(true);
                    });
                    break;
                }
                case 'hide':{
                    KNS_EventGauge.parseEvents(this._eventId, args[1]).forEach(function(ev){
                        ev.knsHideEventGauge();
                    });
                    break;
                }
            }
        }
    }


    //=============================================
    // alias Game_Character
    //=============================================
    Game_Character.prototype.knsGetEventGauge = function(){
        return this._knsEventGauge || null;
    }

    Game_Character.prototype.knsShowEventGauge = function(v1, v2, pos, gaugeType){
        this._knsEventGauge = { v1, v2, pos, gaugeType };
        this.knsUpdateEventGauge(true);
    }

    Game_Character.prototype.knsHideEventGauge = function(){
        this._knsEventGauge = null;
        this.knsUpdateEventGauge(true);
    }

    Game_Character.prototype.knsUpdateEventGauge = function(bool){
        this._knsEventGaugeRefresh = bool;
    }

    //=============================================
    // alias Game_Event
    //=============================================
    const _Game_Event_erase = Game_Event.prototype.erase;
    Game_Event.prototype.erase = function(){
        _Game_Event_erase.apply(this, arguments);
        this.knsHideEventGauge();
    }

    //=============================================
    // alias Sprite_Character
    //=============================================
    const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;
    Sprite_Character.prototype.initialize = function(){
        _Sprite_Character_initialize.apply(this, arguments);
        this._knsEventGauge = new KNS_EventGauge.Sprite_KnsEventGauge(this._character, this);
    }

    const _Sprite_Character_update = Sprite_Character.prototype.update;
    Sprite_Character.prototype.update = function(){
        _Sprite_Character_update.apply(this, arguments);
        if (this.parent && !this._knsEventGauge.parent){
            this.parent.parent.addChild(this._knsEventGauge);
        }
    }
    
    //=============================================
    // new KNS_EventGauge.Sprite_KnsEventGauge
    //=============================================
    this.Sprite_KnsEventGauge = class extends Sprite{
        initialize(character, parentSprite){
            super.initialize(null);
            this.anchor.set(0.5, 0);
            this._knsCharacter = character;
            this._knsParent = parentSprite;
            this._knsGaugeInfo = null;
            this.knsUpdateGauge(true);
        }
        update(){
            super.update();
            this.knsUpdateGauge(false);
        }
        knsUpdateGauge(forced){
            if (this._knsCharacter){
                const gauge = this._knsCharacter.knsGetEventGauge();
                if (forced || this._knsCharacter._knsEventGaugeRefresh){
                    this.knsRefreshGauge(gauge);
                    this._knsCharacter._knsEventGaugeRefresh = false;
                }
                if (this._knsGaugeInfo){
                    let y = this._knsCharacter.screenY();
                    if (gauge.pos === 'top'){
                        y += -this._knsParent.height - this.bitmap.height - this._knsGaugeInfo.padding;
                    }else{
                        y += this._knsGaugeInfo.padding;
                    }
                    this.position.set(this._knsCharacter.screenX(), y);
                }
            }
        }
        knsRefreshGauge(gauge){
            if (gauge){
                const gaugeInfo = KNS_EventGauge.findGaugeType(gauge.gaugeType);
                if (!gaugeInfo){
                    console.error(`[${KNS_EventGauge.name}]存在しないゲージ(${gauge})`);
                    return;
                }
                if (!this.bitmap || this._knsGaugeInfo !== gaugeInfo){
                    this._knsGaugeInfo = gaugeInfo;
                    this.bitmap = new Bitmap(gaugeInfo.width, gaugeInfo.height + 24);
                }else{
                    this.bitmap.clear();
                }
                this.bitmap.fillRect(
                    0, this.bitmap.height - gaugeInfo.height,
                    gaugeInfo.width, gaugeInfo.height,
                    gaugeInfo.backgroundColor
                );
                const v1 = $gameVariables.value(gauge.v1);
                const v2 = $gameVariables.value(gauge.v2);
                const rate = v2 === 0 ? 0 : Math.min(1, v1 / v2);
                this.bitmap.gradientFillRect(
                    0, this.bitmap.height - gaugeInfo.height,
                    gaugeInfo.width * rate, gaugeInfo.height,
                    gaugeInfo.color1, gaugeInfo.color2, false
                );

                const max = gaugeInfo.maxDigit;
                const spacePad = function(num){
                    let space = "";
                    const sn = String(num);
                    if (max > sn.length){
                        space = " ".repeat(max - sn.length)
                    }
                    return space + num;
                };
                this.bitmap.fontSize = gaugeInfo.fontSize;
                this.bitmap.drawText(
                    gaugeInfo.showMax ?
                        `${spacePad(v1)}/${spacePad(v2)}${gaugeInfo.unit}` :
                        `${spacePad(v1)}${gaugeInfo.unit}`,
                    0, 0, this.bitmap.width - 3, this.bitmap.height, 'right'
                );
            }else{
                if (this.bitmap){
                    this._knsGaugeInfo = null;
                    this.bitmap = null;
                }
            }
        }
    };
}).call(KNS_EventGauge);
