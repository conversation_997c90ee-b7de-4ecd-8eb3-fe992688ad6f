{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "Night", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 25, "note": "dark_night\nサブ拠点の宿屋", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 25, "data": [7428, 7452, 7452, 7452, 7452, 7436, 7452, 7452, 7452, 7452, 7436, 7452, 7452, 7452, 7452, 7449, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7810, 7810, 7810, 7810, 7456, 7810, 7810, 7810, 7810, 7456, 7810, 7810, 7810, 7810, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7816, 7816, 7816, 7816, 7456, 7816, 7816, 7816, 7816, 7456, 7816, 7816, 7816, 7816, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1549, 1548, 1548, 7456, 1549, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1549, 1548, 1548, 7456, 1549, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1548, 1549, 1548, 7456, 1549, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7430, 7469, 1549, 7467, 7457, 7455, 7469, 1549, 7467, 7457, 7455, 7469, 1549, 7467, 7457, 7451, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7814, 1549, 7811, 7810, 7810, 7814, 1549, 7811, 7810, 7810, 7814, 1549, 7811, 7810, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7820, 1549, 7817, 7816, 7816, 7820, 1549, 7817, 7816, 7816, 7820, 1549, 7817, 7816, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1548, 1549, 1548, 1549, 1548, 1549, 1548, 1549, 1548, 1549, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7430, 7469, 1548, 7467, 7457, 7447, 7469, 1548, 7467, 7457, 7447, 7469, 1548, 7467, 7457, 7451, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7814, 1548, 7811, 7810, 7456, 7814, 1549, 7811, 7810, 7456, 7814, 1548, 7811, 7810, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7820, 1548, 7817, 7816, 7456, 7820, 1549, 7817, 7816, 7456, 7820, 1548, 7817, 7816, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1548, 1549, 1548, 7456, 1548, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1548, 1549, 1548, 7456, 1548, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1549, 1548, 1549, 1548, 7456, 1548, 1549, 1548, 1549, 7456, 1548, 1548, 1549, 1548, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7454, 7457, 7457, 7457, 7457, 7455, 7457, 7457, 7457, 7457, 7455, 7457, 7457, 7457, 7457, 7463, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 800, 0, 0, 0, 0, 800, 0, 0, 0, 0, 800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 808, 0, 0, 0, 0, 808, 0, 5, 0, 5, 808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 0, 0, 118, 119, 0, 13, 0, 13, 0, 116, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 126, 127, 0, 0, 0, 0, 0, 124, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 116, 117, 0, 13, 0, 116, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 0, 0, 0, 0, 124, 125, 0, 0, 0, 124, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 21, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イベント　（ルート）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [113, 113, 1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$after_event = 0"]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーのイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["司祭　寝取られ"]}, {"code": 111, "indent": 0, "parameters": [12, "$inn_rent_sex == 2"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 25, 1)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$inn_rent_sex = 0"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["寝取らせ"]}, {"code": 111, "indent": 0, "parameters": [12, "$inn_rent_sex == 1"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 24, 1)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 10, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 26, 1)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$inn_rent_sex = 0"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["Lvl3　解放イベント"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 2, 0]}, {"code": 111, "indent": 3, "parameters": [1, 214, 0, 120, 1]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 21, 2)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["Lvl4　解放イベント"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 3, 0]}, {"code": 111, "indent": 3, "parameters": [1, 214, 0, 280, 1]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 22, 2)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["二人がめっちゃセックスして従者がウザがる"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 1, 2)"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭の興奮が１００の時"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [1, 186, 0, 100, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 1, 4)"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["司祭の興奮が１００じゃない場合は絆レベルに応じたイベント"]}, {"code": 408, "indent": 2, "parameters": ["******************************************************"]}, {"code": 108, "indent": 2, "parameters": ["***************************************"]}, {"code": 408, "indent": 2, "parameters": ["イベントテストモード"]}, {"code": 408, "indent": 2, "parameters": ["***************************************"]}, {"code": 111, "indent": 2, "parameters": [0, 36, 0]}, {"code": 117, "indent": 3, "parameters": [993]}, {"code": 102, "indent": 3, "parameters": [["Lvl 1", "Lvl 2", "Lvl 3", "Lvl 4"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "Lvl 1"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 19, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "Lvl 2"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 20, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [2, "Lvl 3"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 21, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [3, "Lvl 4"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 22, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 102, "indent": 3, "parameters": [["Lvl 5"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "Lvl 5"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 23, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 102, "indent": 3, "parameters": [["\\v[80]"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[80]"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["***************************************"]}, {"code": 408, "indent": 3, "parameters": ["イベントテストモードここまで"]}, {"code": 408, "indent": 3, "parameters": ["***************************************"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["***************************************"]}, {"code": 408, "indent": 2, "parameters": ["イベントテストモードじゃない場合は絆レベルに応じたイベント呼び出し"]}, {"code": 408, "indent": 2, "parameters": ["***************************************"]}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(204)"]}, {"code": 655, "indent": 2, "parameters": ["num += 18"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(0, num, 1)"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PMイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 18, 1)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["吸血姫がパーティにる"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 19, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 29, 1)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ１９６　イベント１　ページ１　終了処理\")"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$inn_event = 0"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 40, "indent": null}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 231, "indent": 0, "parameters": [1, "BG-tavern1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["$map_temp = $gameVariables.value(41)"]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [323]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(41,$map_temp)"]}, {"code": 121, "indent": 0, "parameters": [19, 19, 1]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 234, "indent": 1, "parameters": [1, [0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["翌朝に部屋で起こるイベント"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ"]}, {"code": 108, "indent": 0, "parameters": ["司祭"]}, {"code": 111, "indent": 0, "parameters": [12, "$after_event == 1"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["$after_event = 0"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 24, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": ["寝取られ"]}, {"code": 108, "indent": 0, "parameters": ["司祭"]}, {"code": 111, "indent": 0, "parameters": [12, "$after_event == 2"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["$after_event = 0"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 25, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["宿屋のフロントへ移動"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 201, "indent": 0, "parameters": [1, 41, 42, 43, 2, 0]}, {"code": 231, "indent": 0, "parameters": [1, "BG-tavern1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["睡眠中にヤリゾーになんかされたら起きた時イベント"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 111, "indent": 0, "parameters": [0, 113, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 1, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$settlement_menu == 1"]}, {"code": 117, "indent": 1, "parameters": [311]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イベント"]}, {"code": 408, "indent": 0, "parameters": ["（二人がめっちゃセックスしてて従者がウザがる）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 39, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 41, "parameters": ["$pixel_animation_blowjob_prot_n_pri", 0], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$pixel_animation_blowjob_prot_n_pri", 0], "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 204, "indent": 0, "parameters": [6, 4, 4]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イベント"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾーがセクハラしにくる）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 111, "indent": 0, "parameters": [1, 862, 0, 1, 1]}, {"code": 117, "indent": 1, "parameters": [995]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 26, 0]}, {"code": 121, "indent": 1, "parameters": [26, 26, 1]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 113, 1]}, {"code": 108, "indent": 1, "parameters": ["*****************"]}, {"code": 408, "indent": 1, "parameters": ["就寝前"]}, {"code": 408, "indent": 1, "parameters": ["*****************"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 1, "parameters": [467, 467, 0, 1, 1684]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 213, "indent": 1, "parameters": [3, 10, false]}, {"code": 213, "indent": 1, "parameters": [-1, 10, true]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 213, "indent": 1, "parameters": [4, 8, true]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 205, "indent": 2, "parameters": [4, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [11, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [5]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [5]}, {"code": 37, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [4, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [10, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [5]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [5]}, {"code": 37, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [4, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 213, "indent": 2, "parameters": [4, 8, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-4"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["イラマチオ開始　ちんぽ段階１"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-18"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["更に奥の方までちんぽ突っ込む　ちんぽ段階２"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "insert1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽ引き抜こうとする　段階１のとこまで抜ける"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "insert1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_34-36"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※ちんぽ引き抜こうとして亀頭部分吸い付かれる　１"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 10]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_38-41"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_42-50"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※奥に入っていく　２"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 355, "indent": 1, "parameters": ["$xray = 1"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-76"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※最奥まで入る　３"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 355, "indent": 1, "parameters": ["$xray = 1"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_78-82"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※射精　司祭頬をふくらんで白目　鼻から精子溢れる"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["$xray = 0"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-102"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽ抜く　ザーゲロ"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["$xray = 0"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 4]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_104-107"]}, {"code": 117, "indent": 1, "parameters": [212]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 404]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 544]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 2, "parameters": [113, 113, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************"]}, {"code": 408, "indent": 1, "parameters": ["就寝後"]}, {"code": 408, "indent": 1, "parameters": ["*****************"]}, {"code": 118, "indent": 1, "parameters": ["起床時イベント"]}, {"code": 231, "indent": 1, "parameters": [1, "BG-tavern1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301-306"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 355, "indent": 1, "parameters": ["$ev_yarizo_inn = 0"]}, {"code": 121, "indent": 1, "parameters": [113, 113, 1]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [862, 862, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭は見た　（興奮１００で王都以外の宿屋泊まる）　寝取られオン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["起床"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭　廊下に出る"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_32"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_33"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["部屋前まで移動"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-45"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性レベルが１以下"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 1, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-101"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性レベルが2－3"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 1]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 3, 2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_200-204"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー友好値　＋１"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["終了"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　関係性レベル５"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　関係性レベル４"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300-304"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾー友好値　＋5"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了 この後セックスに続いてもええかも"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "階段", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 9}, {"id": 3, "name": "司祭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 3}, {"id": 4, "name": "従者", "note": "", "pages": [{"conditions": {"actorId": 21, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 10, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker_sleep2", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 11, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$pol_naked", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 12, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$pol_naked", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 19, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 3}, {"id": 5, "name": "客１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 3}, {"id": 6, "name": "客２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 13}, {"id": 7, "name": "客３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 13}, {"id": 8, "name": "客４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 13}, {"id": 9, "name": "客５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 13}, {"id": 10, "name": "ドア　主人公たちの部屋", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 8}, {"id": 11, "name": "ドア　ヤリゾーの部屋", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 8}, {"id": 12, "name": "ド<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 8}, {"id": 13, "name": "ドア 左下　ダブル", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 10}, {"id": 14, "name": "ド<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 10}, {"id": 15, "name": "ド<PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 10}, {"id": 16, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!_fire", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 8}, {"id": 17, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!_fire", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 8}, {"id": 18, "name": "PMイベント", "note": "", "pages": [{"conditions": {"actorId": 21, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 18, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 18, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["従者の部屋へ行く"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 18, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["再び眠る"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 21, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 196, 7, 9, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [2]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [2]}, {"code": 19, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [2]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [2]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィック表示　PM　足むき出し"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 17)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 18, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 18, 2, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["布団をかけてあげる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["足コキ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21-22"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["差分１　足にちんぽはさみ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 17)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu1_short", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-41"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["差分２　加速"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-59"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精１"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 17)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-63"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["エロ耐久テスト"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["成功　射精２"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 17)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-74"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["足に精子塗り込み"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_80-82"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 24}, {"id": 19, "name": "ヤリゾーイベントLvl1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 1, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 0}, {"id": 20, "name": "ヤリゾーイベントLvl2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 1, 3)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 1}, {"id": 21, "name": "ヤリゾーイベントLvl3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["廊下で手マンからのセックス"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 21, 3)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["Lvl3解放イベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 9, 2]}, {"code": 203, "indent": 0, "parameters": [4, 0, 7, 9, 2]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["開始"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [3, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-29"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーの部屋へ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [3, 0, 7, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 7, 3, 2]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-64"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [3, 1, true]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["立ち手マンシーン"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "DBM_Underwater_Temple", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-106"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["手マン　手ブレ開始"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-123"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["メス我慢汁があふれる"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-157"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["絶叫アヘ顔"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180-185"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190-201"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン　ちんぽを腹の上にのせる"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_300-311"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ちんぽインサート"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_321-336"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フラッシュ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_350-351"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ１"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_360-380"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ２"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_400-411"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_420"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_421-424"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン終了　フェードアウトイン"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_430-438"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_450"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_460-462"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [3, 0, 4, 3, 4]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_470-475"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 80, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー絆レベル上昇処理"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 204]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["Lvl3 眠れない夜　廊下で手マンからのセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 9, 2]}, {"code": 203, "indent": 0, "parameters": [4, 0, 7, 9, 2]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["開始"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭廊下に出る"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーも出て来る"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [3, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-23"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["むわＳＥ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 212, "indent": 0, "parameters": [3, 31, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-33"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["立ち絵　ヤリゾーがおまんこ嗅ぎ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン　手マン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 255]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_crazy", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting5", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-67"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン　セックス開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 255]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 80]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_71-77"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-84"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーちんぽ抜く"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-95"]}, {"code": 108, "indent": 0, "parameters": ["！処理どうするか未定なので一旦終了まで飛ばす"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["翌朝"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 21, 3, 110, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 196, 21, 3, 120, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-102"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_111"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_121"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー出てくる"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 21, 3, 140, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_131"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_141-142"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 80, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー絆レベル上昇処理"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 2}, {"id": 22, "name": "ヤリゾーイベントLvl4", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取られ　ヤリゾーLvl4解放イベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 196"]}, {"code": 655, "indent": 0, "parameters": ["$event = 22"]}, {"code": 655, "indent": 0, "parameters": ["$page = 2"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※導入　ヤリゾーが主人公と司祭の部屋に入ってくる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 3, 3, 2]}, {"code": 203, "indent": 0, "parameters": [4, 0, 2, 9, 8]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "parameters": ["$heroine_sleep", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine_sleep", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["$prot_sleep", 0], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$prot_sleep", 0], "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": 0}, {"code": 19, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※ヤリゾーがちんぽを出して司祭の顔の前でシコり始める"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-16"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17-19"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_21-35"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_36-37"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_38"]}, {"code": 108, "indent": 0, "parameters": ["※グラフィック　主人公が寝てる横でセックス"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン１"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 255]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 80]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-50"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,1)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-73"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-88"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※朝"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [3, 0, 7, 3, 2]}, {"code": 203, "indent": 0, "parameters": [4, 0, 7, 4, 8]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "parameters": ["$heroine_sleep", 0], "indent": null}, {"code": 19, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine_sleep", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 33, "indent": null}, {"code": 41, "parameters": ["$pixel_animation_doggyossan", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$pixel_animation_doggyossan", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "bed1", "volume": 5, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※部屋に司祭がいない。　主人公移動ルート　←wait5右wait30フキダシ？"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 18, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※ヤリゾーの部屋をノックすると慌てて隠れる司祭"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "bed1", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [3, 1, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 40, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 37, "indent": null}, {"code": 41, "parameters": ["$heroine_naked2", 0], "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 14, "parameters": [0, -1], "indent": null}, {"code": 14, "parameters": [0, 1], "indent": 0}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 44, "parameters": [{"name": "Open4", "volume": 20, "pitch": 100, "pan": 0}], "indent": null}, {"code": 39, "indent": null}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine_naked2", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open4", "volume": 20, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": 0}, {"code": 19, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※部屋に入るも司祭は居ない"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※部屋の扉が開く"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-113"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※部屋の扉が閉まる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 15, "parameters": [3], "indent": 0}, {"code": 18, "indent": 0}, {"code": 15, "parameters": [3], "indent": 0}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "parameters": [3], "indent": 0}, {"code": 16, "indent": null}, {"code": 44, "parameters": [{"name": "Open4", "volume": 90, "pitch": 100, "pan": 0}], "indent": 0}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open4", "volume": 90, "pitch": 100, "pan": 0}], "indent": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 1, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Raise3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 204]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 3}, {"id": 23, "name": "ヤリゾーイベントLvl5", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭　廊下に出る"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["部屋前まで移動"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [3, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["首絞めっクス"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 23, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["首絞めっクス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["..."]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィックイニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_51-55"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_61-65"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-104"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 4}, {"id": 24, "name": "イン・キーパーとのセックス 寝取らせ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーとのセックス　司祭編　寝取らせ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 2, 3, 10]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 2, 3, 10]}, {"code": 122, "indent": 0, "parameters": [464, 464, 0, 4, "$gameVariables.value(465)+$gameVariables.value(466)"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "indent": null, "parameters": ["vxchara02ab", 7]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["vxchara02ab", 7]}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 3, 14, 4]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 14, 6]}, {"code": 201, "indent": 0, "parameters": [0, 196, 2, 9, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 24, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 24, 1, 80, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["覗く"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 108, "indent": 1, "parameters": ["グラフィック表示"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bed1", "pan": 0, "pitch": 80, "volume": 65}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-16"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精処理"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_17"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_18-21"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30"]}, {"code": 119, "indent": 1, "parameters": ["チェックアウト"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["立ち去る"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "bed1", "pan": 0, "pitch": 80, "volume": 65}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_82"]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_83"]}, {"code": 119, "indent": 1, "parameters": ["チェックアウト"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["チェックアウト"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$after_event = 1"]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [319]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーとのセックス　司祭編　寝取らせ　チェックアウト時間"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["増えた回数の処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [980]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーンイニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 234, "indent": 1, "parameters": [1, [0, 0, 0, 0], 1, false]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 196, 2, 5, 2, 0]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "indent": null, "parameters": ["", 7]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 7]}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 7, 8]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 24, 2, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 24, 2, 300, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["回数報告"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-203"]}, {"code": 119, "indent": 1, "parameters": ["報告後"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["出発する"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ報告後の選択肢"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["報告後"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 24, 2, 400, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 24, 2, 500, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["報告後セックス"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_401"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(141, 5, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["出発"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_501"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 24}, {"id": 25, "name": "イン・キーパーとのセックス 寝取られ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーとのセックス　司祭編　寝取られ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 2, 3, 10]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 2, 3, 10]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(465)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(466)"]}, {"code": 655, "indent": 0, "parameters": ["num3 = num1 + num2"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(54,num3)"]}, {"code": 122, "indent": 0, "parameters": [464, 464, 0, 1, 54]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "indent": null, "parameters": ["vxchara02ab", 7]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["vxchara02ab", 7]}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 3, 14, 4]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 14, 6]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 213, "indent": 0, "parameters": [-1, 10, true]}, {"code": 204, "indent": 0, "parameters": [2, 10, 4]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [3, 8, true]}, {"code": 111, "indent": 0, "parameters": [0, 35, 0]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["エロシーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["グラフィック表示"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bed1", "pan": 0, "pitch": 80, "volume": 65}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["男半透明に"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_14-15"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スパンキングＳＥ"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-22"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["エロシーン　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["グラフィック表示"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 4, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 80, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-58"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 80, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$after_event = 2"]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [319]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーとのセックス　司祭編　寝取られ　チェックアウト時間"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["増えた回数の処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [980]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーンイニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 234, "indent": 1, "parameters": [1, [0, 0, 0, 0], 1, false]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 196, 2, 7, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 2, 11, 8]}, {"code": 203, "indent": 0, "parameters": [6, 0, 3, 9, 6]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 213, "indent": 0, "parameters": [3, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 25, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 25, 2, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(109, 12, 3)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 111, "indent": 1, "parameters": [0, 35, 1]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(109, 12, 3)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 24}, {"id": 26, "name": "イン・キーパーとのセックス　ＰＭ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イン・キーパーとのセックス　ＰＭ編"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 1567]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 22, "y": 24}, {"id": 27, "name": "バーバリアンイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 24}, {"id": 28, "name": "キャットシーイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 24}, {"id": 29, "name": "吸血姫イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 29, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 29, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["従者の部屋へ行く"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 29, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["再び眠る"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["絆レベル２ オナニー覗く"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 196, 7, 9, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu2", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 29, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 29, 2, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["覗く"]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [2]}, {"code": 18, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [2]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["覗かない"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["オナニーシーン"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_crazy", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 7)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_103-104"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 196, 29, 2, 110, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 196, 29, 2, 120, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["気付かれる前に～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_111-112"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["村人に～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_121"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックス開始シーン"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 19, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-206"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン騎乗位"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 3"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bed2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_220-222"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_223-225"]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_giryou\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 108, "indent": 1, "parameters": ["成功"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300-301"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_310-311"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_350-351"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 254]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["失敗"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_400-403"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_404"]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 14, 8)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_410"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["共通"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_500"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_ALL_STOP"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 24}]}