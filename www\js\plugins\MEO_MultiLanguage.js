let $dataCSV,$lastCommonEventId,$lastRemovedCommonEventId,$lastReservedCommonEventId,$currentInterpreter,$flagReservedCommonCall,$commandPlaySE,$currentCommonEventIds=[],$languageVarIndex=1;function LoadFileUTF8Escaped(e){return $Node.fs.readFileSync(e,"utf8").replace(/\r/g,"")}function Access(e,t){"string"==typeof t&&(t=t.split(/[\s./]/));let a=e;for(let e=0;e<t.length;e++){if(null==a)return null;a=a[t[e]]}return null==a?null:a}function GetType(e){return e.__proto__.constructor.name}function VarFromCSVEvents(e,t,a,o,s){let n=$gameVariables._data[1]||0;return $gameVariables._data[e]=$dataCSV.events[t][a][o].Data[s][n+1]}function VarFromCSVCommon(e,t,a){let o=$gameVariables._data[1]||0;return $gameVariables._data[e]=$dataCSV.common[t].Data[a][o+1]}function GetLanguage(){return $gameVariables._data[$languageVarIndex]||0}function GetLanguage2(){}{let e=[{table:"$dataActors",folder:"actors"},{id:"armor",table:"$dataArmors",folder:"armors"},{id:"class",table:"$dataClasses",folder:"classes"},{id:"enemy",table:"$dataEnemies",folder:"enemies"},{id:"item",table:"$dataItems",folder:"items"},{id:"skill",table:"$dataSkills",folder:"skills"},{id:"state",table:"$dataStates",folder:"states"},{id:"troop",table:"$dataTroops",folder:"troops"},{id:"weapon",table:"$dataWeapons",folder:"weapons"},{id:"system",table:"$dataSystem",folder:"system"}];e.GetByIdExcept=function(t=[]){return e.filter(e=>!t.includes(e.id))},e.GetFoldersExcept=function(e=[]){return this.filter(t=>!e.includes(t.folder)).map(e=>e.folder)},$dataCSV=function(){let t={common:[],events:[],system:[]},a=e=>Number.parseInt(e.split("_")[1]),o=e=>{let t=e.split(".");return t.pop(),t.join(".")},s=e=>{return"csv"===e.split(".").pop()},n=new CSV(LoadFileUTF8Escaped("./csv/language.csv"));t.language=n;let r=e.GetFoldersExcept(["system"]);for(let e of r){t[e]=[];for(let a=0;a<n.Data.length;a++){let o=`./csv/${e}/${e}_${n.Data[a][0].trim()}.csv`;$Node.fs.existsSync(o)&&(t[e][a]=new CSV(LoadFileUTF8Escaped(o)))}}let l=$Node.fs.readdirSync("./csv/system");for(let e of l)if(s(e)){let a=new CSV(LoadFileUTF8Escaped(`./csv/system/${e}`));a.path=o(e),t.system.push(a)}let m=$Node.fs.readdirSync("./csv/common");for(let e of m)if(s(e)){let o=a(e);t.common[o]=new CSV(LoadFileUTF8Escaped(`./csv/common/${e}`))}let i=$Node.fs.readdirSync("./csv/maps");for(let e of i){let o=a(e),n=$Node.fs.readdirSync(`./csv/maps/${e}`);t.events[o]=[];for(let r of n){let n=a(r),l=$Node.fs.readdirSync(`./csv/maps/${e}/${r}`);t.events[o][n]=[];for(let m of l)if(s(m)){let s=a(m);t.events[o][n][s]=new CSV(LoadFileUTF8Escaped(`./csv/maps/${e}/${r}/${m}`))}}}return t}();let t={cr(e,[t]){$gameTemp.reserveCommonEvent(t),e()},c(e,[t]){$gameTemp.reserveCommonEvent(t),e()},s(e,[t,a]){$gameSwitches._data[t]=a,e()},v(e,[t,a]){$gameVariables._data[t]=a,e()},se(e,[t]){$commandPlaySE=!0,AudioManager.playSe({name:t,pan:0,pitch:100,volume:100}),e()},me(e,[t]){AudioManager.playMe({name:t,pan:0,pitch:100,volume:100}),e()},bgs(e,[t]){AudioManager.playBgs({name:t,pan:0,pitch:100,volume:100}),e()},bgm(e,[t]){AudioManager.playBgm({name:t,pan:0,pitch:100,volume:100}),e()},stop_se(e){AudioManager.stopSe(),e()},stop_me(e){AudioManager.stopMe(),e()},stop_bgs(e){AudioManager.stopBgs(),e()},stop_bgm(e){AudioManager.stopBgm(),e()},stop_all_audio(e){AudioManager.stopAll(),e()}};Window_Message.prototype.newPage=function(e){this.contents.clear(),this.resetFontSettings(),this.clearFlags(),this.loadMessageFace(),e.x=this.newLineX(),e.y=0,e.left=this.newLineX(),e.height=this.calcTextHeight(e,!1)};let a,o=0,s=0,n=!1,r=function(e,a){let o,s,n=[],r=$gameVariables._data[1]||0,l=new SeqDesc(a.parameters[0]);if(l.length>0)for(let a of l)if($flagReservedCommonCall&&($lastCommonEventId=$lastReservedCommonEventId),$lastCommonEventId)(o=$dataCSV.common[$lastCommonEventId])?(s=o.Data[a])?n.push({message:s[r+1],coex:new CoEx(s[0],t)}):console.error(`%cERROR: NO ENTRY WITH ID '${a}' FOUND!`,"color:#ff0000"):console.error(`%cERROR: NO CSV WITH COMMON '${$lastCommonEventId}' FOUND!`,"color:#ff0000");else{let l=e._mapId,m=e._eventId,i=$gameMap.events()[m-1]._pageIndex+1,d=$dataCSV.events[l];if(d){let e=d[m];e?(o=e[i])?(s=o.Data[a])?n.push({message:s[r+1],coex:new CoEx(s[0],t)}):console.error(`%cERROR: NO ENTRY WITH ID '${a}' FOUND!`,"color:#ff0000"):console.error(`%cERROR: NO CSV WITH PAGE ID '${$pageId}' FOUND!`,"color:#ff0000"):console.error(`%cERROR: NO EVENT WITH ID '${m}' FOUND!`,"color:#ff0000")}else console.error(`%cERROR: NO MAP WITH ID '${l}' FOUND!`,"color:#ff0000")}else console.error(`%cERROR: TEXT INPUT '${l}' INVALID!`,"color:#ff0000");return n};function Translate(){let t=e.GetByIdExcept(["system"]);for(let e of t){let t=$dataCSV[e.folder];if(t.length>0){let a={};for(let e=0;e<t.length;e++){let o=t[e].Data,s=o[0];for(let e=0;e<s.length;e++){let t=s[e];a[t]||(a[t]=[])}for(let t=1;t<o.length;t++){let n=o[t];for(let o=0;o<s.length;o++){let r=n[o],l=s[o];a[l][t]||(a[l][t]=[]),a[l][t][e]=r}}}let o=window[e.table];for(let t=1;t<o.length;t++){let s=o[t],n=$dataCSV[e.folder];if(n&&n.length>0&&n[0].Data[t]){let e=Object.keys(a);for(let o of e)Object.defineProperty(s,o,{get(){let e=$gameVariables._data[1]||0;return a[o][t][e]||"Error!"}})}}}}for(let e of $dataCSV.system){let t=Access($dataSystem,e.path);if(t){switch(GetType(t)){case"Array":let a=e.Data;for(let o=0;o<a[0].length;o++)Object.defineProperty(t,`${o}`,{get(){let t=$gameVariables._data[1]||0;return e.Data[t][o]||"Error!"}})}}}}Game_Interpreter.prototype.command101=function(){$currentInterpreter=this;let e=this._list[this._index+1];if(n||(n=!0,s=0,a=r(this,e)),$commandPlaySE&&(AudioManager.stopSe(),$commandPlaySE=!1),!$gameMessage.isBusy()){if($gameMessage.setFaceImage(this._params[0],this._params[1]),$gameMessage.setBackground(this._params[2]),$gameMessage.setPositionType(this._params[3]),401===this.nextEventCode()){o++;let e=a[s++];e&&(e.coex.Run(function(){}),$gameMessage.add(e.message)),s>=a.length&&(n=!1,this._index++)}switch(this.nextEventCode()){case 102:this._index++,this.setupChoices(this.currentCommand().parameters);break;case 103:this._index++,this.setupNumInput(this.currentCommand().parameters);break;case 104:this._index++,this.setupItemChoice(this.currentCommand().parameters)}n||this._index++,this.setWaitMode("message")}return!1},Game_Interpreter.prototype.command117=function(){var e=$dataCommonEvents[this._params[0]];if(e){this._lastCommonEventId=this._params[0],this._flagCommonEvent117=!0,$lastCommonEventId=this._lastCommonEventId,$currentCommonEventIds.push(this._lastCommonEventId);var t=this.isOnCurrentMap()?this._eventId:0;this.setupChild(e.list,t)}return!0},Game_Interpreter.prototype.setupReservedCommonEvent=function(){return!!$gameTemp.isCommonEventReserved()&&($flagReservedCommonCall=!0,this.setup($gameTemp.reservedCommonEvent().list),$gameTemp.clearCommonEvent(),!0)},Game_Interpreter.prototype.executeCommand=function(){var e=this.currentCommand();if(e){this._params=e.parameters,this._indent=e.indent;var t="command"+e.code;if("function"==typeof this[t]&&!this[t]())return!1;this._index++}else $currentCommonEventIds.length>0?($lastRemovedCommonEventId=$currentCommonEventIds.pop(),$lastCommonEventId=$currentCommonEventIds[$currentCommonEventIds.length-1]||0):$lastCommonEventId=0,$flagReservedCommonCall=!1,this.terminate();return!0},Game_Temp.prototype.initialize=function(){this._isPlaytest=Utils.isOptionValid("test"),this._commonEventId=0,this._lastCommonEventId=0,this._destinationX=null,this._destinationY=null},Game_Temp.prototype.reserveCommonEvent=function(e){$lastReservedCommonEventId=this._lastCommonEventId=this._commonEventId=e};let l=Scene_Boot.prototype.start;Scene_Boot.prototype.start=function(){Translate(),l.apply(this,arguments)}}