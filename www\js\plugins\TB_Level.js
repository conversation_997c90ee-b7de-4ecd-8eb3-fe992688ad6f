/*:
 * @plugindesc Handles Actor Leveling Logic and Set Maximum Level for Actors
 * <AUTHOR> Name
 *
 * @param Max Level
 * @type number
 * @min 1
 * @max 99
 * @desc The maximum level that actors can achieve.
 * @default 5
 *
 * @help This plugin sets the maximum level for all actors and also handles level up events.
 */

(function() {
    var parameters = PluginManager.parameters('TB_Level');
    var maxLevel = parseInt(parameters['Max Level'] || 5);

    Game_Actor.prototype.changeExp = function(exp, show) {
        var actorId = this.actorId();
        var initialLevel = this._level;

        if (this.isMaxLevel()) {
            if(exp >= this.nextLevelExp()) {
                // If actor is at max level and the experience gained would level them up, skip processing
                return;
            }
            this._exp[this._classId] = this.currentExp();
        } else {
            this._exp[this._classId] = exp;
            this._exp[this._classId] = Math.max(this.currentExp(), 0);
        }
        
        while (!this.isMaxLevel() && this.currentExp() >= this.nextLevelExp()) {
            this.levelUp();
        }
        while (this.currentExp() < this.currentLevelExp()) {
            this.levelDown();
        }
        if (show && this._level > initialLevel) {
            this.displayLevelUp(this.findNewSkills(initialLevel));
        }
        
        this.refresh();

        // Override max level
        if (this._level >= maxLevel) {
            this._level = maxLevel;
            this._exp[this._classId] = this.currentExp();
        }

        // Check the difference in levels and assign to the window variable
        window['$levelup_' + actorId] = this._level - initialLevel;
        console.log('アクターID ' + actorId + ' のレベル差: ' + (this._level - initialLevel));


        // After processing all level ups, if there was a level up, call common event 28
        if (this._level > initialLevel) {
            $gameTemp.reserveCommonEvent(28);
        }
    };
})();
