{function Component_Image(t){Object.assign(this,{Src:"",X:0,Y:0,ScaleX:1,ScaleY:1,Visible:!1},t),this.Bitmap=ImageManager.loadPicture(this.Src),this.Sprite=new Sprite(this.Bitmap),this.UpdateSprites(),this.Parent&&this.Parent.addChild(this.Sprite)}Component_Image.prototype.SetImage=function(t){this.Bitmap=ImageManager.loadPicture(t),this.Sprite.bitmap=this.Bitmap,this.UpdateSprites()},Component_Image.prototype.UpdateSprites=function(){this.Sprite.x=this.X,this.Sprite.y=this.Y,this.Sprite.scale.set(this.ScaleX,this.ScaleY),this.Sprite.visible=this.Visible}}