let $LogWindow_CurrentWindow,$LogWindow_CurrentWindow_BackgroundSprite;{let t=PluginManager.parameters(PluginParams.PREFIX+"LogWindow"),o={};o.X=parseInt(t.X),o.Y=parseInt(t.Y),o.Width=parseInt(t.Width),o.Height=parseInt(t.Height),o.FontSize=parseInt(t.FontSize),o.<PERSON>gin=parseInt(t.<PERSON><PERSON>),o.WindowSkin=t.WindowSkin,o.Background=t.Background,o.Alpha=t.Alpha,o.Speed=parseFloat(t.Speed),o.Acceleration=parseFloat(t.Acceleration);let i=ImageManager.loadPicture(o.Background);function Window_LogWindow(){this.HistoryCount=$LogWindow.History.length,this.HistoryObj=$LogWindow.History,this.initialize.apply(this,arguments),this.HistoryPosY=0,this.HistorySpeedY=0,this.HistoryAcceleration=0,this.Animate=!1,this.FirstDraw=!1}Window_LogWindow.prototype=Object.create(Window_Base.prototype),Window_LogWindow.prototype.constructor=Window_LogWindow,Window_LogWindow.prototype.initialize=function(t,i,n,e){Window_Base.prototype.initialize.call(this,o.X,o.Y,o.Width,o.Height),this.ContentsHeight=this.contentsHeight()},Window_LogWindow.prototype.standardFontSize=function(){return o.FontSize},Window_LogWindow.prototype.loadWindowskin=function(){this.windowskin=ImageManager.loadSystem(o.WindowSkin)},Window_LogWindow.prototype.update=function(){if(this.FirstDraw||(this.drawHistory(0,this.HistoryPosY,o.FontSize+o.Margin),this.FirstDraw=!0),$LogWindow.History.length>this.HistoryCount||$LogWindow.History!=this.HistoryObj){let t=$LogWindow.History.length-this.HistoryCount||$LogWindow.Sliced;this.HistoryCount=$LogWindow.History.length,this.HistoryObj=$LogWindow.History,this.Animate=!0,this.HistoryPosY+=t*(o.FontSize+o.Margin),this.HistorySpeedY=o.Speed,this.HistoryAcceleration=0}this.Animate&&this.animate()},Window_LogWindow.prototype.drawHistory=function(t,i,n=32){let e=$LogWindow.History,r=e.length-1,s=this.ContentsHeight,d=o.FontSize+o.Margin;this.contents.clear();for(let o=0;o<=r;o++){let a=i+(s-(n*o+n));a>=-d&&this.drawTextEx(e[r-o],t,a)}},Window_LogWindow.prototype.drawHistoryOLD=function(t,o,i=32){let n=$LogWindow.History,e=n.length-1,r=this.contentsHeight();this.contents.clear();for(let s=0;s<=e;s++)this.drawTextEx(n[e-s],t,o+(r-(i*s+i)))},Window_LogWindow.prototype.animate=function(){this.drawHistory(0,this.HistoryPosY,o.FontSize+o.Margin),this.HistoryPosY>0?(this.HistoryPosY-=this.HistorySpeedY,this.HistorySpeedY+=o.Acceleration):(this.HistoryPosY=0,this.Animate=!1,this.drawHistory(0,this.HistoryPosY,o.FontSize+o.Margin))},$VXACE.ScriptCommand.AddVoidCall("show_map_log_window",function(){$LogWindow_CurrentWindow&&$LogWindow_CurrentWindow.show()}),$VXACE.ScriptCommand.AddVoidCall("hide_map_log_window",function(){$LogWindow_CurrentWindow&&$LogWindow_CurrentWindow.hide()}),$VXACE.ScriptCommand.AddVoidCall("clear_mlog",function(){$LogWindow&&$LogWindow.Clear()}),$VXACE.ScriptCommand.AddCall("set_mlog",function(t){if($LogWindow){let o=t,i=t.split("_log_database_text_")[1];$LogSheetCSV.Exist(i)&&(o=$LogSheetCSV.Get(i)),$LogWindow.Add(o)}});let n=Scene_Map.prototype.createDisplayObjects;Scene_Map.prototype.createDisplayObjects=function(){n.call(this);let t=new Window_LogWindow,e=new Sprite(i);e.alpha=o.Alpha,t.addChildAt(e,0),$LogWindow_CurrentWindow_BackgroundSprite=e,$LogWindow_CurrentWindow=t,$LogWindow.Width=t.contentsWidth(),$LogWindow.Sync(),this.addWindow(t)};let e=DataManager.createGameObjects;DataManager.createGameObjects=function(){e.call(this),$LogWindow.Clear()};let r=Scene_Load.prototype.onLoadSuccess;Scene_Load.prototype.onLoadSuccess=function(){r.call(this),$LogWindow.Clear()}}