{function Component_Text(t={}){Object.assign(this,{Text:"",X:0,Y:0,BitmapWidth:1,BitmapHeight:1,FontSize:28,Visible:!1},t),this.Bitmap=new Bitmap(this.BitmapWidth,this.BitmapWidth),this.Sprite=new Sprite(this.Bitmap),this.Bitmap.fontSize=this.FontSize||28,this.Parent&&this.Parent.addChild(this.Sprite),this.draw()}Component_Text.prototype.UpdateSprites=function(){this.Sprite.x=this.X,this.Sprite.y=this.Y,this.Sprite.visible=this.Visible},Component_Text.prototype.draw=function(){this.Bitmap.clear();let t=this.Bitmap.measureTextWidth(this.Text);t=Math.min(this.Bitmap.width,t),this.Bitmap.drawText(this.Text,0,0,t,this.Bitmap.fontSize,"left"),this.UpdateSprites()}}