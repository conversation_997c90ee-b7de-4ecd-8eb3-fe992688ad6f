let SeqDescRules=[["NUMBER",/^\d+/],["-",/^\-/],["&",/^\&/]];function SeqDesc(e){let l=[];if(!/^((\d+(-\d+)*)(\&(\d+(-\d+)*))*)$/.test(e))return console.error("invalid syntax"),l;let n,t=0,s=0,u=0,o=0,r=!1,i={NUMBER:function(e){let n=parseInt(e);if(0!==t&&r){if(1===t&&r&&u!==(o=n)){let e=u<o,n=Math.abs(u-o)+1;for(let t=1;t<n;t++)l.push(e?u+t:u-t);u=o}}else u=n,l.push(n),t=1},"-":function(){s=0,r=!0},"&":function(){r=!1}},f=GenerateTokens(e,SeqDescRules);for(;!(n=f.next()).done;)i[(n=n.value)[0]](n[1]);return l}function*GenerateTokens(e,l){let n=e.length;for(let t=0;t<n;t++){let n=null,s=null,u=!1;for(let o=0;o<l.length;o++)if(n=l[o][1].exec(e)){e=e.slice(n[0].length),t+=n[0].length-1,s=[l[o][0],n[0]],u=null===l[o][0];break}u||null!==s||(s=`UNKOWN: ${(e=e.slice(1))[0]}`),u||(yield s)}}