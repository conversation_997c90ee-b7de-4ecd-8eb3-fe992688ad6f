{function Component_StatusIcons(t){Object.assign(this,{X:0,Y:0,Width:200,Height:100,IconPadding:5,IconLimit:5,IconDelay:750,Visible:!1},t),this.StatesCount=-1,this.Delta=0,this.Bitmap=new Bitmap(this.Width,this.Height),this.Sprite=new Sprite(this.Bitmap),this.IconIndex=0,this.draw(),this.Parent&&this.Parent.addChild(this.Sprite)}Component_StatusIcons.prototype.UpdateSprites=function(){this.Sprite.x=this.X,this.Sprite.y=this.Y,this.Sprite.visible=this.Visible},Component_StatusIcons.prototype.draw=function(){if(this.Parent&&this.Parent.Actor){let t=this.Parent.Actor,i=new Rectangle(0,0,0,0),e=new Rectangle(0,0,this.IconPadding,0),s=t._states.length,n=Math.min(this.IconLimit,s);this.Bitmap.clear();for(let a=0;a<n;a++){let n=t._states[s>this.IconLimit?(this.IconIndex+a)%s:a],o=$dataStates[n].iconIndex,h=BitmapHelper.DrawIcon(this.Bitmap,{x:i.x,y:i.y,iconIndex:o});i.AddWidthToPositon(h),i.AddWidthToPositon(e)}}this.UpdateSprites()},Component_StatusIcons.prototype.drawOLD=function(){if(this.Parent&&this.Parent.Actor){let t=this.Parent.Actor,i=new Rectangle(0,0,0,0);for(let e=0;e<t._states.length;e++){let s=t._states[e],n=$dataStates[s].iconIndex,a=BitmapHelper.DrawIcon(this.Bitmap,{x:i.x,y:i.y,iconIndex:n});i.AddWidthToPositon(a)}}this.UpdateSprites()};let t=!0;Component_StatusIcons.prototype.update=function(){if(this.Delta+=SceneManager._deltaTime,t&&this.Delta>=this.IconDelay/1e3&&(this.Delta=0,this.IconIndex++,this.draw()),this.Parent&&this.Parent.Actor){let t=this.Parent.Actor;this.StatesCount!=t._states.length&&(this.StatesCount=t._states.length,this.draw())}}}