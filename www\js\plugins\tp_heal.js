class Game_Actor {
    static tp_heal_actor(id, num) {
        let actor = $gameActors.actor(id);
        if (actor) {
            num = parseInt(num);
            let follower = 0;
            let loop = id === 0 ? 2 : 1;

            if (id === 0 && $gameParty.members().length >= 3) {
                follower = $gameParty.members()[2].actorId();
                console.log("Follower ID: " + follower);
                loop = 3;
                id = 1;
            }

            for (let n = 0; n < loop; n++) {
                actor = $gameActors.actor(id);
                actor._tp -= num;
                let tp = Math.floor(actor._tp);

                let fatigue = $gameVariables.value(1) === 0 ? "疲労" : "Fatigue";

                // Display result
                if (num >= 0) {
                    set_mlog(`\\c[6][\\n[${id}]]${fatigue}\\c[24] - ${num}\\c[0]\\c[6](${tp} / 100)`);
                } else {
                    num *= -1;
                    set_mlog(`\\c[6][\\n[${id}]]${fatigue}\\c[2] + ${num}\\c[0]\\c[6](${tp} / 100)`);
                }

                if (id === 2 && follower >= 1) {
                    id = follower;
                }

                if (id === 1 && loop >= 2) {
                    id = 2;
                }

                if (tp === 100) {
                    if (!actor.isStateAffected(16)) {
                        console.log("Apply");
                        actor.addState(16);
                        let text = `\\c[6][${actor.name()}] + \\I[${$dataStates[16].iconIndex}]${$dataStates[16].name}`;
                        set_mlog(text);
                    }
                } else {
                    if (actor.isStateAffected(16)) {
                        actor.removeState(16);
                        let text = `\\c[6][${actor.name()}] - \\I[${$dataStates[16].iconIndex}]${$dataStates[16].name}`;
                        set_mlog(text);
                    }
                }
            }
        } else {
            console.log(`Actor with id: ${id} does not exist.`);
        }
    }

    // Placeholder for `set_mlog` method
    static set_mlog(text) {
        console.log(text);
    }
}

function tp_heal(id, num) {
    Game_Actor.tp_heal_actor(id, num);
}
