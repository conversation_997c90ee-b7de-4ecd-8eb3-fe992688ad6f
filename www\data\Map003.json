{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "harmonic-exoticland", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 35, "note": "フィールド\nBGM固定", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 40, "data": [2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 4008, 3976, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 4008, 3996, 3996, 3976, 3968, 3968, 3968, 3968, 3972, 3996, 4006, 2832, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2826, 2836, 2836, 2852, 4008, 3996, 3976, 3972, 3996, 4006, 2850, 2836, 2817, 2820, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2840, 3714, 3700, 3716, 2832, 2816, 2816, 2818, 2836, 2852, 4008, 4006, 2850, 2836, 2817, 2816, 2820, 2854, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2820, 2854, 3330, 3332, 2856, 2841, 3696, 3680, 3704, 2832, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2820, 2844, 2854, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2840, 3339, 3305, 3298, 3332, 2860, 3720, 3708, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2818, 2852, 3336, 3304, 3298, 3317, 3341, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2844, 2844, 2844, 2826, 2852, 3336, 3304, 3320, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2854, 2064, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2081, 2081, 2093, 2832, 2818, 2852, 3336, 3334, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2091, 2081, 2081, 2081, 2057, 2048, 2048, 2048, 2048, 2072, 2850, 2836, 2852, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2836, 2836, 2836, 2817, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2837, 2849, 2861, 2066, 2076, 2056, 2048, 2048, 2072, 2832, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2091, 2081, 2087, 2858, 2088, 2076, 2056, 2072, 2856, 2844, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2285, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2842, 3627, 3621, 2064, 2050, 2068, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3616, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3616, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2276, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3616, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2242, 2260, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3619, 3623, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2244, 2268, 2278, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3330, 3316, 3332, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3616, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2278, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 3312, 3296, 3320, 2856, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2840, 3628, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3330, 3316, 3297, 3296, 3298, 3316, 3316, 3316, 3332, 2856, 2844, 2824, 2816, 2816, 2840, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3312, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3298, 3316, 3332, 2856, 2844, 2824, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3312, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3298, 3316, 3332, 2856, 2841, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3339, 3305, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3298, 3332, 2848, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3312, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3320, 2848, 2088, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2056, 2048, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3336, 3304, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3320, 2833, 2836, 2852, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 3714, 3716, 2088, 2056, 2048, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3312, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3296, 3320, 2856, 2824, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 3714, 3681, 3682, 3716, 2088, 2056, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3336, 3324, 3324, 3304, 3296, 3296, 3296, 3296, 3296, 3296, 3300, 3324, 3334, 2090, 2856, 2841, 2064, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 3696, 3680, 3680, 3682, 3716, 2064, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2082, 2068, 2068, 2084, 3336, 3324, 3324, 3324, 3304, 3296, 3296, 3320, 2082, 2068, 2051, 2084, 2860, 2064, 2048, 2048, 2048, 2048, 2048, 2072, 3723, 3709, 3708, 3688, 3684, 3718, 2064, 2816, 2816, 2816, 2820, 2844, 2828, 2854, 2082, 2068, 2049, 2048, 2048, 2050, 2068, 2068, 2068, 2084, 3336, 3324, 3324, 3334, 2064, 2048, 2048, 2050, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2084, 3720, 3718, 2082, 2049, 2844, 2844, 2844, 2854, 2090, 2860, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2049, 2048, 2068, 2068, 2068, 2068, 2051, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2850, 2836, 2852, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2856, 2844, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 3180, 3180, 3160, 3152, 3152, 3152, 3176, 3472, 3168, 3152, 3176, 4344, 4312, 4328, 0, 4320, 4304, 4304, 4308, 4332, 4312, 4304, 4328, 3168, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3028, 3044, 3192, 3180, 3160, 3152, 3176, 3472, 3168, 3152, 3154, 3188, 4320, 4310, 4337, 4313, 4304, 4304, 4328, 0, 4320, 4308, 4342, 3168, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3008, 3010, 3028, 3044, 3192, 3180, 3190, 3472, 3168, 3152, 3152, 3176, 4344, 4342, 3194, 4344, 4332, 4312, 4310, 4337, 4333, 4342, 3186, 3153, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3008, 3008, 3008, 3014, 3041, 3053, 0, 3472, 3192, 3180, 3160, 3154, 3172, 3172, 3155, 3172, 3188, 4344, 4342, 3186, 3172, 3172, 3153, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3008, 3008, 3012, 3046, 0, 0, 0, 3472, 0, 0, 3168, 3152, 3152, 3152, 3152, 3152, 3154, 3172, 3172, 3153, 3152, 3156, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3008, 3008, 3032, 0, 0, 0, 0, 3472, 0, 3195, 3161, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3008, 3012, 3038, 3053, 0, 0, 0, 3472, 0, 0, 3192, 3160, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3036, 3046, 3195, 3189, 3050, 0, 0, 3472, 3042, 3028, 3044, 3168, 3152, 3152, 3152, 3156, 3180, 3180, 3180, 3180, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 3049, 3053, 0, 3472, 3024, 3008, 3032, 3192, 3160, 3152, 3156, 3190, 0, 3482, 0, 0, 0, 0, 0, 0, 0, 0, 2190, 0, 3195, 3185, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3185, 3174, 3172, 3155, 3188, 0, 0, 3472, 3024, 3008, 3032, 3146, 3168, 3156, 3190, 3050, 0, 3472, 3195, 3185, 3185, 3197, 0, 0, 0, 0, 0, 0, 3042, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3581, 3192, 3160, 3152, 3154, 3188, 0, 3472, 3024, 3008, 3032, 3148, 3192, 3190, 3042, 3011, 3044, 3472, 0, 3050, 0, 0, 0, 3482, 0, 0, 0, 0, 3048, 3036, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 3168, 3152, 3152, 3176, 3150, 3472, 3048, 3036, 3046, 0, 3051, 3041, 3037, 3036, 3046, 3472, 3051, 3039, 3041, 3053, 0, 3472, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3412, 3428, 3192, 3160, 3152, 3176, 0, 3459, 3473, 3463, 3473, 3473, 3473, 3473, 3473, 3473, 3473, 3471, 3473, 3473, 3473, 3473, 3473, 3471, 3485, 0, 0, 0, 0, 2190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3420, 3402, 3428, 3170, 3180, 3162, 3188, 3472, 3194, 3472, 3147, 3137, 3149, 3051, 3045, 0, 3042, 3028, 3028, 3028, 3028, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3430, 3196, 3098, 3168, 3176, 3484, 3184, 3472, 3186, 3172, 3172, 3188, 3049, 3030, 3009, 3008, 3008, 3012, 3036, 3036, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 3088, 3192, 3162, 3172, 3178, 3472, 3170, 3180, 3180, 3182, 3189, 3048, 3016, 3008, 3008, 3032, 3186, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3424, 3073, 3092, 3192, 3160, 3176, 3484, 3184, 3579, 3573, 3386, 3169, 3188, 3048, 3036, 3020, 3046, 3168, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3418, 3072, 3058, 3092, 3168, 3158, 3185, 3191, 3386, 3580, 3376, 3192, 3182, 3197, 0, 3052, 3186, 3153, 3152, 3152, 3176, 0, 0, 0, 2190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3412, 3393, 3392, 3416, 3072, 3056, 3080, 3168, 3176, 3579, 3573, 3361, 3364, 3370, 3579, 3569, 3573, 3386, 3195, 3181, 3160, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3392, 3396, 3430, 3072, 3056, 3080, 3192, 3190, 3386, 3580, 3360, 3344, 3346, 3364, 3380, 3580, 3376, 3579, 3581, 3192, 3180, 3160, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3392, 3416, 3099, 3065, 3056, 3080, 0, 0, 3361, 3364, 3345, 3344, 3344, 3344, 3346, 3364, 3351, 3377, 3377, 3377, 3389, 3192, 3177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3392, 3394, 3428, 3096, 3064, 3080, 3198, 3582, 3384, 3372, 3372, 3352, 3344, 3344, 3348, 3372, 3369, 3579, 3569, 3569, 3569, 3573, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3392, 3392, 3394, 3428, 3072, 3062, 3089, 3101, 3570, 3556, 3572, 3384, 3356, 3372, 3382, 3578, 3361, 3364, 3365, 3377, 3381, 3580, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3392, 3392, 3392, 3416, 3074, 3094, 3195, 3197, 3576, 3544, 3538, 3572, 3388, 3570, 3556, 3562, 3384, 3372, 3382, 3578, 3385, 3389, 3169, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4292, 0, 0, 0, 3392, 3392, 3392, 3392, 3416, 3088, 3198, 3091, 3089, 3101, 3552, 3536, 3538, 3556, 3537, 3536, 3538, 3556, 3556, 3556, 3539, 3556, 3572, 3192, 3160, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 4291, 4285, 4286, 4301, 0, 0, 3392, 3392, 3392, 3392, 3416, 3073, 3076, 3082, 3187, 3197, 3576, 3564, 3564, 3544, 3536, 3536, 3536, 3536, 3540, 3564, 3548, 3564, 3574, 0, 3192, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 4288, 0, 3774, 0, 4298, 0, 3420, 3400, 3396, 3420, 3430, 3074, 3084, 3094, 3196, 0, 0, 0, 0, 3576, 3564, 3564, 3564, 3544, 3560, 3390, 3568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4299, 4287, 4289, 4293, 3774, 4300, 0, 3188, 3432, 3430, 3187, 3197, 3088, 3198, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3566, 3569, 3575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4297, 4301, 0, 0, 3182, 3185, 3185, 3191, 0, 3100, 0, 2190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3821, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3195, 3185, 3191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 195, 196, 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 238, 239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 150, 151, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 238, 239, 0, 0, 0, 0, 0, 0, 246, 247, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 159, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 246, 247, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 265, 0, 265, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 265, 0, 265, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 266, 256, 273, 256, 257, 256, 273, 258, 281, 258, 257, 258, 258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 256, 272, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 265, 277, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 274, 258, 280, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 265, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 256, 256, 256, 257, 256, 256, 256, 256, 256, 273, 256, 257, 258, 281, 256, 257, 256, 256, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 265, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 266, 256, 256, 0, 282, 256, 256, 0, 0, 0, 0, 0, 266, 258, 280, 0, 282, 256, 281, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 27, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 35, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 265, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 33, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 0, 128, 0, 128, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 1, 128, 1, 128, 1, 128, 1, 128, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 1, 128, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135, 0, 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135, 0, 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 133, 8, 133, 6, 130, 3, 129, 2, 129, 2, 129, 2, 129, 2, 129, 2, 129, 2, 129, 2, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 137, 12, 137, 0, 132, 5, 132, 0, 0, 0, 0, 0, 0, 0, 129, 0, 129, 2, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "初期イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["リージョン11は路地裏判定で使っているため使わないこと"]}, {"code": 355, "indent": 0, "parameters": ["$WMTS.Set(1, true)"]}, {"code": 655, "indent": 0, "parameters": ["$WMTS.Set(128, true)"]}, {"code": 111, "indent": 0, "parameters": [0, 310, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(2, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(129, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 315, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(3, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(130, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 316, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(4, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(131, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 317, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(5, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(132, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 318, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(6, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(133, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 319, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(7, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(134, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 1282, 0, 99, 1]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(8, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(133, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 320, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(9, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(135, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 321, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(10, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(136, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 322, 0]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.Set(12, true)"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Set(137, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["WMTS_Prepare();"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 135, "indent": 0, "parameters": [0]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 3, 7, 0, 0]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 3, 5, -1, 1]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 203, "indent": 0, "parameters": [2, 1, 42, 43, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["!$Cursors", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["!$Cursors", 0]}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 240, 1]}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-exoticland", "pan": 0, "pitch": 100, "volume": 50}]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 121, "indent": 1, "parameters": [240, 240, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-exoticland", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$world_map = 1"]}, {"code": 121, "indent": 0, "parameters": [160, 160, 0]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "<PERSON><PERSON><PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Other3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 1}, {"id": 3, "name": "ID:004 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 12}, {"id": 4, "name": "港町アリエス", "note": "<KNS_WorldMapSlide: 002>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name2\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"port_town\""]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"Peaceful\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1523]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 10}, {"id": 5, "name": "ID:003 村", "note": "<KNS_WorldMapSlide: 006>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 6]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name6\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"village\""]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"Peaceful\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1527]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 12}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 12}, {"id": 7, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 12}, {"id": 8, "name": "EV008", "note": "<KNS_WorldMapSlide: 021>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name21\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"beach\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 25, "y": 12}, {"id": 9, "name": "入り江洞窟", "note": "<KNS_WorldMapSlide: 033>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 33]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name33\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 10}, {"id": 10, "name": "ID:040 小屋", "note": "<KNS_WorldMapSlide: 040>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 40]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name40\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"mansion\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(1111) >= 6 && v.value(1111) < 9;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 40]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name40\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"mansion\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 10}, {"id": 11, "name": "EV011", "note": "<KNS_WorldMapSlide: 025>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 25]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name25\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"tower\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 10}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 14}, {"id": 13, "name": "忘れられた洞窟", "note": "<KNS_WorldMapSlide: 034>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 34]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name34\")"]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 16}, {"id": 14, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 14}, {"id": 15, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 12}, {"id": 16, "name": "ID:039 廃教会のある森", "note": "<KNS_WorldMapSlide: 039>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 39]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name39\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 14}, {"id": 17, "name": "ID:36 ブレッシング・ロード", "note": "<KNS_WorldMapSlide: 032>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name32\")"]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 32]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 12}, {"id": 18, "name": "36:山間の隠道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 2, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 36]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name36\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 7}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 2, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 0, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 8}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 10}, {"id": 19, "name": "37:打ち捨てられた山小屋", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 37]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 7}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 37]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name37\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 8}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 37]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name37\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 8}, {"id": 20, "name": "霊峰ロンデニオン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 102]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name102\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"dungeon\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 2}, {"id": 21, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 14}, {"id": 22, "name": "ID:035　南部に続く森", "note": "<KNS_WorldMapSlide: 035>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 35]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name35\")"]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 16}, {"id": 23, "name": "common", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 10}, {"id": 24, "name": "ID:006　孤児院", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 914, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 0, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 24]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name24\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"mansion\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 915, "variableValid": true, "variableValue": 99}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 24]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name24\")"]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 16}, {"id": 25, "name": "巨人の枕", "note": "<KNS_WorldMapSlide: 008>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 8]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name8\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 18}, {"id": 26, "name": "スプリングエルダーツリー", "note": "<KNS_WorldMapSlide: 022>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 2, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************"]}, {"code": 408, "indent": 0, "parameters": ["* 四季を通して花開く楽園と呼ばれている大樹"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 22]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 25, "y": 26}, {"id": 27, "name": "タイタン・ステップ", "note": "<KNS_WorldMapSlide: 064>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 2, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 64]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name64\")"]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 20}, {"id": 28, "name": "ホスピタラー騎士団・駐屯地", "note": "<KNS_WorldMapSlide: 005>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 5]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name5\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1526]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 24}, {"id": 29, "name": "荒野の街", "note": "<KNS_WorldMapSlide: 007>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 7]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name7\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"wasteland_town\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 20}, {"id": 30, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 20}, {"id": 31, "name": "蟻地獄の巣", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 65]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name65\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 22}, {"id": 32, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 22}, {"id": 33, "name": "遺跡（ドゲェッシューナ）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 60]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name60\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 22}, {"id": 34, "name": "遺跡１", "note": "<KNS_WorldMapSlide: 061>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 61]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name61\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 20}, {"id": 35, "name": "遺跡２", "note": "<KNS_WorldMapSlide: 062>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 62]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name62\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 18}, {"id": 36, "name": "遺跡３", "note": "<KNS_WorldMapSlide: 063>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 63]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name63\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 16}, {"id": 37, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 18}, {"id": 38, "name": "北部の砦", "note": "<KNS_WorldMapSlide: 012>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 6, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 12]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name12\")"]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"beach\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 2}, {"id": 39, "name": "自由都市ゴルゴダ", "note": "<KNS_WorldMapSlide: 003>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name3\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"port_town\""]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"Peaceful\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1524]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 4}, {"id": 40, "name": "剣の墓場", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 6}, {"id": 41, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 20}, {"id": 42, "name": "オークの集落", "note": "<KNS_WorldMapSlide: 066>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 2, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 66]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name66\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 26}, {"id": 43, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 24}, {"id": 44, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 22}, {"id": 45, "name": "ID:001　王城", "note": "<KNS_WorldMapSlide: 001>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name1\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1522]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 8}, {"id": 46, "name": "血塗られた遺跡", "note": "<KNS_WorldMapSlide: 067>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 67]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name67\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 22}, {"id": 47, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 12}, {"id": 48, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 12}, {"id": 49, "name": "ID:004 学園都市", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name4\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1525]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 14}, {"id": 50, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 24}, {"id": 51, "name": "炎溶の聖域", "note": "<KNS_WorldMapSlide: 068>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 68]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name68\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 26}, {"id": 52, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 20}, {"id": 53, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 20}, {"id": 54, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 18}, {"id": 55, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 18}, {"id": 56, "name": "ID:009 冒険者の野営地", "note": "<KNS_WorldMapSlide: 009>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 9]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name9\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"wasteland_town\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 20}, {"id": 57, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 16}, {"id": 58, "name": "ID:081 アビサル・ウッズ浅部", "note": "<KNS_WorldMapSlide: 080>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 80]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 20}, {"id": 59, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 18}, {"id": 60, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 20}, {"id": 61, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 22}, {"id": 62, "name": "ID:084 死の果樹園", "note": "<KNS_WorldMapSlide: 084>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 84]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 24}, {"id": 63, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 22}, {"id": 64, "name": "ID:011 ダークエルフの集落", "note": "<KNS_WorldMapSlide: 011>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 11]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 22}, {"id": 65, "name": "ID:014　チポ族の集落", "note": "<KNS_WorldMapSlide: 014>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 14]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 32}, {"id": 66, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 18}, {"id": 67, "name": "ID:087　毒沼", "note": "<KNS_WorldMapSlide: 087>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 87]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 16}, {"id": 68, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 14}, {"id": 69, "name": "ID:089 古城", "note": "<KNS_WorldMapSlide: 089>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 89]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 12}, {"id": 70, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 12}, {"id": 71, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 14}, {"id": 72, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 10}, {"id": 73, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 8}, {"id": 74, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 6}, {"id": 75, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 4}, {"id": 76, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 10}, {"id": 77, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 8}, {"id": 78, "name": "ID:092　古戦場", "note": "<KNS_WorldMapSlide: 092>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 92]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 6}, {"id": 79, "name": "ID:085 放棄された漁村", "note": "<KNS_WorldMapSlide: 085>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 8, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 85]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 28}, {"id": 80, "name": "ID:083　黒の洞窟", "note": "<KNS_WorldMapSlide: 083>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 0, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 83]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 22}, {"id": 81, "name": "ID:081 アビサル・ウッズ浅部", "note": "<KNS_WorldMapSlide: 081>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 81]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 20}, {"id": 82, "name": "ID:082　蛇の神殿", "note": "<KNS_WorldMapSlide: 082>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 82]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 16}, {"id": 83, "name": "ID:002 街道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 2, "pattern": 0, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 26}, {"id": 84, "name": "ID:090 フラワーガーデン", "note": "<KNS_WorldMapSlide: 090>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "map", "direction": 4, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 90]}, {"code": 355, "indent": 0, "parameters": ["id=$gameVariables.value(334)"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(1502, \"location_name${id}\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [303]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 26}, null, null, null]}