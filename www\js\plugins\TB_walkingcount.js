(function() {
    var _Game_Player_increaseSteps = Game_Player.prototype.increaseSteps;
    
    Game_Player.prototype.increaseSteps = function() {
        _Game_Player_increaseSteps.call(this);
        
        if (this.isNormal()) {
            $gameParty.increaseSteps();
            
            if ($gameParty.members().some(function(member) {return member.actorId() === 2;})) {
                
                if (typeof $yari_step == "undefined"){
                  $yari_step = 0
                }else{
                  $yari_step += 1
                }
                
                if ($gameParty.members().includes($gameActors.actor(21)) && $gameVariables.value(204) >= 2) {
                  if ($yari_step === 100) {
                    $yari_step = 0;
                    $gameTemp.reserveCommonEvent(455);
                  }
                }
                
                // 感度倍増時の司祭ちゃん
                var actor2 = $gameActors.actor(2);
                if (actor2.isStateAffected(15)) {
                    $gameTemp.reserveCommonEvent(452);
                } else {
                      //歩行時の司祭のランダムな発言
                      $common445 = Math.floor(Math.random() * $gameVariables.value(293));
                      if ($common445 === 1) {
                        $gameTemp.reserveCommonEvent(451);
                      }
                    
                }
            }
        }
    };
})();
