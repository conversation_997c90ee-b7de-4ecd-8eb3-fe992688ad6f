Window_Base.prototype.drawBitmap=function(t,e,n,i=0,a=0,o=t.width,c=t.height){this.contents.blt(t,i,a,o,c,e,n,t.width,t.height)},Window_Base.prototype.drawPicture=function(t,e=0,n=0){let i=ImageManager.loadPicture(t);return this.contents.blt(i,0,0,i.width,i.height,e,n,i.width,i.height),i},Window_Base.prototype.drawBitmapCut=function(t,e=0,n=0,i=0,a=0,o,c){o=o||t.width,c=c||t.height,this.contents.blt(t,i,a,o,c,e,n,o,c)},Window_Base.prototype.drawPictureCut=function(t,e=0,n=0,i=0,a=0,o,c){let s=ImageManager.loadPicture(t);return o=o||s.width,c=c||s.height,this.contents.blt(s,i,a,o,c,e,n,o,c),s},Window_Base.prototype.drawLine=function(t,e,n=this.contents.width,i=2){},Window_Base.prototype.drawTextWrap=function(t,e,n,i=this.contents.width,a=0,o="left"){let c,s=[],l=[],r=[/^\n+/,/^[ ]+/,/^[^ |^\n]+/],d=t.length;for(let e=0;e<d;e++)for(let n=0;n<r.length;n++)(c=r[n].exec(t))&&(t=t.slice(c[0].length),e+=c[0].length-1,s.push(c[0]),l.push(n));let h=0,p=e,w=n,g=!1,x=this.contents.fontSize;for(let t=0;t<s.length;t++){let n=s[t],c=this.contents.measureTextWidth(n);((h+=c)>i||0===l[t])&&(p=e,w+=x+a,h=c,1!==l[t]&&0!==l[t]||(g=!0)),g||(this.contents.drawText(n,p,w,i,x,o),p+=c),g=!1}};{$dataCSV.npc=[];let t=$dataCSV.language;for(let e=0;e<t.Data.length;e++){let n=`./csv/npc/npc_${t.Data[e][0].trim()}.csv`;$Node.fs.existsSync(n)&&($dataCSV.npc[e]=new CSV(LoadFileUTF8Escaped(n)))}$dataCSV.npc.misc=new CSV(LoadFileUTF8Escaped("./csv/npc/npc_misc.csv"))}let $globalWindowNpc;function CreateWindowNpc(){$globalWindowNpc||($globalWindowNpc=new Window_Npc(25,4,836),SceneManager._scene.addWindow($globalWindowNpc))}function RemoveWindowNpc(){$globalWindowNpc&&($globalWindowNpc.destroy(),$globalWindowNpc=void 0)}function Window_Npc(){this.initialize.apply(this,arguments)}ImageManager.loadPicture("status_pussy"),ImageManager.loadPicture("status_womb"),Window_Npc.prototype=Object.create(Window_Base.prototype),Window_Npc.prototype.constructor=Window_Npc,Window_Npc.prototype.initialize=function(t,e,n){Window_Base.prototype.initialize.call(this,t,e,n,616),this.draw()},Window_Npc.prototype.draw=function(){this.contents.clear();let t=t=>$gameVariables._data[t]||0,e=t(1),n=$dataCSV.npc[e].Data,i=$dataCSV.npc.misc.Data,a=t(21)-10,o=e=>t(e+a),c=this.contents.width,s=(t,e,n)=>{let i=t,a=e,o=(c/2-31)/2;return i+=19,a+=7,this.contents.drawText(n[0].text,i,a,o,24,n[0].align),i+=o,this.contents.drawText(n[1].text,i,a,o,24,n[1].align),i+=o,this.contents.fillRect(i+11,a,2,28,"#FFFFFF"),i+=24,this.contents.drawText(n[2].text,i,a,o,24,n[2].align),i+=o,this.contents.drawText(n[3].text,i,a,o,24,n[3].align),{x:i,y:a+28+7}},l={x:0,y:0};this.contents.drawText(i[0][e],0,l.y,c,24,"center"),l.y+=40,this.contents.fillRect(0,l.y,c,2,"#FFFFFF"),l=s(0,48,[{text:`${i[1][e]}:`,align:"left"},{text:this.convertEscapeCharacters(n[a][0]),align:"center"},{text:`${i[2][e]}:`,align:"left"},{text:n[a][1],align:"center"}]),l=s(0,l.y,[{text:`${i[3][e]}:`,align:"left"},{text:n[a][2],align:"center"},{text:`${i[4][e]}:`,align:"left"},{text:n[a][3],align:"center"}]),l=s(0,l.y,[{text:`${i[5][e]}:`,align:"left"},{text:n[a][4],align:"center"},{text:"",align:"left"},{text:"",align:"center"}]),this.contents.fillRect(0,l.y,c,2,"#FFFFFF"),l.y+=12,this.contents.fontSize=20;this.drawTextWrap(this.convertEscapeCharacters(n[a][4+$gameVariables.value(92+$gameVariables.value(21))]),0,l.y,c),this.contents.fontSize=28,l.y+=104,this.contents.fillRect(0,l.y,c,2,"#FFFFFF"),l.y+=10,this.contents.drawText(`${i[6][e]}`,0,l.y,c,24,"center"),l.y+=40,this.contents.fillRect(0,l.y,c,2,"#FFFFFF"),l.y+=6,l=s(0,l.y,[{text:`${i[7][e]}:`,align:"left"},{text:o(302),align:"center"},{text:`${i[8][e]}:`,align:"left"},{text:o(102),align:"center"}]),(l=s(0,l.y,[{text:`${i[9][e]}:`,align:"left"},{text:o(402),align:"center"},{text:`${i[10][e]}:`,align:"left"},{text:o(502),align:"center"}])).y+=6,this.contents.fillRect(0,l.y,c,2,"#FFFFFF");let r=o(102)-1,d=o(1502),h=o(202);l.y+=8;let p=this.drawPictureCut("status_pussy",0,l.y,0,80*Math.min(r,2),80,80);l.x=84,this.contents.fontSize=20,this.drawTextWrap(this.convertEscapeCharacters(n[a][8+Math.min(r,2)]),l.x,l.y,c/2-84),l.x=c/2;let w=this.drawPictureCut("status_womb",l.x,l.y,80*h,80*d,80,80);this.redraw="requesting"===p._loadingState||"requesting"===w._loadingState,l.x+=84,this.drawTextWrap(this.convertEscapeCharacters(n[a][h?16:11+d]),l.x,l.y,c/2-84),this.contents.fontSize=28,l.y+=80,l.y+=8,this.contents.fillRect(0,l.y,c,2,"#FFFFFF"),l.y+=8,l.x=0,l=s(0,l.y,[{text:`${i[11][e]}:`,align:"left"},{text:o(902),align:"center"},{text:`${i[12][e]}:`,align:"left"},{text:`${t(601)}%`,align:"center"}])},Window_Npc.prototype.update=function(){this.redraw&&this.draw()},Window_Npc.prototype.refresh=function(){},Window_Npc.prototype.drawItem=function(){};