function PluginParams(n,a){let r=PluginManager.parameters(n)||{},e=Object.keys(r);for(let n=0;n<e.length;n++){let o=e[n];this[o]=PluginParams.Parse(r[o],a[o])}}PluginParams.PREFIX="MEO_",PluginParams.Parse=function(n,a){return{bool:n=>PluginParams.ToBoolean(n),boolean:n=>PluginParams.ToBoolean(n),number:n=>PluginParams.ToNumberFloat(n),int:n=>PluginParams.ToNumberInt(n),float:n=>PluginParams.ToNumberFloat(n),array:n=>PluginParams.ToJSON(n),json:n=>PluginParams.ToJSON(n),string:n=>n,undefined:n=>n}[a](n)},PluginParams.ToBoolean=function(n){return"true"===n.toLowerCase()},PluginParams.ToNumberInt=function(n){return parseInt(n)},PluginParams.ToNumberFloat=function(n){return parseFloat(n)},PluginParams.ToJSON=function(n){return JSON.parse(n)},new PluginParams("LogWindow",{X:"number",Y:"number",Width:"number",Height:"number",FontSize:"number",Margin:"number",Speed:"float",Acceleration:"float"});