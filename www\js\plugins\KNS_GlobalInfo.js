/*:
 * <AUTHOR>
 * @plugindesc ver.1.0.0 ゲーム全体のフラグを管理します
 * 
 * @target MV
 *
 * @help
 * 　クリア情報等のゲーム全体のフラグをファイルに出力し
 * 　読み書きを行います。
 * 　フラグファイルはJSON文字列形式で保存されるため、
 * 　それに対応するものであれば保存できます。
 * 　フラグの保存は通常セーブファイル保存時にのみ行われます。
 * 
 * 【提供するプラグインコマンド】
 * KNS_GlobalInfo read (フラグ名) (代入先変数ID)
 * 　代入先変数にフラグの値を代入します。
 * 
 * KNS_GlobalInfo write (フラグ名) (読み取り変数ID)
 * 　読み取り変数の値をフラグに代入します。
 * 
 * KNS_GlobalInfo write (フラグ名) eval (条件文)
 * 　条件文の返り値をフラグに代入します。
 * 
 * KNS_GlobalInfo save
 * 　フラグをファイルとして保存します。
 * 
 * ver.1.0.0(2023-07-26)
 * - デモ
 */

const KNS_GlobalInfo = {
	name: "KNS_GlobalInfo",
	param: null,
	globalFileId: -15, // -1未満であれば何でもいい

	lastFlag: null,
	init: function(){
		this.lastFlag = {};
		if (StorageManager.exists(this.globalFileId)){
			try{
				const obj = JsonEx.parse(StorageManager.load(this.globalFileId));
				if (obj && typeof obj === 'object'){
					this.lastFlag = obj;
				}else{
					console.error("[KNS_GlobalInfo]形式エラー", obj);
				}
			}catch (e){
				console.error("[KNS_GlobalInfo]読み込みエラー", e);
			}
		}
	},
	read: function(key){
		return this.lastFlag[key];
	},
	write: function(key, value){
		this.lastFlag[key] = value;
	},
	save: function(){
		try{
			StorageManager.save(this.globalFileId, JsonEx.stringify(this.lastFlag));
		}catch(e){
			console.error("[KNS_GlobalInfo]書き込みエラー", e);
		}
	}
};

(function(){
    //=======================================================
    // alias Game_Interpreter
    //=======================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_GlobalInfo.name){ return; }
        switch ((args[0] || "").toLowerCase()){
            case 'read':{
				$gameVariables.setValue(Math.floor(args[2]), KNS_GlobalInfo.read(args[1]));
                break;
            }
            case 'write':{
				let value;
				if (args[2] === 'eval'){
					value = eval(args[3]);
				}else{
					value = $gameVariables.value(Math.floor(args[2]));
				}
				KNS_GlobalInfo.write(args[1], value);
				break;
            }
			case 'save':{
				KNS_GlobalInfo.save();
				break;
			}
        }
    };

	//=====================================================
	// alias StorageManager
	//=====================================================
	const _StorageManager_localFilePath = StorageManager.localFilePath;
	StorageManager.localFilePath = function(savefileId) {
		if (savefileId === KNS_GlobalInfo.globalFileId){
			return this.localFileDirectoryPath() + "kns_global.rpgsave";
		}else{
			return _StorageManager_localFilePath.apply(this, arguments);
		}
	};
	
	const _StorageManager_webStorageKey = StorageManager.webStorageKey;
	StorageManager.webStorageKey = function(savefileId) {
		if (savefileId === KNS_GlobalInfo.globalFileId){
			return "KNS Global";
		}else{
			return _StorageManager_webStorageKey.apply(this, arguments);
		}
	};
	
	//=====================================================
	// DataManager
	//=====================================================
	const _DataManager_saveGameWithoutRescue = DataManager.saveGameWithoutRescue;
	DataManager.saveGameWithoutRescue = function(savefileId) {
		KNS_GlobalInfo.save();
		return _DataManager_saveGameWithoutRescue.apply(this, arguments);
	};

	//=====================================================
	// alias Scene_Boot
	//=====================================================
	const _Scene_Boot_create = Scene_Boot.prototype.create;
	Scene_Boot.prototype.create = function() {
		_Scene_Boot_create.apply(this, arguments);
		KNS_GlobalInfo.init();
	};
}).call(KNS_GlobalInfo);