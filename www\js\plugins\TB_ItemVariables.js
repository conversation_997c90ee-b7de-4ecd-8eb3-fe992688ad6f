//=============================================================================
// StoreItemID.js
//=============================================================================

/*:
 *
 * @help
 * This plugin stores the ID of the last used item in variable 273.
 * Use the plugin command 'StoreItemID' after using an item to store
 * the item's ID in variable 273.
 *
 * Usage:
 * Use an item, then use the plugin command 'StoreItemID'.
 * Check variable 273 to see the ID of the last used item.
 *
 * Note:
 * This plugin does not automatically run after each item use.
 * You must manually use the 'StoreItemID' command after each item use.
 */



(function() {

  var _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
  Game_Interpreter.prototype.pluginCommand = function(command, args) {
      _Game_Interpreter_pluginCommand.call(this, command, args);
      if (command === 'StoreItemID') {
          $gameVariables.setValue(273, $gameParty.lastItem().id);
      }
  };

  var _Game_Party_useItem = Game_Party.prototype.useItem;
  Game_Party.prototype.useItem = function(item) {
      _Game_Party_useItem.call(this, item);
      this.setLastItem(item);
  };

  Game_Party.prototype.setLastItem = function(item) {
      this._lastItem = item;
  };

  Game_Party.prototype.lastItem = function() {
      return this._lastItem;
  };

})();
