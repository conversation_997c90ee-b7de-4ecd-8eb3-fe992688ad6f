/*:
 * @plugindesc プラグインコマンドで動的にウィンドウを生成します
 * <AUTHOR>
 * 
 * @help
 * ■概要
 * 　プラグインコマンドで動的にウィンドウを生成します。
 * 　ウィンドウは同時に複数生成可能で、変数やマップ更新時に
 * 値も更新されます。
 * 
 * 　NPCシート等と連携する場合は「KNS_NpcData load」等の
 * プラグインコマンドを指定し変数を表示する形でご対応ください。
 * 
 * 
 * ■プラグインコマンド
 * 　プラグインコマンドでウィンドウを生成する際の流れは以下の通りです。
 * １．createでウィンドウサイズ指定
 * ２．makeLineで行数・列数を指定
 * ３．addItemでテキスト追加
 * ４．削除する際はremove、removeAll
 * 
 * 例）
 * KNS_CreateWindow create auto 0 10 512 auto
 * KNS_CreateWindow makeLine 2 1
 * KNS_CreateWindow addItem logOnly pri_trigger_love
 * KNS_CreateWindow makeLine 3 1
 * KNS_CreateWindow addItem valueOnly 463
 * KNS_CreateWindow makeLine 0.5 1
 * KNS_CreateWindow addItem horzLine 2 #ffffff88
 * KNS_CreateWindow makeLine 1 2
 * KNS_CreateWindow addItem logValue pm_event507 462
 * KNS_CreateWindow addItem logValue pm_event507 3
 * KNS_CreateWindow makeLine 1 3
 * KNS_CreateWindow addItem logValue pm_event507 4
 * KNS_CreateWindow addItem titleValue 5
 * KNS_CreateWindow addItem titleValue 6
 * 
 * 
 * 
 * KNS_CreateWindow create ID x y 横幅 高さ
 * 　サイズを指定しウィンドウを生成します。
 * 　生成されたウィンドウはIDで管理され、IDにautoを指定すると
 * 　「マップID_イベントID」で自動生成されます。
 * 　高さにautoを指定すると行数から自動で高さが計算されます。
 * 
 * 
 * KNS_CreateWindow makeLine 行数 列数
 * 　行数・列数を指定して最後に生成されたウィンドウに行を追加します。
 * 
 * 
 * KNS_CreateWindow addItem テキストタイプ 引数
 * 　最後に生成されたウィンドウに項目を表示します。
 * 　テキストタイプは以下のものから指定してください。
 * 
 * - empty      : 何も指定しません。
 * （引数：[なし]）
 * 
 * - valueOnly  : 変数の値を改行可で表示します。
 * （引数：変数ID）
 * 
 * - titleValue : 変数名と値を表示します。
 * （引数：変数ID）
 * 
 * - logValue   : ログシートで指定した項目名と値を表示します。
 * （引数：ログキー、変数ID）
 * 
 * - logOnly    : ログシートで指定した項目を改行可で指定します。
 * （引数：ログキー）
 * 
 * - horzLine   : 水平線を指定します。
 * （引数：太さ、カラー）
 * 
 * 
 * KNS_CreateWindow remove ID
 * 　IDで指定したウィンドウを削除します。
 * 
 * 
 * KNS_CreateWindow removeAll
 * 　すべてのウィンドウを削除します。
 * 
 */
const KNS_CreateWindow = {
    name: 'KNS_CreateWindow',
    reVariable: /\\v\[(\d+)\]/,
    parseVariable(arg){
        if (this.reVariable.test(arg)){
            return $gameVariables.value(Number(RegExp.$1));
        }else{
            return Number(arg);
        }
    },
    lastInfo: null
};

(function(){
    //==========================================
    // alias Game_Interpreter
    //==========================================
    Game_Interpreter.prototype.knsGenCreatedWindowId = function(id){
        if (id === 'auto'){
            return this._mapId + "_" + this._eventId;
        }else{
            return id;
        }
    }

    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_CreateWindow.name){ return; }
        switch (args[0].toLowerCase()){
            case 'create':{
                let id = this.knsGenCreatedWindowId(args[1]);
                let x = Number(args[2] || 0);
                let y = Number(args[3] || 0);
                let w = Number(args[4] || 0);
                let h = Number(args[5]) || 'auto';
                $gameSystem.knsAddCreatedWindow(id, x, y, w, h);
                KNS_CreateWindow.lastInfo = { id: id, row: -1, col: 0, };
                break;
            }
            case 'makeline':{
                let id = KNS_CreateWindow.lastInfo.id;
                KNS_CreateWindow.lastInfo.row += 1;
                KNS_CreateWindow.lastInfo.col = 0;
                let line = KNS_CreateWindow.lastInfo.row;
                let rows = Number(args[1] || 1);
                let cols = Number(args[2] || 1);
                const win = $gameSystem.knsGetCreatedWindow(id);
                if (win){
                    win.setRow(line, rows);
                    win.setCol(line, cols);
                    $gameSystem.knsRequestRefreshCreateWindow();
                }
                break;
            }
            case 'additem':{
                let id = KNS_CreateWindow.lastInfo.id;
                let line = KNS_CreateWindow.lastInfo.row;
                let i = KNS_CreateWindow.lastInfo.col;
                KNS_CreateWindow.lastInfo.col += 1;
                let textType = String(args[1]);
                let textParams = args.slice(2, args.length);
                const win = $gameSystem.knsGetCreatedWindow(id);
                if (win){
                    win.setItem(line, i, textType, textParams);
                    $gameSystem.knsRequestRefreshCreateWindow();
                }
                break;
            }
            case 'remove':{
                let id = this.knsGenCreatedWindowId(args[1]);
                $gameSystem.knsRemoveCreatedWindow(id);
                break;
            }
            case 'removeall':{
                $gameSystem.knsRemoveAllCreatedWindows();
                break;
            }
        }
    }


    //==========================================
    // alias Game_Map
    //==========================================
    const _Game_Map_requestRefresh = Game_Map.prototype.requestRefresh;
    Game_Map.prototype.requestRefresh = function(mapId) {
        _Game_Map_requestRefresh.apply(this, arguments);
        $gameSystem.knsRequestRefreshCreateWindow();
    };

    //==========================================
    // alias Game_System
    //==========================================
    Game_System.prototype.knsRequestRefreshCreateWindow = function(){
        this._knsRequestRefreshCreateWindow = true;
    }

    Game_System.prototype.knsGetAllCreatedWindows = function(){
        if (!this._knsCreatedWindows){ this._knsCreatedWindows = {}; }
        return this._knsCreatedWindows;
    }

    Game_System.prototype.knsRemoveAllCreatedWindows = function(){
        this._knsCreatedWindows = {};
        this.knsRequestRefreshCreateWindow();
    }

    Game_System.prototype.knsGetCreatedWindow = function(id){
        const obj = this.knsGetAllCreatedWindows();
        return obj[id] || null;
    }

    Game_System.prototype.knsAddCreatedWindow = function(id, x, y, w, h){
        const obj = this.knsGetAllCreatedWindows();
        obj[id] = new Game_KnsCreateWindow(id, x, y, w, h);
        this.knsRequestRefreshCreateWindow();
    }

    Game_System.prototype.knsRemoveCreatedWindow = function(id){
        const obj = this.knsGetAllCreatedWindows();
        if (obj[id]){ delete obj[id]; }
        this.knsRequestRefreshCreateWindow();
    }

    //==========================================
    // alias Scene_Map
    //==========================================
    const _Scene_Map_createDisplayObjects = Scene_Map.prototype.createDisplayObjects;
    Scene_Map.prototype.createDisplayObjects = function(){
        _Scene_Map_createDisplayObjects.apply(this, arguments);
        this._knsContainerCreatedWindow = new Container_KnsCreateWindow();
        this.addWindow(this._knsContainerCreatedWindow);
    }
}).call(KNS_CreateWindow);

//==========================================
// alias Game_KnsCreateWindow
//==========================================
function Game_KnsCreateWindow(){
    return this.initialize.apply(this, arguments)
}

Game_KnsCreateWindow.prototype.initialize = function(id, x, y, w, h) {
    this._id = id;
    this._x = x;
    this._y = y;
    this._width = w;
    this._height = h;
    this._lineInfos = [];
};

Game_KnsCreateWindow.prototype.calcHeight = function(){
    if (this._height === 'auto'){
        const pad = Window_Base.prototype.standardPadding.call(this);
        const height = Window_Base.prototype.lineHeight.call(this);
        return this._lineInfos.reduce(function(r, item){
            return r + item.row;
        }, 0) * height + pad * 2;
    }else{
        return this._height;
    }
}

Game_KnsCreateWindow.prototype.maxItems = function(){
    return this._lineInfos.length;
}

Game_KnsCreateWindow.prototype.getLineInfo = function(line){
    if (!this._lineInfos[line]){
        this._lineInfos[line] = {
            row: 1,
            col: 1,
            texts: {}
        };
    }
    return this._lineInfos[line];
}

Game_KnsCreateWindow.prototype.setRow = function(line, row){
    const info = this.getLineInfo(line);
    info.row = row;
}

Game_KnsCreateWindow.prototype.setCol = function(line, col){
    const info = this.getLineInfo(line);
    info.col = col;
}

Game_KnsCreateWindow.prototype.setItem = function(line, index, textType, textParams){
    const info = this.getLineInfo(line);
    info.texts[index] = { textType: textType, textParams: textParams }
}

Game_KnsCreateWindow.TEXT_TYPES = {
    empty      : 'empty',
    valueOnly  : 'valueOnly',
    titleValue : 'titleValue',
    logValue   : 'logValue',
    logOnly    : 'logOnly',
    horzLine   : 'horzLine',
};

Game_KnsCreateWindow.prototype.getTitle = function(text){
    switch (text.textType){
        case Game_KnsCreateWindow.TEXT_TYPES.empty:{
            return '';
        }
        case Game_KnsCreateWindow.TEXT_TYPES.valueOnly:{
            return $gameVariables.value(text.textParams[0]);
        }
        case Game_KnsCreateWindow.TEXT_TYPES.titleValue:{
            return $dataSystem.variables[text.textParams[0]];
        }
        case Game_KnsCreateWindow.TEXT_TYPES.logValue:
        case Game_KnsCreateWindow.TEXT_TYPES.logOnly:{
            let key = text.textParams[0];
            return $LogSheetCSV.Exist(key) ? $LogSheetCSV.Get(key) : '';
        }
    }
}

Game_KnsCreateWindow.prototype.getValue = function(text){
    switch (text.textType){
        case Game_KnsCreateWindow.TEXT_TYPES.empty:{
            return '';
        }
        case Game_KnsCreateWindow.TEXT_TYPES.valueOnly:{
            return '';
        }
        case Game_KnsCreateWindow.TEXT_TYPES.titleValue:{
            return $gameVariables.value(text.textParams[0]);
        }
        case Game_KnsCreateWindow.TEXT_TYPES.logValue:{
            return $gameVariables.value(text.textParams[1]);
        }
        case Game_KnsCreateWindow.TEXT_TYPES.logOnly:{
            return '';
        }
    }
}


//==========================================
// alias Container_KnsCreateWindow
//==========================================
class Container_KnsCreateWindow extends PIXI.Container{
    constructor(){
        super();
        this._knsWindows = [];
        this.refresh();
    }
    refresh(){
        const cur = Object.values($gameSystem.knsGetAllCreatedWindows());
        this._knsWindows = this._knsWindows.filter(function(win){
            if (cur.includes(win.knsInfo)){
                return true;
            }else{
                this.removeChild(win);
                return false;
            }
        }, this);
        cur.forEach(function(info){
            let found = this._knsWindows.find(function(win){
                return win.knsInfo === info;
            });
            if (!found){
                found = new Window_KnsCreateWindow(info);
                this._knsWindows.push(found);
                console.log(found);
            }
            found.refresh();
            this.addChild(found);
        }, this);
    }
    update(){
        if ($gameSystem._knsRequestRefreshCreateWindow){
            $gameSystem._knsRequestRefreshCreateWindow = false;
            this.refresh();
        }
    }
}

//==========================================
// alias Window_KnsCreateWindow
//==========================================
class Window_KnsCreateWindow extends Window_Base{
    initialize(knsInfo){
        super.initialize(
            knsInfo._x, knsInfo._y, knsInfo._width, knsInfo.calcHeight()
        );
        this.knsInfo = knsInfo;
        this.refresh();
    }
    refresh(){
        this.contents.clear();
        const max = this.knsInfo.maxItems();
        let y = 0;
        for (let i = 0; i < max; i++){
            const info = this.knsInfo.getLineInfo(i);
            const row = info.row;
            const col = info.col;
            const height = this.lineHeight() * row;

            const sep = 16;
            let width = Math.floor((this.contents.width - sep * (col - 1)) / col);
            Object.keys(info.texts).forEach(function(key){
                const x = key * (width + sep);
                const value = info.texts[key];
                if (value.textType === Game_KnsCreateWindow.TEXT_TYPES.horzLine){
                    const size  = value.textParams[0] || 2;
                    const color = value.textParams[1] || "#ffffff";
                    const padY = Math.floor((height - size) * 0.5);
                    this.contents.fillRect(0, y + padY, this.contents.width, size, color);
                }else if (
                    value.textType === Game_KnsCreateWindow.TEXT_TYPES.logOnly
                    || value.textType === Game_KnsCreateWindow.TEXT_TYPES.valueOnly
                ){
                    this.knsDrawTextAutoline(String(this.knsInfo.getTitle(value)), x, y, width + x);
                }else{
                    const titleText = this.knsInfo.getTitle(value);
                    const valueText = this.knsInfo.getValue(value);
                    let vw = this.textWidth(valueText);
                    let tw = width - vw;
                    this.drawText(titleText, x, y, tw, 'left');
                    this.drawText(valueText, x + tw, y, vw, 'right');
                }
            }, this);
            y += height;
        }
    }
}