// ======================
//  MyWindowTest.js
// ======================
/*:
 * @plugindesc Test Create Window
 * <AUTHOR>
 * 
 * @help
 * Plugin Command:
 * MyWindowTest show   #Open window.
 */

/*:ja
 * @plugindesc ウィンドウ作成テスト
 * <AUTHOR>
 * 
 * @help
 * プラグインコマンド:
 * MyWindowTest show    #ウィンドウを開く
 */

(function(){
    'use strict';

    var pluginName = "MyWindowTest";
    
    //  ----------プラグインコマンドの定義 ここから ----------
    var _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    
    Game_Interpreter.prototype.pluginCommand = function(command, args){
	_Game_Interpreter_pluginCommand.call(this, command, args);

	if(command === pluginName){
	    switch(args[0]){
	    case 'show':
                SceneManager.push(Scene_MyWindow);
                break;
	    }
	}
    };

    //  ----------プラグインコマンドの定義 ここまで ----------



    //  ----------Sceneの定義 ここから ----------
    function Scene_MyWindow(){
        this.initialize.apply(this, arguments);
    }

    //  自作Sceneの元になるSceneを定義
    Scene_MyWindow.prototype = Object.create(Scene_Base.prototype);
    Scene_MyWindow.prototype.constructor = Scene_MyWindow;

    //  Sceneを生成した時の処理(親クラスのinitializeメソッドに自分を渡して呼びだし)
    Scene_MyWindow.prototype.initialize = function(){
        Scene_Base.prototype.initialize.call(this);
    };
    
    // ウィンドウの作成
    Scene_MyWindow.prototype.create = function(){
	Scene_Base.prototype.create.call(this);

	this.createWindowLayer();

	this.createMainWindow();
	this._mainWindow.drawText($gameVariables.value(100)[0], 0, 0, this._mainWindow.width);
    };

    // メインウィンドウ作成処理
    Scene_MyWindow.prototype.createMainWindow = function(){
        var ww = 350;
        var wh = 100;
	
        var wx = (Graphics.width - ww) / 2;
        var wy = (Graphics.height - wh) / 2;

        this._mainWindow = new Window_Base(wx, wy, ww, wh);	
        this.addWindow(this._mainWindow);
    };

    // ----------Sceneの定義 ここまで ----------    

})();