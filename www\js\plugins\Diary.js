let $$$DEBUG,$diary=null;{let t=new QuickCache;t.Set("diary_icons",ImageManager.loadPicture("icons/diary_icons"));let e=new CSV(LoadFileUTF8Escaped("./csv/diary.csv"));function DiaryAddEntry(t,e,i,a,n,r){$diary.Add(t,e,i,a,n,r)}function DiaryAddEntryFromVar(t,e,i,a,n,r){$diary.Add(t||Var(186),e||Var(4),i||Var(283),a||Var(183),n||Var(182),r||Var(184))}function Diary(){this.Entries=[],this.Capacity=9999}function Window_Diary(){this.initialize.apply(this,arguments)}function Scene_Diary(){this.initialize.apply(this,arguments)}Diary.prototype.Add=function(t,e,i,a,n,r){this.Entries.push({icon:t,day:e,npc:i,how:a,where:n,comment:r})},Diary.prototype.Set=function(t){this.Entries=t},Diary.prototype.Clear=function(){this.Entries=[]},Window_Diary.prototype=Object.create(Window_Base.prototype),Window_Diary.prototype.constructor=Window_Diary,Window_Diary.prototype.initialize=function(e,i,a){Window_Base.prototype.initialize.call(this,0,0,Graphics.width,Graphics.height),this.PageIndex=0,this.Pages=Math.floor(($diary.Entries.length-1)/14),this.Images=[t.Get("diary_icons")],this.Redraw=!1,this.draw()},Window_Diary.prototype.draw=function(){let t=Var(1);this.contents.clear();for(let t=0;t<5;t++)this.contents.fillRect(180*t+60,0,2,Graphics.height-96,"#FFFFFF");this.contents.fillRect(0,32,Graphics.width-32,2,"#FFFFFF");for(let t=1;t<15;t++)this.contents.fillRect(0,60*t+32,Graphics.width-32,2,"#FFFFFF");let i=this.Images[0];$$$DEBUG=this,this.contents.fontSize=28,this.contents.textColor="#ffee22",this.contents.drawText(e.Data[1][t+1],60,0,180,24,"center"),this.contents.drawText(e.Data[2][t+1],240,0,180,24,"center"),this.contents.drawText(e.Data[3][t+1],420,0,180,24,"center"),this.contents.drawText(e.Data[4][t+1],600,0,180,24,"center"),this.contents.drawText(e.Data[5][t+1],780,0,462,24,"center"),this.contents.fontSize=16,this.contents.textColor="#ffffff";let a=[60,180,180,180,180,462],n=["icon","day","npc","how","where","comment"];for(let t=0;t<14;t++){let e=$diary.Entries.length,r=$diary.Entries[e-1-(14*this.PageIndex+t)];if(r){let e=60,o=60*t+50;this.drawBitmapCut(i,14,o-4,0,32*r.icon,32,32);for(let t=1;t<6;t++){let i=a[t],s=r[n[t]];this.contents.drawText(this.convertEscapeCharacters(""+s),e,o,i,24,"center"),e+=i}}}this.contents.textColor="#ffee22",this.contents.fontSize=22,this.contents.drawText(`${this.PageIndex+1}/${Math.max(this.Pages+1,1)}`,0,Graphics.height-32-48,Graphics.width-32,24,"center"),this.contents.textColor="#ffffff",this.contents.fontSize=18,this.contents.drawText(e.Data[6][t+1],Graphics.width-(Graphics.width/2.25+48)+20,Graphics.height-32-48+10,Graphics.width/2.25,32,"center"),this.contents.fontSize=28},Window_Diary.prototype.update=function(){this.Redraw&&this.draw()},Window_Diary.prototype.pageUp=function(){this.PageIndex<this.Pages&&(this.PageIndex++,this.draw())},Window_Diary.prototype.pageDown=function(){this.PageIndex>0&&(this.PageIndex--,this.draw())},Scene_Diary.prototype=Object.create(Scene_MenuBase.prototype),Scene_Diary.prototype.constructor=Scene_Diary,Scene_Diary.prototype.initialize=function(){Scene_MenuBase.prototype.initialize.call(this)},Scene_Diary.prototype.create=function(){Scene_MenuBase.prototype.create.call(this),this.DiaryWindow=new Window_Diary,this.addWindow(this.DiaryWindow)},Scene_Diary.prototype.update=function(){this.DiaryWindow.update(),Input.isTriggered("cancel")&&(AudioManager.playSe({name:"Cancel2",pan:0,pitch:100,volume:100}),SceneManager.pop()),(Input.isTriggered("right")||Input.isTriggered("up")||Input.isTriggered("pageup"))&&(AudioManager.playSe({name:"Cursor2",pan:0,pitch:100,volume:100}),this.DiaryWindow.pageDown(),this.DiaryWindow.draw()),(Input.isTriggered("left")||Input.isTriggered("down")||Input.isTriggered("pagedown"))&&(AudioManager.playSe({name:"Cursor2",pan:0,pitch:100,volume:100}),this.DiaryWindow.pageUp(),this.DiaryWindow.draw())};let i=Scene_Load.prototype.onLoadSuccess;Scene_Load.prototype.onLoadSuccess=function(){i.call(this),$gameSystem.diary&&$diary.Set($gameSystem.diary.entries)};let a=DataManager.saveGame;DataManager.saveGame=function(){return $gameSystem.diary={entries:$diary.Entries},a.apply(DataManager,arguments)};let n=DataManager.createGameObjects;function TestFill(){for(let t=0;t<50;t++)DiaryAddEntry(5,""+t,""+t,""+t,""+t,""+t);DiaryAddEntry(0,"","","","","Just a Comment 1"),DiaryAddEntry(0,"","","","","Just a Comment 2"),DiaryAddEntry(1,"Day 1","名も知らぬ男","ちんぽ見せ","\\n[1]","最悪ですわ！"),DiaryAddEntry(1,"Day 1","名も知らぬ男","ちんぽ見せ","\\n[1]","最\\c[1]悪で\\c[2]すわ！"),DiaryAddEntry(2,"Day 1","MEO","ぶっかけ","髪の毛","ありえませんわ！"),DiaryAddEntry(3,"Day 9999","TechnoBrake","噛みつき","おっぱい","おほおおおおおおおおおおおおおおおおおおお！")}DataManager.createGameObjects=function(){n.call(this),$diary.Clear()},$diary=new Diary}