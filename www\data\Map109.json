{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 25, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 25, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "ビーチナンパ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ビーチナンパ　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-beach_nanpa_bj\""]}, {"code": 108, "indent": 0, "parameters": ["3男A"]}, {"code": 408, "indent": 0, "parameters": ["4男B"]}, {"code": 408, "indent": 0, "parameters": ["5ボディ body#{num}"]}, {"code": 408, "indent": 0, "parameters": ["6陰毛"]}, {"code": 408, "indent": 0, "parameters": ["7腋毛"]}, {"code": 408, "indent": 0, "parameters": ["8腕セット"]}, {"code": 108, "indent": 0, "parameters": ["9おっぱい boobs#{num}"]}, {"code": 408, "indent": 0, "parameters": ["15ヘッド head#{num}-#{num2} num2 = headナンバー"]}, {"code": 408, "indent": 0, "parameters": ["16ヘアー hair#{num}-#{num2}"]}, {"code": 408, "indent": 0, "parameters": ["17head0-mouth"]}, {"code": 408, "indent": 0, "parameters": ["18フェイス h#{num}-face\"{num2} num =headナンバー　num2 = #23"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男A"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男B"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy2`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["おっぱいの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tits${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-hair${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マウス"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num}-mouth`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ビーチナンパ　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-beach_nanpa_sex\""]}, {"code": 108, "indent": 0, "parameters": ["1.男Ｂ"]}, {"code": 408, "indent": 0, "parameters": ["2.ヘッド"]}, {"code": 408, "indent": 0, "parameters": ["3.ヘアー"]}, {"code": 408, "indent": 0, "parameters": ["4.フェイス"]}, {"code": 408, "indent": 0, "parameters": ["8.ベース"]}, {"code": 408, "indent": 0, "parameters": ["9陰毛"]}, {"code": 108, "indent": 0, "parameters": ["10ペニスプッシー"]}, {"code": 408, "indent": 0, "parameters": ["11ペニスアヌス　射精差分は-cum"]}, {"code": 408, "indent": 0, "parameters": ["15男腕"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男B"]}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy2`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイス"]}, {"code": 355, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,150,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-pnies-pussy`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,150,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-pnies-anus`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,150,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-pnies-pussy-cum`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,150,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 3, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-pnies-anus-cum`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,150,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男腕"]}, {"code": 111, "indent": 0, "parameters": [1, 23, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy2-arms`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "ファーマー搾乳", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ファーマー搾乳"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["if ($milk === null) $milk = 0"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-milking\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg#{18}-#{}"]}, {"code": 408, "indent": 0, "parameters": ["3 cum#{40}"]}, {"code": 408, "indent": 0, "parameters": ["4 man#{$man}-lower"]}, {"code": 408, "indent": 0, "parameters": ["5 skin#{33}"]}, {"code": 408, "indent": 0, "parameters": ["6 bote#{33}"]}, {"code": 408, "indent": 0, "parameters": ["7 ah"]}, {"code": 108, "indent": 0, "parameters": ["8 chain"]}, {"code": 408, "indent": 0, "parameters": ["9 milk#{$milk}"]}, {"code": 408, "indent": 0, "parameters": ["10 macine"]}, {"code": 408, "indent": 0, "parameters": ["11 head#{33}"]}, {"code": 408, "indent": 0, "parameters": ["12 hair#{34}"]}, {"code": 408, "indent": 0, "parameters": ["13 face#{23}"]}, {"code": 108, "indent": 0, "parameters": ["14 nose pierce"]}, {"code": 408, "indent": 0, "parameters": ["15 ear tag"]}, {"code": 408, "indent": 0, "parameters": ["16 man#{$man}-upper"]}, {"code": 408, "indent": 0, "parameters": ["17 head accesory"]}, {"code": 408, "indent": 0, "parameters": ["20 saku"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(18)"]}, {"code": 655, "indent": 1, "parameters": ["nn = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["if (nn >= 2) nn = 1;"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-bg${n}-${nn}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cum${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男下"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-man${n}-lower`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-skin${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボテの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-bote${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["チェインの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-chain`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ミルクの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-milk${$milk}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マシンの表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$milk == 1"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-machine`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ノーズピアスの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-nose_pierce`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["イアータグの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 3, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-ear_tag`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男上"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-man${n}-upper`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドアクセサリの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-head-accessory`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["柵の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-saku`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "ヤリゾー馬車痴漢", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー馬車痴漢"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-carriage\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["2 mob3"]}, {"code": 408, "indent": 0, "parameters": ["3 yarizo"]}, {"code": 408, "indent": 0, "parameters": ["4 head"]}, {"code": 408, "indent": 0, "parameters": ["5 hair"]}, {"code": 408, "indent": 0, "parameters": ["6 upper"]}, {"code": 108, "indent": 0, "parameters": ["7 lower skin-dankai"]}, {"code": 408, "indent": 0, "parameters": ["8 ph"]}, {"code": 408, "indent": 0, "parameters": ["10cloth"]}, {"code": 408, "indent": 0, "parameters": ["15 face"]}, {"code": 408, "indent": 0, "parameters": ["16 mob temae2"]}, {"code": 408, "indent": 0, "parameters": ["17 mob temae1"]}, {"code": 108, "indent": 0, "parameters": ["18 squirt"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["奥のモブの表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mob3`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アッパーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-upper${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ロウアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if (num2 >= 3) num2 = 2;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-lower${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if (num2 >= 3) num2 = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tights${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["衣装"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["モブ手前"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mob2`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mob1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["（セックスシーン段階３のときに表示される）"]}, {"code": 108, "indent": 0, "parameters": ["潮"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "ヤリゾー？背後から乳揉み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["スケアクロウ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo_grabbing_tits\""]}, {"code": 108, "indent": 0, "parameters": ["３ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": ["４バッグ"]}, {"code": 408, "indent": 0, "parameters": ["１０へっど"]}, {"code": 408, "indent": 0, "parameters": ["１１ヘアー"]}, {"code": 408, "indent": 0, "parameters": ["１２フェイス"]}, {"code": 408, "indent": 0, "parameters": ["１３ボディ"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["バッグの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "ヤリゾーレベル２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl2　解放"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-lvl2-release\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 5}, {"id": 6, "name": "ヤリゾーLvl2 町中", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 5}, {"id": 7, "name": "ヤリゾーLvl2 ダンジョン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 5}, {"id": 8, "name": "ヤリゾーLvl2 キャンプ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl2　キャンプ　睡眠ケツコキ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-sleeping-ass_job\""]}, {"code": 108, "indent": 0, "parameters": ["3 body v33"]}, {"code": 408, "indent": 0, "parameters": ["6 tights"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth"]}, {"code": 408, "indent": 0, "parameters": ["8 futon (v39で進行管理） 0のときだけ表示 1で消去"]}, {"code": 408, "indent": 0, "parameters": ["9 bag"]}, {"code": 108, "indent": 0, "parameters": ["10 head v33"]}, {"code": 408, "indent": 0, "parameters": ["11 hair v34"]}, {"code": 408, "indent": 0, "parameters": ["12 face v23"]}, {"code": 408, "indent": 0, "parameters": ["13 guy (v39で進行管理） 2いじょうで表示"]}, {"code": 408, "indent": 0, "parameters": ["15 cut-in (v39で進行管理）2がちんぽ置き 3で射精"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["布団の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-futon`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer8);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["バッグの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy23`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin${num1}-${num2}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 5}, {"id": 9, "name": "酔っ払いの看病フェラセックス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["酔っ払いの看病フェラセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-nursing\""]}, {"code": 108, "indent": 0, "parameters": ["3 bed"]}, {"code": 408, "indent": 0, "parameters": ["4 man"]}, {"code": 408, "indent": 0, "parameters": ["5 cum"]}, {"code": 408, "indent": 0, "parameters": ["6 body pose#{pose}-skin#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["7 hair"]}, {"code": 408, "indent": 0, "parameters": ["8 arms (poseが0の場合のみ）"]}, {"code": 108, "indent": 0, "parameters": ["10 face"]}, {"code": 408, "indent": 0, "parameters": ["15 foot"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 355, "indent": 0, "parameters": ["n = 1"]}, {"code": 655, "indent": 0, "parameters": ["i = 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["while i < 21"]}, {"code": 655, "indent": 0, "parameters": ["eval(\"$layer#{i} = i\")"]}, {"code": 655, "indent": 0, "parameters": ["i += 1"]}, {"code": 655, "indent": 0, "parameters": ["n += 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-bed\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-guy\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = \"#{$e_name}-cum\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-pose#{num1}-body#{num2}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-pose#{num1}-hair#{num2}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["うでの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(24)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = \"#{$e_name}-pose#{num1}-arm#{num2}\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer8].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-pose#{num1}-face#{num2}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の足の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-guy-foot\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 10, "name": "カジノ・スロ客セックス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["カジノ・スロ客セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-casino-slot-sex\""]}, {"code": 108, "indent": 0, "parameters": ["2. guy"]}, {"code": 408, "indent": 0, "parameters": ["3. cum-bj"]}, {"code": 408, "indent": 0, "parameters": ["4.body0-0(skin-stage)"]}, {"code": 408, "indent": 0, "parameters": ["5 hair0-0(skin-stage)"]}, {"code": 408, "indent": 0, "parameters": ["6.b0-face0(stage-face)"]}, {"code": 408, "indent": 0, "parameters": ["10 cum-sex"]}, {"code": 108, "indent": 0, "parameters": ["15.cutin-bg"]}, {"code": 408, "indent": 0, "parameters": ["16.cutin-body"]}, {"code": 408, "indent": 0, "parameters": ["17.cutin-hair"]}, {"code": 408, "indent": 0, "parameters": ["18.cutin-face"]}, {"code": 408, "indent": 0, "parameters": ["19. waku"]}, {"code": 408, "indent": 0, "parameters": ["20.slot"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum-bj`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-b${num1}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示(まんこ）"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum-sex`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-bg`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-body${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [" $no = `${$e_name}-cutin-hair${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-face${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": [" $no = `${$e_name}-cutin-waku`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["スロットの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-slot`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-slot`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,30,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 11, "name": "ストーリー-ロシタ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ストーリー - ロシタ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-roshita\""]}, {"code": 108, "indent": 0, "parameters": ["5. base"]}, {"code": 408, "indent": 0, "parameters": ["6. hair"]}, {"code": 408, "indent": 0, "parameters": ["10. face"]}, {"code": 408, "indent": 0, "parameters": ["11. <PERSON><PERSON><PERSON><PERSON>"]}, {"code": 408, "indent": 0, "parameters": ["20. cut-in"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 355, "indent": 0, "parameters": ["n = 1"]}, {"code": 655, "indent": 0, "parameters": ["i = 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["while i < 21"]}, {"code": 655, "indent": 0, "parameters": ["eval(\"$layer#{i} = i\")"]}, {"code": 655, "indent": 0, "parameters": ["i += 1"]}, {"code": 655, "indent": 0, "parameters": ["n += 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-base#{num1}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-hair#{num1}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-face#{num1}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["加速線"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-kasoku\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = \"#{$e_name}-cutin#{num}\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 12, "name": "真面目な冒険者　３つ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["真面目な冒険者　ダブルフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-double_bj1\""]}, {"code": 108, "indent": 0, "parameters": ["2 base"]}, {"code": 408, "indent": 0, "parameters": ["3 hair"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth102"]}, {"code": 408, "indent": 0, "parameters": ["11 cloth82"]}, {"code": 408, "indent": 0, "parameters": ["12 arms#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["13 cloth102_arm"]}, {"code": 108, "indent": 0, "parameters": ["14 cock"]}, {"code": 408, "indent": 0, "parameters": ["15 finger#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["16 guys"]}, {"code": 408, "indent": 0, "parameters": ["17 face"]}, {"code": 408, "indent": 0, "parameters": ["20 breath"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102_arm`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["チンポの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["指の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-finger${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男たちの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ブレスの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-breath`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["真面目な冒険者　3some"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-3some1\""]}, {"code": 108, "indent": 0, "parameters": ["3 base"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["8 hair"]}, {"code": 408, "indent": 0, "parameters": ["10 face"]}, {"code": 408, "indent": 0, "parameters": ["11 anal_guy"]}, {"code": 408, "indent": 0, "parameters": ["15 cock"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アナルガイの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-anal_guy`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-anal_guy`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,60,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["チンポの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["真面目な冒険者　ケツ漏れ精子"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-cum_leaking_back\""]}, {"code": 108, "indent": 0, "parameters": ["2 man"]}, {"code": 408, "indent": 0, "parameters": ["4 base"]}, {"code": 408, "indent": 0, "parameters": ["5 hair"]}, {"code": 408, "indent": 0, "parameters": ["7 tights"]}, {"code": 408, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["15 steam"]}, {"code": 108, "indent": 0, "parameters": ["20 cutin"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["steamの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 13, "name": "ヤリゾー 司祭倒れ込み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーに倒れ込み"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-down_to\""]}, {"code": 108, "indent": 0, "parameters": ["2 man #"]}, {"code": 408, "indent": 0, "parameters": ["3 body"]}, {"code": 408, "indent": 0, "parameters": ["6 tights"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth"]}, {"code": 408, "indent": 0, "parameters": ["8 hair"]}, {"code": 408, "indent": 0, "parameters": ["10 face"]}, {"code": 108, "indent": 0, "parameters": ["12 hat"]}, {"code": 408, "indent": 0, "parameters": ["13 right hand"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["19 cock"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth102`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth82`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["帽子"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth92`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["右手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-right_hand${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 1}, {"id": 14, "name": "ヤリゾー 太ももコキ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー太ももコキ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-st_thighjob\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#"]}, {"code": 408, "indent": 0, "parameters": ["5 body"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["7 ah"]}, {"code": 408, "indent": 0, "parameters": ["10 tights 102"]}, {"code": 408, "indent": 0, "parameters": ["11 cloth 82"]}, {"code": 108, "indent": 0, "parameters": ["12 head"]}, {"code": 408, "indent": 0, "parameters": ["13 hair"]}, {"code": 408, "indent": 0, "parameters": ["15 face"]}, {"code": 408, "indent": 0, "parameters": ["16 cloth 92"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["17 cock#"]}, {"code": 108, "indent": 0, "parameters": ["18 cock-cloth82"]}, {"code": 408, "indent": 0, "parameters": ["20 cum1"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth102`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer10];"]}, {"code": 655, "indent": 0, "parameters": ["  $picture.show($no, 0, 0, 0, $s_w, $s_h, 255, 0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth82`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer11];"]}, {"code": 655, "indent": 0, "parameters": ["  $picture.show($no, 0, 0, 0, $s_w, $s_h, 255, 0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["帽子"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth92`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer16];"]}, {"code": 655, "indent": 0, "parameters": ["  $picture.show($no, 0, 0, 0, $s_w, $s_h, 255, 0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cock-cloth82`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer18];"]}, {"code": 655, "indent": 0, "parameters": ["  $picture.show($no, 0, 0, 0, $s_w, $s_h, 255, 0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 1}, {"id": 15, "name": "ヤリゾーレベル３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　解放　手マン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-yarizo-fj1\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["guy"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー右手"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo_hand_r`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー左手"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo_hand_l${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー体毛"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo_b_hair`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　解放　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-yarizo-sex1\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${num2}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num2}`;"]}, {"code": 655, "indent": 0, "parameters": ["if (num2 >= 1) $no = `${$e_name}-cock${num2}-s${skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　解放　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-yarizo-bj1\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　解放　おしっこ後手マンとアナルほじくり"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-yarizo-fj2\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${num2}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${num2}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${num2}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの手"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-hand${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(15);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　テント内からの隠しビュー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-yarizo-tent_sex\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg_upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameVariables.value(28) === 0) {"]}, {"code": 655, "indent": 0, "parameters": ["  alpha = 255"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["  alpha = 30"]}, {"code": 655, "indent": 0, "parameters": ["};"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　ケツ肉ぶいんドギー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-doggy_w_yari\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽバック"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cock_back`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(2);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボテ"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-bote${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(15);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ロール"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-roll`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(18);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["x-ray"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(19);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(20);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　乳引っ張りイラマチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-yarizo-dt1\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["おっぱいの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-boobs${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["背景"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-bg`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-cock`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ヘッド"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-head${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["髪の毛"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-hair${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["表情"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-face${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["湯気"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-steam`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["イき潮"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl３　宿屋廊下手マンとセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-yarizo-inn_lvl3a\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 111, "indent": 0, "parameters": [1, 29, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer4);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 111, "indent": 0, "parameters": [1, 29, 0, 1, 2]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer5);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["歯っと"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツte "]}, {"code": 111, "indent": 0, "parameters": [1, 29, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cloth102_hand`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer12);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["イき潮"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["stage = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["opa = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}-${stage}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,opa,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 7}, {"id": 16, "name": "ヤリゾーレベル４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl４　解放　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-lvl4\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["guy"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-protag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー右手"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["フレーム"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl４　スカート隠しセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-SkirtSex\""]}, {"code": 108, "indent": 0, "parameters": ["3 base0-0 skin-hair"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["guy"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 9}, {"id": 17, "name": "ヤリゾーレベル５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 11}, {"id": 18, "name": "ヤリゾーLvl3 町中", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー顔コき"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-yarizo-face_rubbing\""]}, {"code": 108, "indent": 0, "parameters": ["2 base{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["15 inmou"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 408, "indent": 0, "parameters": ["20 guy#{456} if stage == 0"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["口陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-inmou`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(20);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 7}, {"id": 19, "name": "ヤリゾーLvl4 町中", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ぴょんぴょんフェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-bj2\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["下着"]}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["if($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["帽子"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] != null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ヘッド加速"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-head_kasoku`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服　腕"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth_arms${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ぴょんぴょんセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-riding_sex2\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["帽子"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着"]}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッド加速"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-kasoku`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー宿屋ドギー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-pri-doggy_inn\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["15 cum#{cum}"]}, {"code": 408, "indent": 0, "parameters": ["18 guy#{456} alpha"]}, {"code": 408, "indent": 0, "parameters": ["20 cutin"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 4]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 1, "parameters": ["alpha = 255"]}, {"code": 655, "indent": 1, "parameters": ["if ($gameVariables.value(28)>=1){"]}, {"code": 655, "indent": 1, "parameters": ["alpha = 50 "]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(18);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(1619)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(20);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-ガニ股まんずりちんしゃぶ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-manzuriBJ\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["手"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["スチーム"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ここからカットイン"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["背景の表示"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutinBG`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinCock${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ベース"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinBase${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ヘアー"]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinHair${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["表情の表示"]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinFace${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["カットイン"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutinFrame`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer11);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer12);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer14);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer16);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フレーム"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-壁から出てるちんぽをファック"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-wallFuckSlumInn\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["xray"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["xray"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer10);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["壁"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-wall`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フレーム"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 9}, {"id": 20, "name": "ヤリゾーLvl5 町中", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーLvl5　首絞めっクス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-choking_sex\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 cum #{cum}"]}, {"code": 408, "indent": 0, "parameters": ["13 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["18 bikusen"]}, {"code": 408, "indent": 0, "parameters": ["20 xray if x-ray >= 1"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー宿屋 土下座"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-pri-dogeza\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#{456} if stage >= 1"]}, {"code": 408, "indent": 0, "parameters": ["5 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["6 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["10 ph if ph >= 1"]}, {"code": 408, "indent": 0, "parameters": ["18 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(2);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー宿屋騎乗位"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-pri-riding_inn\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#{guy}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["8 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 108, "indent": 0, "parameters": ["13 cock"]}, {"code": 408, "indent": 0, "parameters": ["14 xray"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,50,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["n =$gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー宿屋ラストシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-pri-hiding_inn\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["5 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["20 overray"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オーバーレイ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-overray`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-POV 壁の穴から覗くセックス　スラム宿屋"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-peekingSlumInn\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアー下レイヤーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair_lower${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["xray"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["xray"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer16);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 355, "indent": 0, "parameters": ["n =$gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${n}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["壁の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-wall${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["フレーム"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 11}, {"id": 21, "name": "監獄-告解室イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["監獄　告解室　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-prison-confession-bj\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["2 cock#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 maku"]}, {"code": 408, "indent": 0, "parameters": ["4 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["5 hyottoko #{skin} if stage >= 1"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth102"]}, {"code": 108, "indent": 0, "parameters": ["11 cloth82"]}, {"code": 408, "indent": 0, "parameters": ["15 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["16 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["17 cloth92"]}, {"code": 408, "indent": 0, "parameters": ["20 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["幕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-maku`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ひょっとこの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-hyottoko${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ストッキングの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["監獄　告解室　壁尻"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-prison-confession-kabejiri\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg0"]}, {"code": 408, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 maku"]}, {"code": 408, "indent": 0, "parameters": ["4 anus#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["11 cock#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["15 guy#{456} if 1602 == 1 delete"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["幕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-maku`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アナルの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-anus${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 4]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer11);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["監獄　告解室　Gangbang"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-prison-confession-gangbang\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg0"]}, {"code": 408, "indent": 0, "parameters": ["2 cum_floor if cum >=3"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["5 anus#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["6 face#{face}"]}, {"code": 108, "indent": 0, "parameters": ["10 cloth92"]}, {"code": 408, "indent": 0, "parameters": ["11 cock_pussy"]}, {"code": 408, "indent": 0, "parameters": ["12 cum#{cum}"]}, {"code": 408, "indent": 0, "parameters": ["20shadow"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フロアの上の精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum_floor`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock_pussy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-shadow`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 21, "y": 1}, {"id": 22, "name": "港町", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-3P-セイラー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-3some-sailor\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["足の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-foot${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["alpha = 0"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(28) === 0) {"]}, {"code": 655, "indent": 0, "parameters": ["  alpha = 255"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["  alpha = 30"]}, {"code": 655, "indent": 0, "parameters": ["};"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy2`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["X-ray"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-3P-海賊"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-3some-pirates\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["x-ray"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["if (num >= 2) num = 1;"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-ギャンブル"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-gambling\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arm${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ダイス"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-dice${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["x-ray"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["if (num >= 2) num = 1;"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 1}, {"id": 23, "name": "王都", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都歓楽街　素股"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-is\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["10 hair #{hair}"]}, {"code": 408, "indent": 0, "parameters": ["11 face#{face}"]}, {"code": 108, "indent": 0, "parameters": ["13 headgear"]}, {"code": 408, "indent": 0, "parameters": ["15 cock#{456}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["16 cum"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都歓楽街　アナルセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-anal_sex\""]}, {"code": 108, "indent": 0, "parameters": ["2 head#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["4 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["5 cloth (head gear)"]}, {"code": 408, "indent": 0, "parameters": ["6 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["7 ph"]}, {"code": 108, "indent": 0, "parameters": ["8 anal#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["15 anal_juice if stage>=1"]}, {"code": 408, "indent": 0, "parameters": ["16 cum if cum >= 1"]}, {"code": 408, "indent": 0, "parameters": ["19 cock#{456} if stage>=1"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["頭の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["アナルの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-anal${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アナルジュースの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-anal_juice`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cock${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都歓楽街　種付けプレス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-matting_press\""]}, {"code": 108, "indent": 0, "parameters": ["2 head#{skin}-#{pose}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}-#{pose}"]}, {"code": 408, "indent": 0, "parameters": ["4 face#{face} if face == 0"]}, {"code": 408, "indent": 0, "parameters": ["5 cloth (head)"]}, {"code": 408, "indent": 0, "parameters": ["10 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["15 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["16 guy#{456}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["頭の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["opa = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,opa,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都歓楽街　魔改造司祭服セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-makaizou\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 ph"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["5 cloth(tights)"]}, {"code": 408, "indent": 0, "parameters": ["6 cloth(cloth)"]}, {"code": 408, "indent": 0, "parameters": ["10 hair"]}, {"code": 108, "indent": 0, "parameters": ["11 face"]}, {"code": 408, "indent": 0, "parameters": ["12 cloth (hat)"]}, {"code": 408, "indent": 0, "parameters": ["15 hand#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["16 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 408, "indent": 0, "parameters": ["19 akume"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82x`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["右手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h, 255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["アクメの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-akume`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都スラム　ちんしゃぶ屋"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-chinshabu\""]}, {"code": 108, "indent": 0, "parameters": ["2 base"]}, {"code": 408, "indent": 0, "parameters": ["3 ah"]}, {"code": 408, "indent": 0, "parameters": ["4 hair"]}, {"code": 408, "indent": 0, "parameters": ["5 face"]}, {"code": 408, "indent": 0, "parameters": ["10 hat"]}, {"code": 408, "indent": 0, "parameters": ["11 hand#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["19 guys"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["右手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男達の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h, 255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都スラム　トリプルフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-triple_bj\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["右手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h, 255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都スラム　トラッシュボックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-trash_can\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オプションの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1616)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["Flies"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-flies`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h, 255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イチャラブ(寝取らせ） -　感覚遮断"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-kankaku\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.erasePicture($layer2)"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 3, 4]}, {"code": 355, "indent": 2, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-guy_lower${num1}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フロア"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-floor`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕タイツ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102_arms`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッド"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアー"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["表情の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer14)"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num1}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男　上レイヤー"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy_upper${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェード"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-fade`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 3}, {"id": 24, "name": "シルエット", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["シルエットシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-silhouette\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-scene${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 25, "name": "タイタン・ステップ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせ ディーラー　（クエスト：ポイズンピン"]}, {"code": 408, "indent": 0, "parameters": ["ク）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-ts_pp_ntr1\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num =$gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-bg#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-guy#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-base#{$skin}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $skin = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-hair#{$hair}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-hat\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-face#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = \"#{$e_name}-squirt#{num}\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-cum#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　冒険者ＮＴＲ　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["MapEvent.call(109, 25, 2)"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-TSADVNTR_BJ\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-clothing82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ロッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-rod`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["---------------------カットイン----------------------"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cutin_base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cutin_clothing82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cutin_face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　冒険者ＮＴＲ　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["MapEvent.call(109, 25, 3)"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-TSADVNTR_SEX\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-clothing92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,80,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　冒険者ＮＴＲ　宿屋"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["MapEvent.call(109, 25, 4)"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-TSADVNTR_INN\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["BGの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 6, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [18, 18, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [18, 18, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["BG(上）の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 4, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-BG_top`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フェードの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-fade`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　冒険者ＮＴＲ　ファイナルシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["MapEvent.call(109, 25, 5)"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-TSADVNTR_FINAL\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の腕"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_arms`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-protag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["加速線の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-kasokusen`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirting`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer19)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 26, "name": "ヤリゾーlvl4　ダンジョン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 9}, {"id": 27, "name": "ヤリゾーlvl4 キャンプ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["テント　セックス　フェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-yarizo-tent_bj\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 108, "indent": 0, "parameters": ["うで　"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-arm${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-tights`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["口"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mouth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["テント"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tent`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 9}, {"id": 28, "name": "ヤリゾーlvl5　ダンジョン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 11}, {"id": 29, "name": "ヤリゾーlvl5 キャンプ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["テント　ヤリゾー　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-yarizo-tent_sex\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(109, 27, 2)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["テント"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tent`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["尻フリ"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ass`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer20)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 11}, {"id": 30, "name": "エネミー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭-オーク"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-orc_sex1\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腹"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-belly${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["オーク"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-orc`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["x-ray"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 3}, {"id": 31, "name": "冒険者の宿営地", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 3}, {"id": 32, "name": "ダークエルフの集落", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["粗チンフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-sdcBlowjob\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["DEの拠点路地裏セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-PriPro-Double\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["仮面パーティ - 見知らぬ男"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-maskedSexStrange\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1000`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マスクの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-mask${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["仮面パーティ - ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pir-MaskedSexYarizo\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マスクの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-mask${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["仮面パーティ - 司祭ｘVP　フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-PriVP-maskedBJ\""]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理　VP"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-PriVP-maskedBJ-VP\""]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マスクの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-mask`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理　司祭"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-PriVP-maskedBJ-Pri\""]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マスクの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-mask`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理　共通"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["ブラーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-blur`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["仮面パーティ - 司祭ｘVP　セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-PriVP-maskedGangBang\""]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 3}]}