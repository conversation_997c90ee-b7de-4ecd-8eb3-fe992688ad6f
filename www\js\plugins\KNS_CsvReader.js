/*:
 * @plugindesc ver.1.3.1 CSVから文章・データベースを言語ごとに切り替えます
 * <AUTHOR>
 *
 * @help
 * 【ver.1.2.1 DB_Galleryについて】
 * csv/DB_Gallery.csvの行形式は以下のように設定してください。
 * ・キャラID(数値)
 * ・シートID(数値)
 * ・初期進行度(数値)
 * ・サムネ画像(ファイル名、拡張子不要)
 * ・ソートID(数値、未指定の場合はシートIDに対応)
 * ・備考
 *
 * 【仕様】
 * csvフォルダから以下のcsvファイルを抽出し言語ごとの挙動を変更します。
 *
 * ●データベースを変数１の値に応じて自動的に書き換える
 * - DB_Actor.csv
 * - DB_Class.csv
 * - DB_State.csv
 * - DB_Item.csv
 * - DB_Weapon.csv
 * - DB_Armor.csv
 * - DB_Skill.csv
 * - DB_Variable.csv
 * - DB_Enemy.csv(ver.1.1.1から)
 * - DB_Gallery.csv(ver.1.2.0から)
 * （自動で変更されるため、RGSS版で使っていた
 * 　DataManager.kns_load_translate_db関数は
 * 　不要になりました）
 *
 * ●グローバル変数を追加、内部的に変更処理を行う
 * - QuestList.csv($csvQuestList)
 * - logsheet.csv($csvLogSheets)
 * - commonSheet.csv($csvCommons)
 * - sheet.csv($csvSheets)
 *
 * ●念のため配列形式で取得
 * - status($csv_erotic_status)
 * - window_ero_status_simple($csv_erotic_status_simple)
 *
 *
 * 【文章提供機能】
 * 　文章中に以下の記述のある場合、指定された
 * テキストをCSVから引用します。
 *
 * event_data_base_text_(テキストID1)
 * event_data_base_text_(テキストID1)-(テキストID2)
 * ・sheet.csvからマップID、イベントID、ページID、テキストIDの
 * 　合う文章を抽出し表示します（連番指定が可能）
 *
 * common_database_text_(テキストID1)
 * common_database_text_(テキストID1)-(テキストID2)
 * ・commonSheet.csvからコモンイベントID、テキストIDの
 * 　合う文章を抽出し表示します（連番指定が可能）
 *
 *
 * 【スクリプト提供機能】
 * set_mlog("_log_database_text_（ログキー）")
 * ・logsheets.csvのA列のキーとマッチするものを抽出し
 * 　ログウィンドウに表示します。
 *
 * val_in_database(変数ID, "（ログキー）")
 * ・logsheets.csvのA列のキーとマッチするものを抽出し
 * 　変数に代入します。
 *
 * var_from_sheet(変数ID, マップID, イベントID, ページID, テキストID, line)
 * ・sheet.csvからマップID、イベントID、ページID、テキストIDの
 * 　合う文章を抽出し変数に代入します。
 *
 * var_from_common(変数ID, コモンID, テキストID, line)
 * ・commonSheet.csvからコモンイベントID、テキストIDの
 * 　合う文章を抽出し変数に代入します。
 *
 * ●言語変更時のアクター名更新処理
 * 言語切り替え時以下のスクリプトを実行すると
 * DB_Actorに指定された名前に更新されます。
 *
 * // 言語変更時アクター名更新処理
 * $gameActors._data.forEach(function(a){
 *   a && a.setName(a.actor().name);
 * });
 *
 * ■更新履歴
 * ver.1.0.1(2023-05-13)
 * - event_data_base_text_を呼び出した際ページIDを一つ前のものを
 *   参照する不具合があったため修正しました。
 * - var_from_sheet, var_from_commonの処理に失敗する不具合を
 *   修正しました。
 *
 * ver.1.0.2(2023-05-14)
 * - var_from_sheet, var_from_commonのline引数に応じて
 *   行数を指定して文章を抽出するよう変更しました。
 * - メッセージ文章の改行前に挟まれたスラッシュを除去し
 *   単純な改行のみを取得するよう変更しました。
 *
 * ver.1.0.3(2023-05-23)
 * - 場所移動やページ切り替えでログ番号がずれる仕様を
 *   修正しました。
 *
 * ver.1.1.0(2023-05-28)
 * - CSVの読み込み方式をXMLリクエストからfsに変更し
 *   同期読み込みを実装しました。
 * - MultiLanguage.jsに合わせ、デプロイ後wwwからではなく
 *   ルートディレクトリのcsvフォルダを読み込むよう変更しました。
 *   これによりwww/csvフォルダは不要となります。
 *
 * ver.1.1.1(2023-06-02)
 * - DB_Enemy.csvを追加しエネミーデータの名前を変換できるようにしました。
 *
 * ver.1.1.2(2023-06-04)
 * - KNS_EventCallから呼び出した際、呼び出されたイベントの
 *   マップ・イベント・ページIDから文章が取得されるよう変更しました。
 *
 * ver.1.1.3(2023-07-01)
 * - commonSheet.csv内でコモンIDが重複していると
 *   前の行のコモンテキストが上書きされる不具合を修正しました。
 *
 * ver.1.1.4(2023-07-03)
 * - マップ、コモンイベントで文章範囲を指定する際、ソートによる
 *   大小関係が乱れる不具合を修正しました。
 * 
 * ver.1.2.0(2023-07-26)
 * - DB_Gallery.csvを追加し、ギャラリーのデータをCSVで管理できるようにしました。
 * 
 * ver.1.2.1(2023-09-11)
 * - DB_Galleryにソート機能を追加
 * 
 * ver.1.2.2(2023-11-02)
 * - profile.csvを追加
 * 
 * ver.1.2.3(2024-01-22)
 * - EnemyData.csvを追加
 * 
 * ver.1.2.4(2024-03-30)
 * - 文章表示時に同時に選択肢表示も行う
 * 
 * ver.1.3.0(2024-03-31)
 * - NpcData.csvを追加
 * - CSV内からのコモンイベント呼び出し時に元のイベントリスト呼び出しのため
 * 　イベントコマンドをディープコピーする
 * 
 * ver.1.3.1(2024-04-03)
 * - スクリプトからのコモンイベント呼び出しがうまくいかなかったので
 * 　イベントコマンド生成で対応
 */


const KNS_CsvReader = {
	name: "KNS_CsvReader",
	getLanguageId: function(){
		return $gameVariables ? $gameVariables.value(1) : 0;
	},
	loadDatabase: function(){
		this.databases.forEach(function(data){
			const url = './csv/' + data[0] + '.csv';
			try{
				this._knsData = new CSV(LoadFileUTF8Escaped(url)).Data;
				data[1]();
			}catch (e){
				console.error(e);
				DataManager._errorUrl = url;
			}
		}, this);
		this._knsData = null;
	},
	translate: function(csv, startIndex, index, languagePad){
		return csv[startIndex + index + languagePad * this.getLanguageId()] || "";
	},
	RE_ID: /\$id=(\d+)/,
	onLoadObject: function(dataName, list, startIndex, languagePad){
		const data = window[dataName];
		this._knsData.forEach(function(csv){
			if (this.RE_ID.test(csv[0]) && data[RegExp.$1]){
				const info = {};
				list.forEach(function(obj){
					info[obj[0]] = { get: this.translate.bind(this,
						csv, startIndex, obj[1], languagePad
					) };
				}, this);
				Object.defineProperties(data[RegExp.$1], info);
			}
		}, this);
	},
	onLoadVariables: function(){
		const info = {};
		this._knsData.forEach(function(csv){
			if (this.RE_ID.test(csv[0])){
				info[RegExp.$1] = { get: this.translate.bind(this, csv, 1, 0, 1) };
			}
		}, this);
		Object.defineProperties($dataSystem.variables, info);
	},
	onLoadLogSheet: function(){
		$csvLogSheets = {};
		this._knsData.forEach(function(csv){
			if (csv[0].length > 0){ $csvLogSheets[csv[0]] = csv; }
		}, this);
	},
	RE_QUEST_ID: /\$quest_id=(\d+)/,
	onLoadQuestList: function(){
		const reVar = /\\v\[(.+?)\]/gi;
		function parseEc(text){
			let result = "";
			for (let i = 0; i < text.length; i++) {
				if (text.slice(i, i + 4) === '@ec(') {
					let nest = 1, script = "";
					for (i += 4; i < text.length; i++) {
						if (text[i] === '(') {
							nest++;
						} else if (text[i] === ')') {
							nest--;
							if (nest <= 0) { break; }
						}
						script += text[i];
					}
					result += eval( script.replace(reVar, (_, id) => $gameVariables.value(id)) );
				} else {
					result += text[i];
				}
			}
			return result;
		};
	
		$csvQuestList = {};
		this._knsData.forEach(function(csv){
			if (!this.RE_QUEST_ID.test(csv[0])){ return; }
			const id = Number(RegExp.$1) || 0;
			const obj = {
				id: id,
				var_id: Math.floor(csv[1] || 0),
				difficulty: Math.floor(csv[2] || 0),
				release_conds: csv[3],
				map_id: Math.floor(csv[4] || 0),
				no_retire: (csv[5] || "").toLowerCase() === 'true'
			};
			$csvQuestList[id] = obj;

			[
				"title", "map_name", "client",
				// ID=0をシステム文字列取得のために使う
				...(id === 0 ? ["rewards", "progress"] : ["_rewards", "_progress"]),
				"short_desc", "long_desc"
			].forEach(function(name, i){
				Object.defineProperty(obj, name, {
					get: KNS_CsvReader.translate.bind(KNS_CsvReader, csv, 6, i, 7)
				});
			}, this);
			if (id === 0){ return; }

			/* rewards */
			Object.defineProperty(obj, "rewards", {
				get: function(){
					const rewards = [];
					this._rewards.replace(/\$([IWAG])(\d+)(?:X(\d+))?/gi, function(_base, listType, id, size){
						let list = null;
						switch(listType.toUpperCase()){
							case "I":{ list = $dataItems; break; }
							case "W":{ list = $dataWeapons; break; }
							case "A":{ list = $dataArmors; break; }
							case "G":{ break; }
						}
						rewards.push([list === null ? Math.floor(id) : list[id], Math.floor(size) || 0]);
						return _base;
					});
					return rewards;
				}
			});

			/* progress */
			Object.defineProperty(obj, "progress", {
				get: function(){
					const items = [];
					this._progress.replace(/\$progress(\d+)=(.*)/gi, function(_base, id, text){
						items.push([id, parseEc(text)]);
						return _base;
					});
					return items;
				}
			});

		}, this);
	},
	onLoadCommons: function(){
		$csvCommons = {};
		let curLine = {};
		const reCommon = /\$common(\d+)/, reText = /\$text(\d+)/;
		this._knsData.forEach(function(csv){
			if (reCommon.test(csv[0])){
				let id = RegExp.$1;
				curLine = $csvCommons[id] || {};
				$csvCommons[id] = curLine;
			}else if (reText.test(csv[0])){
				let id = RegExp.$1;
				curLine[id] = csv;
			}
		}, this);
	},
	onLoadSheet: function(){
		$csvSheets = {};
		const	reMap = /\$map(\d+)/, reEvent = /\$event(\d+)/,
				rePage = /\$page(\d+)/, reText = /\$text(\d+)/;
		let mapId = -1, eventId = -1, pageId = -1;
		this._knsData.forEach(function(csv){
			if (csv[0].length === 0){
				return;
			}else if (reMap.test(csv[0])){
				mapId = RegExp.$1;
				if (!$csvSheets[mapId]){ $csvSheets[mapId] = {}; };
			}else if (reEvent.test(csv[0])){
				eventId = RegExp.$1;
			}else if (rePage.test(csv[0])){
				pageId = RegExp.$1;
			}else if (reText.test(csv[0])){
				let textId = RegExp.$1;
				if (!$csvSheets[mapId][eventId]){ $csvSheets[mapId][eventId] = {}; };
				if (!$csvSheets[mapId][eventId][pageId]){
					$csvSheets[mapId][eventId][pageId] = {};
				};
				$csvSheets[mapId][eventId][pageId][textId] = csv;
			}
		}, this);
	},
	onLoadNPC: function(){
		$csvNpcs = {};
		this._knsData.forEach(function(csv, i){
			if (i === 0){ return; }
			let mapId = csv[1];
			let eventId = csv[2];
			let pageId = csv[3];
			if (!$csvNpcs[mapId]){ $csvNpcs[mapId] = {}; }
			if (!$csvNpcs[mapId][eventId]){ $csvNpcs[mapId][eventId] = {}; }
			$csvNpcs[mapId][eventId][pageId] = csv;
		}, this);
	},
	onLoadGallery: function(){
        $csvGalleryList = {};
		this._knsData.forEach(function(csv){
            let [charaId, sceneId, initProgress, imgPath, sortId] = csv;
            charaId = Number(charaId);
            sceneId = Number(sceneId);
			sortId  = sortId.length === 0 ? NaN : Number(sortId);
            if (!charaId || !sceneId){ return; }
            if (!$csvGalleryList[charaId]){
                $csvGalleryList[charaId] = { "allData": [] };
            }
            const scene = {
                "charaId":      Math.floor(charaId),
                "sceneId":      Math.floor(sceneId),
                "initProgress": Math.floor(initProgress),
                "imgPath":      imgPath,
				"sortId":       isNaN(sortId) ? (sceneId + 16777216) : sortId 
            };
            $csvGalleryList[charaId][sceneId] = scene;
            $csvGalleryList[charaId].allData.push(scene);
		}, this);
        Object.values($csvGalleryList).forEach(function(item){
            item.allData.sort(function(a, b){
				return a.sortId - b.sortId;
			});
        });
    },
	onLoadProfile: function(){
		$csv_profile = {};
		this._knsData.forEach(function(item){
			const key = item[0];
			if (key.length === 0){ return; }
			const texts = item.slice(1, item.length);
			if ($csv_profile[key]){
				$csv_profile[key].push(texts);
			}else{
				$csv_profile[key] = [texts];
			}
		}, this);
	},
	onLoadEnemyData: function(){
		$csv_EnemyData = {};
		this._knsData.forEach(function(item){
			const key = item[0];
			if (key.length === 0){ return; }
			$csv_EnemyData[key] = item;
		}, this);
	},
	getProfileTerm: function(key, value){
		let index = -1;
		const terms = $csv_profile[key];
		for (let i = terms.length - 1; i >= 0; i--){
			if (terms[i][0] <= value){
				index = i;
				break;
			}
		}
		if (index !== -1){
			return this.translate($csv_profile[key][index], 1, 0, 1);
		}else{
			return "";
		}
	},
	onLoadAsGlobal: function(key){
		window[key] = this._knsData;
	},
	getLogText: function(key){
		if ($csvLogSheets[key]){ return this.translate($csvLogSheets[key], 1, 0, 1); }
		return null;
	},
	getEventMessage: function(map_id, event_id, page_id, text_id){
		if (
			$csvSheets[map_id] &&
			$csvSheets[map_id][event_id] &&
			$csvSheets[map_id][event_id][page_id] &&
			$csvSheets[map_id][event_id][page_id][text_id]
		){
			const csv = $csvSheets[map_id][event_id][page_id][text_id];
			return [csv[1], this.translate(csv, 2, 0, 1).replace(
				/\/\n/g, "\n"
			)];
		}else{
			return null;
		}
	},
	getCommonMessage: function(common_id, text_id){
		if ($csvCommons[common_id] && $csvCommons[common_id][text_id]){
			const csv = $csvCommons[common_id][text_id];
			return [csv[1], this.translate(csv, 2, 0, 1).replace(
				/\/\n/g, "\n"
			)];
		}else{
			return null;
		}
	},
	getConvertWindow: function(){
		if (!this._knsConvertWindow){
			this._knsConvertWindow = new Window_Base(0, 0, 1, 1);
		}
		return this._knsConvertWindow;
	},
	parseEscapeCharacter: function(text){
		return this.getConvertWindow().convertEscapeCharacters(text);
	}
};

(function(){
	this.databases = [
		// var, src, onload
		["DB_Actor", this.onLoadObject.bind(this, "$dataActors", [
			["name", 0], ["nickname", 1], ["description", 2],
		], 1, 3)],
		["DB_Class", this.onLoadObject.bind(this, "$dataClasses", [["name", 0]], 2, 1)],
		["DB_State", this.onLoadObject.bind(this, "$dataStates", [["name", 0]], 1, 1)],
		["DB_Item", this.onLoadObject.bind(this, "$dataItems", [["name", 0], ["description", 1]], 2, 2)],
		["DB_Weapon", this.onLoadObject.bind(this, "$dataWeapons", [["name", 0], ["description", 1]], 2, 2)],
		["DB_Armor", this.onLoadObject.bind(this, "$dataArmors", [["name", 0], ["description", 1]], 2, 2)],
		["DB_Skill", this.onLoadObject.bind(this, "$dataSkills", [["name", 0], ["description", 1]], 2, 2)],
		["DB_Variable", this.onLoadVariables.bind(this)],
		["DB_Enemy", this.onLoadObject.bind(this, "$dataEnemies", [["name", 0]], 1, 1)],
		["DB_Gallery", this.onLoadGallery.bind(this)],
		["DB_NPC", this.onLoadNPC.bind(this)],

		["profile", this.onLoadProfile.bind(this)],
		["logsheet", this.onLoadLogSheet.bind(this)],
		["QuestList", this.onLoadQuestList.bind(this)],
		["commonSheet", this.onLoadCommons.bind(this)],
		["sheet", this.onLoadSheet.bind(this)],
		["status", this.onLoadAsGlobal.bind(this, "$csv_erotic_status")],
		["window_ero_status_simple", this.onLoadAsGlobal.bind(this, "$csv_erotic_status_simple")],
		["EnemyData", this.onLoadEnemyData.bind(this)],
	];

	window.val_in_database = function(var_id, key){
		const text = KNS_CsvReader.getLogText(key);
		if (text !== null){
			$gameVariables.setValue(var_id, KNS_CsvReader.parseEscapeCharacter(text));
		}else{
			console.log(`[${key}]存在しないログが指定されました！`)
		}
	};

	window.var_from_sheet = function(var_id, map, event, page, text_id, line=-1){
		const text = KNS_CsvReader.getEventMessage(map, event, page, text_id);
		if (text !== null){
			$gameVariables.setValue(var_id, KNS_CsvReader.parseEscapeCharacter(
				line === -1 ? text[1] : text[1].split("\n")[line] || ""
			));
		}else{
			console.log(`[${[map, event, page]}]存在しないイベントテキストが指定されました！`)
		}
	};

	window.var_from_common = function(var_id, common, text_id, line=-1){
		const text = KNS_CsvReader.getCommonMessage(common, text_id);
		if (text !== null){
			$gameVariables.setValue(var_id, KNS_CsvReader.parseEscapeCharacter(
				line === -1 ? text[1] : text[1].split("\n")[line] || ""
			));
		}else{
			console.log(`[${[common, text_id]}]存在しないコモンテキストが指定されました！`)
		}
	};

	/*
	window.set_mlog = function (str) {
		if ($LogWindow){
			if (/^\_?log_database_text_(.+)/.test(str)){
				const text = KNS_CsvReader.getLogText(RegExp.$1);
				if (text !== null){ $LogWindow.Add(text); }
				return;
			}
			$LogWindow.Add(str);
		}
	};
	*/

	//==========================================================
	// alias Game_Event
	//==========================================================
	Game_Event.prototype.knsGetMessageMapId = function(){
		return this._mapId;
	}

	//==========================================================
	// alias Game_Interpreter
	//==========================================================
	const _Game_Interpreter_setup = Game_Interpreter.prototype.setup;
	Game_Interpreter.prototype.setup = function(list, eventId) {
		_Game_Interpreter_setup.apply(this, arguments);
		if (this._list){
			this._knsCommonEventId = $dataCommonEvents.findIndex(function(ce){
				return ce && ce.list === list;
			}, this);
			this._list = JsonEx.makeDeepCopy(this._list);
		}else{
			this._knsCommonEventId = -1;
		}
		const event = $gameMap.event(eventId);
		if (this.isOnCurrentMap() && event){
			this._knsMessageMapId = event.knsGetMessageMapId();
			this._knsPageIndex = event._pageIndex + 1;
		}else{
			this._knsMessageMapId = this._mapId;
			this._knsPageIndex = 0;
		}
	};

	const _Game_Interpreter_setupChild = Game_Interpreter.prototype.setupChild;
	Game_Interpreter.prototype.setupChild = function(list, eventId,
		mapId=this._knsMessageMapId, page_id=this._knsPageIndex
	) {
		_Game_Interpreter_setupChild.apply(this, arguments);
		this._childInterpreter._knsMessageMapId = mapId;
		this._childInterpreter._knsPageIndex = page_id;
	};

	KNS_CsvReader.RE_MESSAGE_CALL = /event_data_base_text_(\d+)(?:-(\d+))?/;
	KNS_CsvReader.RE_COMMON_CALL  = /common_database_text_(\d+)(?:-(\d+))?/;
	const _Game_Interpreter_command101 = Game_Interpreter.prototype.command101;
	Game_Interpreter.prototype.command101 = function(){
		if (!$gameMessage.isBusy()) {
			let data = null;
			const line = this._list[this._index + 1].parameters[0];
			if (KNS_CsvReader.RE_MESSAGE_CALL.test(line)){
				let min = Math.floor(RegExp.$1);
				let max = Math.floor(RegExp.$2 || min);
				[min, max] = [min, max].sort(function(a, b){ return a - b; });
				data = [];
				for (let i = min; i <= max; i++){
					const text = KNS_CsvReader.getEventMessage(
						this._knsMessageMapId, this._eventId, this._knsPageIndex, i
					);
					if (text !== null){
						data.push(text);
					}else{
						console.log(`[${
							[this._knsMessageMapId, this._eventId, this._knsPageIndex, i]
						}]存在しないイベントテキストが指定されました！`)
					}
				}
			}else if (KNS_CsvReader.RE_COMMON_CALL.test(line)){
				let min = Math.floor(RegExp.$1);
				let max = Math.floor(RegExp.$2 || min);
				[min, max] = [min, max].sort(function(a, b){ return a - b; });
				let id = this._knsCommonEventId;
				if (id !== -1){
					data = [];
					for (let i = min; i <= max; i++){
						const text = KNS_CsvReader.getCommonMessage(id, i);
						if (text !== null){
							data.push(text);
						}else{
							console.log(`[${[id, i]}]存在しないコモンテキストが指定されました！`)
						}
					}
				}
			}
			if (data !== null){
				let startIndex = this._index;
				let indent = this._list[startIndex].indent;
				while (this.nextEventCode() === 401) { this._index++; }
				if (data.length > 0){
					const list = [];
					data.forEach(function(datum){
						datum[0].replace(
							/\$s\(\d+\)\s*==\s*(true|false)/g, function(_, a, b){
								list.push({ code: 121, indent: indent, parameters: [Number(a), Number(a), b === "true"], knsInserted: true });
								return "";
							}
						).replace(
							/\$v\[(\d+)\]\s*=\s*(-?\d+)/g, function(_, a, b){
								list.push({ code: 122, indent: indent, parameters: [Number(a), Number(a), 0, 0, Number(b)], knsInserted: true });
								return "";
							}
						).replace(
							/\$c\((.+?)\)/g, function(_, a){
								list.push({ code: 117, indent: indent, parameters: [Number(a)], knsInserted: true });
								return "";
							}
						);
						list.push(
							{ code: 101, indent: indent, parameters: this._params, knsInserted: true },
							{ code: 401, indent: indent, parameters: [datum[1]], knsInserted: true }
						);
					}, this);
					let endIndex = this._index;
					this._list.splice(startIndex, endIndex - startIndex + 1, ...list);
					this._index = startIndex;
					return false;
				}
			}
		}
		return _Game_Interpreter_command101.apply(this, arguments);
	}


	//==========================================================
	// alias Scene_Boot
	//==========================================================
	const _Scene_Boot_start = Scene_Boot.prototype.start;
	Scene_Boot.prototype.start = function() {
		KNS_CsvReader.loadDatabase();
		_Scene_Boot_start.apply(this, arguments);
	};

	//==========================================================
	// alias DataManager
	//==========================================================
	Object.defineProperty(DataManager, "kns_load_translate_db", {
		get: function(){
			console.log("DataManager.kns_load_translate_dbは廃止されたため不要な記述です");
			return function(){};
		}
	})
}).call(KNS_CsvReader);