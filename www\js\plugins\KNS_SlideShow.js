/*:
 * @plugindesc 汎用的なスライドウィンドウを実装
 * <AUTHOR>
 * 
 * @param maxItems
 * @text 最大スライド数
 * @type number
 * @default 5
 * 
 * @param startY
 * @text 初期Y軸
 * @type number
 * @default 64
 * 
 * @param width
 * @text 横幅
 * @type number
 * @default 360
 * 
 * @param height
 * @text 一行の高さ
 * @type number
 * @default 36
 * 
 * @param gradWidth
 * @text グラデーション幅
 * @type number
 * @default 40
 * 
 * @param gradColor1
 * @text グラデーション色１
 * @type string
 * @default #00000088
 * 
 * @param gradColor2
 * @text グラデーション色２
 * @type string
 * @default #00000000
 * 
 * 
 * @param textPadX
 * @text テキストパディングX
 * @type number
 * @default 4
 * 
 * @param textPadY
 * @text テキストパディングY
 * @type number
 * @default 4
 * 
 * @param textMarginY
 * @text テキストマージンY
 * @type number
 * @default 4
 * 
 * @param fontSize
 * @text フォントサイズ
 * @type number
 * @default 24
 * 
 * 
 * @param imageX
 * @text 画像X
 * @type number
 * @default 24
 * 
 * @param imageY
 * @text 画像Y
 * @type number
 * @default 4
 * 
 * @param imagePaddingX
 * @text 画像パディングX
 * @type number
 * @default 4
 * 
 * @help
 * ■プラグインコマンド
 * KNS_SlideShow show (key) (position) (time) (maxLine) (image)
 * 　文章キーに対応するテキストを表示する
 * 
 * key: 文章キー(logsheet)を指定
 * position: left|rightで位置を指定
 * time: -1のとき永続、0以上のときはフレーム分表示する
 * maxLine: 最大行。-1のときは改行に応じて自動で指定
 * image: img/systemフォルダから画像を指定、省略可
 * 
 * 例）KNS_SlideShow show mob_7 left 180 5
 * 
 * KNS_SlideShow remove
 * 　スライド表示をすべて隠す
 */
const KNS_SlideShow = {
    name: "KNS_SlideShow",
    param: null,
    knsLoadImage: function(image){
        return ImageManager.loadSystem(image);
    }
};
(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.maxItems = Number(this.param.maxItems);
    this.param.startY = Number(this.param.startY);
    this.param.width = Number(this.param.width);
    this.param.height = Number(this.param.height);
    this.param.gradColor1 = String(this.param.gradColor1);
    this.param.gradColor2 = String(this.param.gradColor2);
    this.param.gradWidth = Number(this.param.gradWidth);
    this.param.textPadX = Number(this.param.textPadX);
    this.param.textPadY = Number(this.param.textPadY);
    this.param.textMarginY = Number(this.param.textMarginY);
    this.param.fontSize = Number(this.param.fontSize);
    this.param.imageX = Number(this.param.imageX);
    this.param.imageY = Number(this.param.imageY);
    this.param.imagePaddingX = Number(this.param.imagePaddingX);

    //=======================================================
    // alias Game_Interpreter
    //=======================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_SlideShow.name){ return; }
        switch (args[0].toLowerCase()){
            case 'show':{
                let key = args[1];
                let text;
                if (key && $LogSheetCSV.Exist(key)){
                    text = $LogSheetCSV.Get(key) || "";
                }else{
                    console.error(`[${KNS_SlideShow.name}]存在しないキーが指定されました（${key}）`)
                    return;
                }

                let pos = args[2] ? String(args[2]) : "left";

                let time = Number(args[3]);
                if (isNaN(time)){ time = -1; }

                let maxLine = Number(args[4]);
                if (isNaN(maxLine)){ maxLine = -1; }

                let image = String(args[5] || "");
                if (image.length > 0){
                    const bmp = KNS_SlideShow.knsLoadImage(image);
                    bmp.addLoadListener(function(){
                        $gameSystem.knsReserveSlideShow(
                            text, pos, time, maxLine, image
                        );
                    });
                    this.setWaitMode('image');
                }else{
                    $gameSystem.knsReserveSlideShow(
                        text, pos, time, maxLine, image
                    );
                }
                break;
            }
            case 'remove':{
                $gameSystem.knsRemoveSlideShow();
                break;
            }
        }
    }

    //=======================================================
    // alias Game_System
    //=======================================================
    Game_System.prototype.knsGetSlideShowList = function(){
        if (!this._knsSlideShowList){ this._knsSlideShowList = []; }
        return this._knsSlideShowList;
    }

    Game_System.prototype.knsReserveSlideShow = function(
        text, positionType, time, maxLine, image=""
    ){
        let id = this._knsDynamicSlideShowId || 0;
        this._knsDynamicSlideShowId = id + 1;
        const list = this.knsGetSlideShowList();
        list.push({
            id: id,
            text: text,
            positionType: positionType,
            time: time,
            maxLine: maxLine,
            image: image
        });
        if (list.length > KNS_SlideShow.param.maxItems){
            list.shift();
        }
    }

    Game_System.prototype.knsRemoveSlideShow = function(){
        const list = this.knsGetSlideShowList();
        list.length = 0;
    }

    Game_System.prototype.knsUpdateSlideShow = function(){
        const list = this.knsGetSlideShowList();
        let changed;
        list.forEach(function(info){
            if (info.time > 0){
                if (--info.time === 0){
                    if (!changed){ changed = []; }
                    changed.push(info.id);
                }
            }
        });
        if (changed){
            this._knsSlideShowList = list.filter(function(info){
                return !changed.includes(info.id);
            });
        }
    }

    //=======================================================
    // alias Scene_Map
    //=======================================================
    const _Scene_Map_createDisplayObjects = Scene_Map.prototype.createDisplayObjects;
    Scene_Map.prototype.createDisplayObjects = function(){
        _Scene_Map_createDisplayObjects.apply(this, arguments);
        this._knsSlideShowSpritesets = ["left", "right"].map(function(type){
            const sp = new Spriteset_KnsSlideShow(type);
            this.addChild(sp);
            return sp;
        }, this);
    }

    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function(){
        _Scene_Map_start.apply(this, arguments);
        this._knsSlideShowSpritesets.forEach(function(sp){
            sp.knsStart();
        });
    }
}).call(KNS_SlideShow)

//=======================================================
// new Spriteset_KnsSlideShow
//=======================================================
class Spriteset_KnsSlideShow extends Window_Base{
    initialize(positionType){
        let width = this.knsWindowWidth();
        super.initialize(0, 0, width, Graphics.height);
        this.padding = 0;
        this._knsSprites = [];
        this._knsPositionType = positionType;
        if (positionType === "left"){
            this.x = 0;
        }else{
            this.x = Graphics.width - width;
        }
        this.y = KNS_SlideShow.param.startY;
        this.knsPreloadBitmaps();
        this.opacity = 0;
        this.contentsOpacity = 0;
    }
    knsStart(){
        this.knsUpdateSlideShowList(true);
    }
    knsWindowWidth(){
        return KNS_SlideShow.param.width + KNS_SlideShow.param.gradWidth;
    }
    update(){
        super.update();
        $gameSystem.knsUpdateSlideShow();
        this.knsUpdateSlideShowList(false);
    }
    knsPreloadBitmaps(){
        const list = $gameSystem.knsGetSlideShowList();
        list.forEach(function(info){
            if (info.image && info.image.length > 0){
                KNS_SlideShow.knsLoadImage(info.image);
            }
        });
    }
    knsUpdateSlideShowList(forced){
        let y = 0;
        const list = $gameSystem.knsGetSlideShowList();
        // appear item on list
        list.forEach(function(info){
            if (this._knsPositionType !== info.positionType){
                return;
            }
            let sp = this._knsSprites.find(function(sp){
                return sp._knsId === info.id;
            });
            if (sp){
                sp._knsTargetY = y;
            }else{
                sp = new Sprite_KnsSlideShow(
                    this, y, this.knsWindowWidth(),
                    info.id, info.text, info.maxLine, info.image,
                    this._knsPositionType, forced
                );
                this.knsAddSlide(sp);
            }
            y += sp.bitmap.height + KNS_SlideShow.param.textMarginY;
        }, this);
        // disappear item not on list
        for (let i = 0; i < this._knsSprites.length; i++){
            const sp = this._knsSprites[i];
            if (!list.some(function(info){ return info.id === sp._knsId })){
                sp.knsSetMotion("disappear");
                if (sp.opacity <= 0){
                    this.knsRemoveSlide(sp);
                }
            }
        }
    }
    lineHeight(){ return KNS_SlideShow.param.height; }
    standardFontSize(){ return KNS_SlideShow.param.fontSize; }
    knsAddSlide(sp){
        this.addChild(sp);
        this._knsSprites.push(sp);
    }
    knsRemoveSlide(sp){
        this.removeChild(sp);
        this._knsSprites.splice(this._knsSprites.indexOf(sp), 1);
    }
    knsCreateTextBitmap(text, image, maxLine){
        this.contents.clear();
        let x = 0;
        let y = 0;
        if (image && image.length > 0){
            x += KNS_SlideShow.param.imageX;
            y += KNS_SlideShow.param.imageY;
            const bmp = KNS_SlideShow.knsLoadImage(image);
            this.contents.blt(bmp, 0, 0, bmp.width, bmp.height, x, y);
            x += bmp.width;
            x += KNS_SlideShow.param.imagePaddingX;
        }
        let height = this.knsDrawTextAutoline(text, x, y, KNS_SlideShow.param.width);
        return (maxLine !== -1 ?
            this.lineHeight() * maxLine : height
        ) + KNS_SlideShow.param.textPadY;
    }

}

//=======================================================
// new Sprite_KnsSlideShow
//=======================================================
class Sprite_KnsSlideShow extends Sprite{
    initialize(parent, y, width, id, text, maxLine, image, posType, forced){
        super.initialize();
        this.position.set(0, y);
        this._knsPositionType = posType;
        this._knsId = id;
        this._knsTargetY = y;
        
        let height = parent.knsCreateTextBitmap(text, image, maxLine);
        let color1, color2, gx, mx;
        if (this._knsPositionType === "left"){
            gx = KNS_SlideShow.param.width;
            mx = 0;
            color1 = KNS_SlideShow.param.gradColor1;
            color2 = KNS_SlideShow.param.gradColor2;
        }else{
            gx = 0;
            mx = KNS_SlideShow.param.gradWidth;
            color1 = KNS_SlideShow.param.gradColor2;
            color2 = KNS_SlideShow.param.gradColor1;
        }
        const bmp = new Bitmap(width + KNS_SlideShow.param.gradWidth, height);
        bmp.fillRect(mx, 0, KNS_SlideShow.param.width, height, KNS_SlideShow.param.gradColor1);
        bmp.gradientFillRect(gx, 0, KNS_SlideShow.param.gradWidth, height, color1, color2);
        bmp.blt(parent.contents,
            0, 0, KNS_SlideShow.param.width, height,
            mx + KNS_SlideShow.param.textPadX, KNS_SlideShow.param.textPadY
        );
        this.bitmap = bmp;

        if (!forced){
            this.knsSetMotion("appear");
            this.knsUpdateMotion();
        }
    }
    knsSetMotion(type){
        if (!this._knsMotionInfo || this._knsMotionInfo.type !== type){
            this._knsMotionInfo = { type: type, index: 0 };
        }
    }
    update(){
        super.update();
        if (this.y !== this._knsTargetY){
            this.y += (this._knsTargetY - this.y) / 4;
            if (Math.abs(this.y - this._knsTargetY) < 1){
                this.y = this._knsTargetY;
            }
        }
        this.knsUpdateMotion();
    }
    knsUpdateMotion(){
        if (!this._knsMotionInfo){ return; }
        switch (this._knsMotionInfo.type){
            case "appear":{
                if (++this._knsMotionInfo.index >= 12){
                    this.x = 0;
                }else{
                    let x = this._knsPositionType === "left" ? -1 : +1;
                    this.x = (12 - this._knsMotionInfo.index) * x * 2;
                }
                break;
            }
            case "disappear":{
                this.x += this._knsPositionType === "left" ? -1 : +1;
                this.opacity -= 12;
                break;
            }
        }
    }
}