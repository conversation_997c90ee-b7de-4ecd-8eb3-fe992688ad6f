/*:
 * @plugindesc ver.1.0.1 DreamX_RandomPrefixesSuffixesの直下においてください。
 * <AUTHOR>
 * 
 * @help
 * ■変数に対象アイテムの接頭・接尾・オリジナルIDを代入
 * KNS_EquipPrefixSuffixes GetPrefix (W|A) (対象アイテムID) (変数ID)
 * KNS_EquipPrefixSuffixes GetSuffix (W|A) (対象アイテムID) (変数ID)
 * KNS_EquipPrefixSuffixes GetOriginal (W|A) (対象アイテムID) (変数ID)
 * 
 * ■対象アイテムに接頭・接尾を指定し新しいIDを変数に代入
 * KNS_EquipPrefixSuffixes SetPrefix (W|A) (対象アイテムID) (合成アイテムID) (変数ID)
 * KNS_EquipPrefixSuffixes SetSuffix (W|A) (対象アイテムID) (合成アイテムID) (変数ID)
 * ※変数・アイテムIDを\v[n]と指定することで変数の値から指定可能です
 * 
 * ■更新履歴
 * ver.1.0.1(2023-12-18)
 * - 名前による逆算機能を追加
 */

const KNS_EquipPrefixSuffixes = {
    name: "KNS_EquipPrefixSuffixes",
    param: null,
    reVariable: /\\v\[(\d+)\]/i,
    parseVariable: function(number){
        if (this.reVariable.test(number)){
            return $gameVariables.value(Number(RegExp.$1)) || 0;
        }else{
            return Number(number) || 0;
        }
    },
    getItemType: function(item){
        if (DataManager.isWeapon(item)){
            return $dataWeapons;
        }else if(DataManager.isArmor(item)){
            return $dataArmors;
        }else{
            return $dataItems;
        }
    },
    getOriginalNameIndex: function(item, list){
        const name = list[item.baseItemId].name;
        let i = 0;
        for (; i < item.name.length; i++){
            let j = 0;
            for (; j < name.length; j++){
                if (item.name[i] === name[j]){
                    i++;
                }else{
                    break;
                }
            }
            if (j === name.length){ return i; }
        }
        return -1;
    },
    getPreSuffixItemByName: function(item, isPrefix){
        // このプラグイン導入前のDreamX_RandomPrefixesSuffixesでは
        // 合成前のアイテムを記録していないので名前から逆算する。
        // 導入後に合成されたアイテムにはこの関数をつかわない。
        const list = this.getItemType(item);
        let i = this.getOriginalNameIndex(item, list);
        if (i === -1){ return null; }
        let targetName;
        if (isPrefix){
            const name = list[item.baseItemId].name;
            targetName = item.name.slice(0, i - name.length);
        }else{
            targetName = item.name.slice(i);
        }
        if (targetName.length === 0){
            return null;
        }
        let shortTargetName;
        if (isPrefix){
            shortTargetName = targetName.slice(0, targetName.length - 1);
        }else{
            shortTargetName = targetName.slice(1);
        }
        return list.find(function(item){
            if (item && item.name.length > 0){
                if (item.meta.prefixSuffixNoNameSpace){
                    return item.name === targetName;
                }else{
                    return item.name === shortTargetName;
                }
            }
            return false;
        }) || null;
    }
};

(function(){
    //==============================================================================
    // alias Game_Interpreter
    //==============================================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command === KNS_EquipPrefixSuffixes.name) {
            const isWeapon = args[1].toUpperCase() === 'W';
            const list = isWeapon ? $dataWeapons : $dataArmors;
            switch (args[0]) {
                case 'GetPrefix':{
                    const itemId = KNS_EquipPrefixSuffixes.parseVariable(args[2]);
                    const varId  = KNS_EquipPrefixSuffixes.parseVariable(args[3]);
                    const item = DreamX.RandomPrefixSuffix.knsGetPrefixItem(list[itemId]);
                    $gameVariables.setValue(varId, item ? item.id : 0);
                    break;
                }
                case 'GetSuffix':{
                    const itemId = KNS_EquipPrefixSuffixes.parseVariable(args[2]);
                    const varId  = KNS_EquipPrefixSuffixes.parseVariable(args[3]);
                    const item = DreamX.RandomPrefixSuffix.knsGetSuffixItem(list[itemId]);
                    $gameVariables.setValue(varId, item ? item.id : 0);
                    break;
                }
                case 'GetOriginal':{
                    const itemId = KNS_EquipPrefixSuffixes.parseVariable(args[2]);
                    const varId  = KNS_EquipPrefixSuffixes.parseVariable(args[3]);
                    const item = DreamX.RandomPrefixSuffix.knsGetOriginalItem(list[itemId]);
                    $gameVariables.setValue(varId, item ? item.id : 0);
                    break;
                }

                case 'SetPrefix':{
                    // targetItemID itemID
                    const targetId = KNS_EquipPrefixSuffixes.parseVariable(args[2]);
                    const compId = KNS_EquipPrefixSuffixes.parseVariable(args[3]);
                    const newItem = DreamX.RandomPrefixSuffix.knsSetPrefixItem(list[targetId], list[compId]);
                    $gameVariables.setValue(
                        KNS_EquipPrefixSuffixes.parseVariable(args[4]),
                        newItem ? newItem.id : 0
                    );
                    break;
                }
                case 'SetSuffix':{
                    const targetId = KNS_EquipPrefixSuffixes.parseVariable(args[2]);
                    const compId = KNS_EquipPrefixSuffixes.parseVariable(args[3]);
                    const newItem = DreamX.RandomPrefixSuffix.knsSetSuffixItem(list[targetId], list[compId]);
                    $gameVariables.setValue(
                        KNS_EquipPrefixSuffixes.parseVariable(args[4]),
                        newItem ? newItem.id : 0
                    );
                    break;
                }
            }
        }
    };


    //==============================================================================
    // alias DreamX.RandomPrefixSuffix
    //==============================================================================
    DreamX.RandomPrefixSuffix.knsGetOriginalItem = function(item){
        if (!item){ return null; }
        const itemId = item.baseItemId ? item.baseItemId : item.id;
        return KNS_EquipPrefixSuffixes.getItemType(item)[itemId];
    }

    DreamX.RandomPrefixSuffix.knsGetPrefixItem = function(item){
        if (item && item.knsPrefixId){
            return KNS_EquipPrefixSuffixes.getItemType(item)[item.knsPrefixId];
        }else if(item && item.baseItemId){
            return KNS_EquipPrefixSuffixes.getPreSuffixItemByName(item, true);
        }else{
            return null;
        }
    }

    DreamX.RandomPrefixSuffix.knsGetSuffixItem = function(item){
        if (item && item.knsSuffixId){
            return KNS_EquipPrefixSuffixes.getItemType(item)[item.knsSuffixId];
        }else if(item && item.baseItemId){
            return KNS_EquipPrefixSuffixes.getPreSuffixItemByName(item, false);
        }else{
            return null;
        }
    }

    DreamX.RandomPrefixSuffix.knsSetPrefixItem = function(target, item){
        const newItem = this.knsMakeSpecifiedItem(
            this.knsGetOriginalItem(target),
            item,
            this.knsGetSuffixItem(target)
        );
        $gameParty.gainItem(target,  -1);
        $gameParty.gainItem(newItem, +1);

        DataManager.registerNewItem(newItem);
        return $gameTemp.lastDXRPSItemCreated;
    }

    DreamX.RandomPrefixSuffix.knsSetSuffixItem = function(target, item){
        const newItem = this.knsMakeSpecifiedItem(
            this.knsGetOriginalItem(target),
            this.knsGetPrefixItem(target),
            item
        );
        $gameParty.gainItem(target,  -1);
        $gameParty.gainItem(newItem, +1);

        DataManager.registerNewItem(newItem);
        return $gameTemp.lastDXRPSItemCreated;
    }

    DreamX.RandomPrefixSuffix.knsMakeSpecifiedItem = function (
        baseItem, prefixItem, suffixItem
    ) {
        // make a deep copy of the original item
        var newItem = JSON.parse(JSON.stringify(baseItem));
        var dataType;
        newItem.note += "\n";
        // item type
        if (newItem.wtypeId) {
            dataType = $dataWeapons;
        } else if (newItem.atypeId) {
            dataType = $dataArmors;
        } else {
            dataType = $dataItems;
        }
        // add icon of original icon index if overlay
        if (baseItem.meta.OverlayIcon) {
            this.addIconOverlay(baseItem, newItem);
        }
        if (prefixItem) {
            this.combineWithBaseItem(prefixItem, newItem, "prefix");
            this.evaluateCustomEffect(prefixItem, baseItem, newItem);
        }
        if (suffixItem) {
            this.combineWithBaseItem(suffixItem, newItem, "suffix");
            this.evaluateCustomEffect(suffixItem, baseItem, newItem);
        }
        this.deletePrefixSuffixNotes(newItem);

        newItem.DXRPSItem = true;
        newItem.DXRPSNewItem = true;

        if (newItem._DXHighestParamBonus && newItem._DXHighestParamBonus !== 0) {
            var sign = newItem._DXHighestParamBonus > 0 ? "+" : "";
            var bonusParamText = paramBonusParamText.format(sign, newItem._DXHighestParamBonus);
            newItem.name = newItem.name + " " + bonusParamText;
            delete newItem._DXHighestParamBonus;
            delete newItem._DXHighestParamID;
        }

        if (newItem.overlayIcons) {
            if (!$gameSystem.overlayIcons) {
                $gameSystem.overlayIcons = [];
            }
            var newCombIconId = paramCombIconStarting
                    + $gameSystem.overlayIcons.length;
            newItem.overlayIcons.sort(function (a, b) {
                return a.order - b.order;
            });
            $gameSystem.overlayIcons.push(newItem.overlayIcons);
            newItem.iconIndex = newCombIconId;
        }

        newItem.nonIndependent = false;
        newItem.originalBaseItemId = baseItem.id;
        newItem.DXRPS124V = true;
        DataManager.processYanflyTags(newItem);
        newItem.DXRPSNewItem = true;
        return newItem;
    };

}).call(KNS_EquipPrefixSuffixes);