/*:
 * @plugindesc ver.1.1.0 スキルを選択し変数に返す機能を実装します。
 * <AUTHOR>
 *
 * @param selectVarId
 * @text デフォルト取得変数
 * @type variable
 * @default 155
 *
 * @param selectActorVarId
 * @text アクター別取得変数
 * @type struct<actorToVarId>[]
 * @default []
 *
 * @help
 * イベントのスクリプトにて以下の記述をすることで
 * 指定のアクターのスキルから選択できます。
 * show_skillList(アクターＩＤ, {
 *   needCost:   false,         // MP/TPが条件を満たしていれば選択可能、省略可能
 *   skillTypes: [1, 2],        // 表示するスキルタイプを指定する、省略可能
 *   keywords:   ["AAA", "BBB"] // メモ欄にいずれかのキーワードが指定されていれば表示、省略可能
 * });
 *
 * スキルが選ばれた場合はスキルデータそのものを、
 * 何も選ばれなかった場合は0を返します。
 *
 * ver.1.0.0(2023-05-22)
 * - デモ
 * ver.1.0.1(2023-05-24)
 * - スキル選択ウィンドウをマップ上で表示するよう変更しました。
 * ver.1.1.0(2023-06-08)
 * - スキルタイプウィンドウの選択を廃止しスキルウィンドウとマウスから
 *   操作するよう変更しました。
 * - スキルの選択条件にスキルコスト・キーワードを追加しました。
 * - 表示するスキルタイプを引数で選択よう変更しました。
 * - アクターごとに選択結果の代入変数IDを変更できるようになりました。
 */
/*~struct~actorToVarId:
 * @param actorId
 * @text アクターID
 * @type actor
 * @default 1
 *
 * @param varId
 * @text 対応変数ID
 * @type variable
 * @default 155
 *
 */
//==========================================================
// new KNSSkillSelect
//==========================================================
const KNS_SkillSelect = {
	name: "KNS_SkillSelect",
	param: null,
	selectVarId: 155,
	selectActorVarIds: {},
	actorIdToVarId: function(actorId){
		return this.selectActorVarIds[actorId] || this.selectVarId;
	}
};
(function(){
	this.param = PluginManager.parameters(this.name);
	this.selectVarId = Math.floor(this.param.selectVarId);
	JsonEx.parse(this.param.selectActorVarId).forEach(function(json){
		const obj = JsonEx.parse(json);
		this.selectActorVarIds[obj.actorId] = Math.floor(obj.varId);
	}, this);

}).call(KNS_SkillSelect);

(function(){
KNS_RgssTranslator.addScriptPattern(/show_skillList\(/g, "this.knsShowSkillList(");
//==========================================================
// alias Game_Interpreter
//==========================================================
Game_Interpreter.prototype.knsShowSkillList = function(actorId, info={}) {
	info.actorId = actorId;
	$gameMessage.knsSetSkillSelectInfo(info);
	this.setWaitMode('message');
};

//==========================================================
// alias Game_Actor
//==========================================================
Game_Actor.prototype.canPaySkillCost = function(skill) {
    return 100 - this._tp >= this.skillTpCost(skill) && this._mp >= this.skillMpCost(skill);
};


//==========================================================
// alias Game_Message
//==========================================================
const _Game_Message_clear = Game_Message.prototype.clear;
Game_Message.prototype.clear = function(){
	_Game_Message_clear.apply(this, arguments);
	this._knsSkillSelectInfo = null;
}

Game_Message.prototype.knsSetSkillSelectInfo = function(info){
	this._knsSkillSelectInfo = info;
}

Game_Message.prototype.knsIsSkillChoice = function(){
	return !!this._knsSkillSelectInfo;
}

const _Game_Message_isChoice = Game_Message.prototype.isChoice;
Game_Message.prototype.isChoice = function() {
    return _Game_Message_isChoice.apply(this, arguments) || this.knsIsSkillChoice();
};

//==========================================================
// new Window_Message
//==========================================================
const _Window_Message_subWindows = Window_Message.prototype.subWindows;
Window_Message.prototype.subWindows = function() {
	return _Window_Message_subWindows.apply(this, arguments).concat(
		[this._knsSkillWindow, this._knsHelpWindow, this._knsSkillCategoryWindow]
	);
};

const _Window_Message_createSubWindows = Window_Message.prototype.createSubWindows;
Window_Message.prototype.createSubWindows = function() {
	_Window_Message_createSubWindows.apply(this, arguments);
	// help
	this._knsHelpWindow = new Window_Help(2);
	this._knsHelpWindow.openness = 0;
	this._knsHelpWindow.deactivate();
	// skill list
	this._knsSkillWindow = new Window_KnsSkillSelectList();
	this._knsSkillWindow.setHandler("ok",     this.knsOnSkillListOk.bind(this));
	this._knsSkillWindow.setHandler("cancel", this.knsOnSkillListCancel.bind(this));
	this._knsSkillWindow.setHandler("left",   this.knsOnSkillListLeft.bind(this));
	this._knsSkillWindow.setHandler("right",  this.knsOnSkillListRight.bind(this));
	// set as subwindows
	this._knsSkillWindow.setHelpWindow(this._knsHelpWindow);
	this._itemWindow.setHelpWindow(this._knsHelpWindow);
	// category
	this._knsSkillCategoryWindow = new Window_KnsSkillSelectCommand();
	this._knsSkillCategoryWindow.setSkillWindow(this._knsSkillWindow);
};

// handlers
Window_Message.prototype.knsOnSkillListOk = function(){
	this.knsTerminateSkillSelection(this._knsSkillWindow.item() || 0);
}

Window_Message.prototype.knsOnSkillListCancel = function(){
	this.knsTerminateSkillSelection(0);
}

Window_Message.prototype.knsOnSkillListLeft = function(){
	const index = this._knsSkillWindow.index();
	if (index % this._knsSkillWindow.maxCols() === 0){
		if (Input.isTriggered('left')){
			this._knsSkillCategoryWindow.cursorLeft(true);
			return true;
		}
	}else{
		this._knsSkillWindow.cursorLeft(false);
		return true;
	}
	return false;
}

Window_Message.prototype.knsOnSkillListRight = function(){
	const index = this._knsSkillWindow.index() + 1;
	if (
		(index % this._knsSkillWindow.maxCols()) === 0 ||
		index === this._knsSkillWindow.maxItems()
	){
		if (Input.isTriggered('right')){
			this._knsSkillCategoryWindow.cursorRight(true);
			return true;
		}
	}else{
		this._knsSkillWindow.cursorRight(false);
		return true;
	}
	return false;
}

// switch
const _Window_Message_startInput = Window_Message.prototype.startInput;
Window_Message.prototype.startInput = function() {
	if (this._knsSkillWindow.active){
		return true;
	}else if ($gameMessage.knsIsSkillChoice()){
		this.knsStartSkillSelection();
		return true;
	}else{
		return _Window_Message_startInput.apply(this, arguments);
	}
};

Window_Message.prototype.knsStartSkillSelection = function(){
	if (this.y >= Graphics.boxHeight / 2){
		this._knsHelpWindow.y = 0;
	}else{
		this._knsHelpWindow.y = Graphics.boxHeight - (
			this._knsHelpWindow.height +
			this._knsSkillCategoryWindow.height +
			this._knsSkillWindow.height
		);
	}
	this._knsHelpWindow.open();
	this._knsHelpWindow.clear();

	this._knsSkillCategoryWindow.activate();
	this._knsSkillCategoryWindow.y = this._knsHelpWindow.y + this._knsHelpWindow.height;
	this._knsSkillWindow.y = this._knsSkillCategoryWindow.y + this._knsSkillCategoryWindow.height;

	const actor = $gameActors.actor($gameMessage._knsSkillSelectInfo.actorId);
	this._knsSkillWindow.knsStart(actor);
	this._knsSkillCategoryWindow.knsStart(actor);
}

Window_Message.prototype.knsTerminateSkillSelection = function(id){
	const actorId = $gameMessage._knsSkillSelectInfo.actorId;
	$gameVariables.setValue(KNS_SkillSelect.actorIdToVarId(actorId), id);
	if (id){
		const actor = $gameActors.actor(actorId);
		actor.setLastMenuSkill(id);
	}
	this._knsHelpWindow.clear();
	this._knsHelpWindow.close();
	this._knsSkillWindow.close();
	this._knsSkillWindow.deactivate();
	this._knsSkillWindow._actor = null;
	this._knsSkillCategoryWindow.close();
	this._knsSkillCategoryWindow.deactivate();
	this._knsSkillCategoryWindow._actor = null;
	this.terminateMessage();
}

//==========================================================
// alias Window_EventItem
//==========================================================
const _Window_EventItem_open = Window_EventItem.prototype.open;
Window_EventItem.prototype.open = function() {
	_Window_EventItem_open.apply(this, arguments);
	if (this._helpWindow){ this._helpWindow.open(); }
};

const _Window_EventItem_close = Window_EventItem.prototype.close;
Window_EventItem.prototype.close = function() {
	_Window_EventItem_close.apply(this, arguments);
	if (this._helpWindow){ this._helpWindow.close(); }
};

Window_EventItem.prototype.updatePlacement = function() {
	if (this._messageWindow.y >= Graphics.boxHeight / 2) {
		this.y = this._helpWindow.height;
		this._helpWindow.y = 0;
	} else {
		this.y = Graphics.boxHeight - this.height;
		this._helpWindow.y = this.y - this._helpWindow.height;
	}
};
})();

//==========================================================
// new Window_KnsSkillSelectCommand
//==========================================================
class Window_KnsSkillSelectCommand extends Window_SkillType{
	windowWidth(){ return Graphics.boxWidth; }
	maxCols(){ return 4; }
	numVisibleRows(){ return 1; }
	itemTextAlign(){ return "center"; }

	initialize(){
		super.initialize(0, 0);
		this.deactivate();
		this.openness = 0;
	}
	knsStart(actor){
		this.setActor(actor);
		this.open();
		this.selectLast();
	}
	makeCommandList() {
		const info = $gameMessage._knsSkillSelectInfo;
		if (info && info.skillTypes){
			info.skillTypes.forEach(function(stypeId){
				this.addCommand($dataSystem.skillTypes[stypeId], 'skill', true, stypeId);
			}, this);
		}else{
			super.makeCommandList();
		}
	};
	processCursorMove() {};
	_updateCursor() {
		this._windowCursorSprite.alpha = 128;
		this._windowCursorSprite.visible = this.isOpen();
	};

}

//==========================================================
// new Window_KnsSkillSelectList
//==========================================================
class Window_KnsSkillSelectList extends Window_SkillList{
	initialize(){
		super.initialize(0, 0, Graphics.width, this.fittingHeight(4));
		this.deactivate();
		this.openness = 0;
	}
	knsStart(actor){
		this.setActor(actor);
		this.open();
		this.selectLast();
		this.activate();
	}
	makeItemList(){
		super.makeItemList();
		this._data.push(null);
	}
	setStypeId(stypeId) {
		const changed = this._stypeId !== stypeId;
		super.setStypeId(stypeId);
		if (changed) {
			this.selectLast();
		}
	};
	includes(item) {
		if (item && super.includes(item)){
			const info = $gameMessage._knsSkillSelectInfo;
			if (info && info.keywords){
				return info.keywords.some(function(keyword){
					return item.note.includes(keyword);
				});
			}else{
				return true;
			}
		}
		return false;
	}
	isEnabled(item){
		if (item){
			const info = $gameMessage._knsSkillSelectInfo;
			if (info && info.needCost){ return this._actor.canPaySkillCost(item); }
		}
		return true;
	}
	selectLast(){
		if (this._actor){
			super.selectLast();
			if (this.item() === null){ this.select(0); }
		}else{
			this.select(0);
		}
	}
	processCursorMove() {
		if (this.isCursorMovable()) {
			var lastIndex = this.index();
			if (Input.isRepeated('down')) {
				this.cursorDown(Input.isTriggered('down'));
			}
			if (Input.isRepeated('up')) {
				this.cursorUp(Input.isTriggered('up'));
			}
			if (this.knsProcessHandler('left')){ return; }
			if (this.knsProcessHandler('right')){ return; }
			if (!this.isHandled('pagedown') && Input.isTriggered('pagedown')) {
				this.cursorPagedown();
			}
			if (!this.isHandled('pageup') && Input.isTriggered('pageup')) {
				this.cursorPageup();
			}
			if (this.index() !== lastIndex) {
				SoundManager.playCursor();
			}
		}
	}
	knsProcessHandler(key) {
		if (Input.isRepeated(key)) {
			if (this._handlers[key]()){
				SoundManager.playCursor();
				return true;
			}
		}
		return false;
	}
}