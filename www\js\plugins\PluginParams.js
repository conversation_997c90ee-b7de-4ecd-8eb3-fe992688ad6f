function PluginParams(n,r){let a=PluginManager.parameters(n)||{},e=Object.keys(a);for(let n=0;n<e.length;n++){let u=e[n];this[u]=PluginParams.Parse(a[u],r[u])}}PluginParams.Parse=function(n,r){return{number:n=>PluginParams.ToNumberFloat(n),int:n=>PluginParams.ToNumberInt(n),float:n=>PluginParams.ToNumberFloat(n),array:n=>PluginParams.ToJSON(n),json:n=>PluginParams.ToJSON(n),string:n=>n,undefined:n=>n}[r](n)},PluginParams.ToNumberInt=function(n){return parseInt(n)},PluginParams.ToNumberFloat=function(n){return parseFloat(n)},PluginParams.ToJSON=function(n){return JSON.parse(n)},new PluginParams("LogWindow",{X:"number",Y:"number",Width:"number",Height:"number",FontSize:"number",Margin:"number",Speed:"float",Acceleration:"float"});