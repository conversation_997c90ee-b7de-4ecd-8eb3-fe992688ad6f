{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "王都系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["パンツ染みおじさん画像"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["3 otoko"]}, {"code": 408, "indent": 0, "parameters": ["5 manjiru"]}, {"code": 408, "indent": 0, "parameters": ["6 maetare"]}, {"code": 408, "indent": 0, "parameters": ["7 body"]}, {"code": 408, "indent": 0, "parameters": ["8 hair"]}, {"code": 108, "indent": 0, "parameters": ["10 panties"]}, {"code": 408, "indent": 0, "parameters": ["11 タイツ"]}, {"code": 408, "indent": 0, "parameters": ["12 finger"]}, {"code": 408, "indent": 0, "parameters": ["13 cloth"]}, {"code": 408, "indent": 0, "parameters": ["14 hat"]}, {"code": 408, "indent": 0, "parameters": ["20 overay"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-rojiura1\""]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["まん汁"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["stage = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-siru${stage}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["前タレ"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82-maetare`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディ"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアー"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["パンティー"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth-panties`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツ"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["司祭のフィンガー"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-finger${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公のハンド"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ビクセン19"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": ["stage = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-bikusen`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["オーバーレイ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-over_ray`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["スラム　寝取らせチンカスフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-bj_smegma\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 355, "indent": 0, "parameters": ["n = 1"]}, {"code": 655, "indent": 0, "parameters": ["i = 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["while i < 21"]}, {"code": 655, "indent": 0, "parameters": ["eval(\"$layer#{i} = i\")"]}, {"code": 655, "indent": 0, "parameters": ["i += 1"]}, {"code": 655, "indent": 0, "parameters": ["n += 1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-base#{$skin}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-hair#{$hair}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-guy\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-arms#{$skin}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-hat\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-face#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["スラム　寝取らせ穴覗き"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-peephole\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["共通"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックス"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 1, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["イき潮"]}, {"code": 111, "indent": 1, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["射精"]}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 1, "parameters": ["pose = $gameVariables.value(27)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-s${pose}-hair${$hair}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["男の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}s`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["覗き目"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["男の表示"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy_eye`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェラチオ"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 2, 0]}, {"code": 108, "indent": 1, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 1, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-bj_base${$skin}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["射精"]}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["tinpo"]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-bj_cock`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["共通"]}, {"code": 108, "indent": 0, "parameters": ["覗き穴"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, -1, 5]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-peephole`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["スラム　寝取らせ　スラム事後"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-slum_after\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-bg#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-base#{$skin}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-hair#{$hair}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-guy_s#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{$e_name}-face#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-steam\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["スラム　寝取らせ　4some"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-4some\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["guy ass"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_ass`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["guys"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["xrayの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "ヤリゾー系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー手コキ１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo_hj1\""]}, {"code": 108, "indent": 0, "parameters": ["3ボディ"]}, {"code": 408, "indent": 0, "parameters": ["10ヘッド"]}, {"code": 408, "indent": 0, "parameters": ["11ヘアー"]}, {"code": 408, "indent": 0, "parameters": ["12フェイス"]}, {"code": 408, "indent": 0, "parameters": ["15ちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["16ア<PERSON><PERSON> skin-pose"]}, {"code": 108, "indent": 0, "parameters": ["20精子"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アームの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arm${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー手マン２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-fj2\""]}, {"code": 108, "indent": 0, "parameters": ["3 guy v[456]"]}, {"code": 408, "indent": 0, "parameters": ["4 base"]}, {"code": 408, "indent": 0, "parameters": ["5 ph"]}, {"code": 408, "indent": 0, "parameters": ["6 guy-legs v[456]"]}, {"code": 408, "indent": 0, "parameters": ["10 head 33-25"]}, {"code": 408, "indent": 0, "parameters": ["11 hair 34-25"]}, {"code": 108, "indent": 0, "parameters": ["12 face 24-25"]}, {"code": 408, "indent": 0, "parameters": ["15 guy v[456]-arm v[28]"]}, {"code": 408, "indent": 0, "parameters": ["20 yuge"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の脚"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy-legs${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の腕"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num1}-arms${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ゆげ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾークンニ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-pussy_licking\""]}, {"code": 108, "indent": 0, "parameters": ["3 head"]}, {"code": 408, "indent": 0, "parameters": ["4 hair"]}, {"code": 408, "indent": 0, "parameters": ["5 face (h-face)"]}, {"code": 408, "indent": 0, "parameters": ["6 body"]}, {"code": 408, "indent": 0, "parameters": ["7 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 guy"]}, {"code": 108, "indent": 0, "parameters": ["11 hands"]}, {"code": 408, "indent": 0, "parameters": ["12 feet"]}, {"code": 408, "indent": 0, "parameters": ["13 squirt"]}, {"code": 408, "indent": 0, "parameters": ["14 steam"]}, {"code": 108, "indent": 0, "parameters": ["15 cutin-bg"]}, {"code": 408, "indent": 0, "parameters": ["16 cutin-crotch"]}, {"code": 408, "indent": 0, "parameters": ["17 cutin-guy"]}, {"code": 408, "indent": 0, "parameters": ["18 ph"]}, {"code": 408, "indent": 0, "parameters": ["19 waku"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num =  $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num2 =  $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${num1}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,50,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hands${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["足の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-feet${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["カットインの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-bg`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-crotch${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-guy${n}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 1, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["n = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cutin-ph`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin-waku`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ゆげ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー目隠しフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-hiding_eyes\""]}, {"code": 108, "indent": 0, "parameters": ["2 body#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["4 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["5 guy#{id}"]}, {"code": 408, "indent": 0, "parameters": ["6 mouth#{face}"]}, {"code": 408, "indent": 0, "parameters": ["10 op#{op}"]}, {"code": 108, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["口の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mouth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オプションの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-op${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ゆげ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 主人公と３Ｐ　Lvl3"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-3some_pro_yari\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男１"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男21"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`;"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(40) >= 3) $no = `${$e_name}-guy21x`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子1-2"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボテ"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 40, 0, 2, 2]}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-bote${num}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの腕"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-arm_yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子　３のみ"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["主人公精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum_pro`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer19);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ちんぽ臭いあて"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-GuessSmell\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["コンパチ"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-konpati1-${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 1617, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-konpati21-${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}-${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth82`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["*************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヘッド部分"]}, {"code": 408, "indent": 0, "parameters": ["*************************************************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${num}-${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マスク"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mask${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["num1 = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}-${num1}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["*************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヘッド部分ここまで"]}, {"code": 408, "indent": 0, "parameters": ["*************************************************"]}, {"code": 108, "indent": 0, "parameters": ["tinpo"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guysCock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["右手"]}, {"code": 355, "indent": 0, "parameters": ["op1 = $gameVariables.value(1616)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-rightArm${op1}-${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 0, 0]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 2, 0]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["左手"]}, {"code": 355, "indent": 0, "parameters": ["op2 = $gameVariables.value(1617)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-leftArm${op2}-${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 0, 0]}, {"code": 235, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 1, 0]}, {"code": 235, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["キスマーク"]}, {"code": 355, "indent": 0, "parameters": ["op3 = $gameVariables.value(1618)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hickey${op3}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["バツゲーム"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-yarizo-BatsuGame\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["tinpo"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [12]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy21`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["手"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hands${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ちんぽ目隠しフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-NtsMekakushiBJ\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男lower"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy_lower${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男lower(ギガチャド主人公）"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [0, 129, 0]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-guy_lower1x`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer13)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["帽子"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男upper"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameVariables.value(456) == 1) {"]}, {"code": 655, "indent": 0, "parameters": ["  num = 21"]}, {"code": 655, "indent": 0, "parameters": ["}else{"]}, {"code": 655, "indent": 0, "parameters": ["  num = 1"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy_upper${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男upper(ギガチャド主人公）"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 129, 0]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-guy_upper1x`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "王都ナンパ野郎-フェラチオ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ナンパ野郎フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-pa1-bj\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg#{bg} (priestess-pa1-bg0)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["9 guy#{456?}"]}, {"code": 108, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["12 hat"]}, {"code": 408, "indent": 0, "parameters": ["13 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["15 cock#{456?}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["20 hand (priestess-pa1-hand)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["name = `event-priestess-pa1-bg`"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${name}${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["name = \"event-priestess-pa1-hand\""]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${name}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ナンパ野郎セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-pa1-sex\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg#{bg} (priestess-pa1-bg0)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["9 guy#{456?}"]}, {"code": 108, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["12 hat"]}, {"code": 408, "indent": 0, "parameters": ["13 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["15 cock#{456?}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["20 hand (priestess-pa1-hand)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["name = \"event-priestess-pa1-bg\""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${name}${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["name = \"event-priestess-pa1-hand\""]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${name}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "宿屋 - 値段交渉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-inn-negotiation\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["2 guy#{456} if pose != 1"]}, {"code": 408, "indent": 0, "parameters": ["5 pos#{pos}-base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["6 pos#{pos}-ph if pose ==2"]}, {"code": 408, "indent": 0, "parameters": ["7 pos#{pos}-ah if pose ==1"]}, {"code": 408, "indent": 0, "parameters": ["10 pos#{pos}-hair#{hair}"]}, {"code": 108, "indent": 0, "parameters": ["12 pos#{pos}-face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["13 pos#{pos}-x-ray#{cum} if pose < 2"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["15 guy#{456} if pose == 1"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["16 steam"]}, {"code": 108, "indent": 0, "parameters": ["17 door if stage ==1, alpha 125"]}, {"code": 408, "indent": 0, "parameters": ["18 hand"]}, {"code": 408, "indent": 0, "parameters": ["20 effect"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 1, 5]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}a`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer2].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-pos${pos}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-pos${pos}-ph`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer6].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-pos${pos}-ah`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer7].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-pos${pos}-hair${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-pos${pos}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["X-Ray"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 2, 4]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["pos = $gameVariables.value(24)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-pos${pos}-xray${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer13].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}b`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer15].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["steam"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["扉"]}, {"code": 355, "indent": 0, "parameters": ["alpha = 255 "]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(28)==1){"]}, {"code": 655, "indent": 0, "parameters": ["alpha = 100 "]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-door`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-hand`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer18].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["エフェクト"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-effect`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-pa1-sex\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg#{bg} (priestess-pa1-bg0)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["9 guy#{456?}"]}, {"code": 108, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["12 hat"]}, {"code": 408, "indent": 0, "parameters": ["13 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["15 cock#{456?}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["20 hand (priestess-pa1-hand)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["name = \"event-priestess-pa1-bg\""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = \"#{name}#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face#{num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["name = `event-priestess-pa1-hand`"]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${name}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "★売春宿編", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["売春宿　手コキ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-hj1\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["5 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["11 guy1000_nipple if 456 == 1000"]}, {"code": 108, "indent": 0, "parameters": ["15 cum#{cum}"]}, {"code": 408, "indent": 0, "parameters": ["16 bikusen"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["乳首の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1000, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy1000_nipple`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ビクセン"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bikusen`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["steam"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["売春宿　アナル舐め"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-rj1\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy_lower#{num}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 guy_upper#{num}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["12 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["15 hands#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy_lower${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男upper"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy_upper${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hands${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["steam"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["売春宿　背面から手マン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-fj3\""]}, {"code": 108, "indent": 0, "parameters": ["2 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["6 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 cock#{456}"]}, {"code": 108, "indent": 0, "parameters": ["11 hand#{456}"]}, {"code": 408, "indent": 0, "parameters": ["15 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["チンポの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["steam"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["売春宿　ピーンセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-sex_pin\""]}, {"code": 108, "indent": 0, "parameters": ["1 bg"]}, {"code": 408, "indent": 0, "parameters": ["2 wet if stage >= 1"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["6 p#{head pose}-head#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["7 p#{head pose}-hair#{hair} if head == 0 elese delete"]}, {"code": 408, "indent": 0, "parameters": ["8 p#{head pose}-face#{face} if head == 0 else delete"]}, {"code": 408, "indent": 0, "parameters": ["10 squriting stage >= 2"]}, {"code": 408, "indent": 0, "parameters": ["13 cum if cum >= 1"]}, {"code": 408, "indent": 0, "parameters": ["15 p#{stage}-guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["16 p#{leg pose}-legs#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["18 arms#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-bg0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["濡れの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-wet`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-p${num}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-p${num}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["num1 = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-p${num1}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": [" $no = `${$e_name}-squirting`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["let num = $gameVariables.value(456);"]}, {"code": 655, "indent": 0, "parameters": ["let num1 = $gameVariables.value(28);"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(28) >= 1) num1 = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let opa = 255;"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(28) >= 1) opa = 120;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-p${num1}-guy${num}`;"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15];"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no, 0, 0, 0, $s_w, $s_h, opa, 0)"]}, {"code": 108, "indent": 0, "parameters": ["脚の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(26)"]}, {"code": 655, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-p${num}-legs${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "タイタンステップ編", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせGangbang 顔にちんぽ押し付け"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-cocks_rub_face\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["5 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["10 guys"]}, {"code": 408, "indent": 0, "parameters": ["15 arms#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせGangbang フェラチオとセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-gangbang_bj_sex\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 ph"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["7 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["8 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth hat"]}, {"code": 108, "indent": 0, "parameters": ["15 guys"]}, {"code": 408, "indent": 0, "parameters": ["16 mouth skin"]}, {"code": 408, "indent": 0, "parameters": ["19 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["マウススキン"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-mouth${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせGangbang ダブルセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-gangbang_double_sex\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 ph"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["7 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["8 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth hat"]}, {"code": 108, "indent": 0, "parameters": ["15 guys"]}, {"code": 408, "indent": 0, "parameters": ["16 mouth skin"]}, {"code": 408, "indent": 0, "parameters": ["19 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせGangbang 見せつけ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-priestess-gangbang_last\""]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 ph"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["7 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["8 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth hat"]}, {"code": 108, "indent": 0, "parameters": ["15 guys"]}, {"code": 408, "indent": 0, "parameters": ["16 mouth skin"]}, {"code": 408, "indent": 0, "parameters": ["19 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["頭装備の表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).equips()[2] != null"]}, {"code": 355, "indent": 1, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy_upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["おっぱいの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-boobs${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせ　クエスト：ポイズン・ピンク　ホーム"]}, {"code": 408, "indent": 0, "parameters": ["レス１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-ts_pp_ntrs1\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["手の表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hands${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせ　クエスト：ポイズン・ピンク　ホーム"]}, {"code": 408, "indent": 0, "parameters": ["レス２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-ts_pp_ntrs2\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["▽▽▽▽▽▽▽▽カットイン▽▽▽▽▽▽▽▽"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["背景"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_bg`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_cock`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 1, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_base${$skin}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_hair${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["顔の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_face${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["男の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["ライン"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin_line`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["タイタン・ステップ　寝取らせ　クエスト：ポイズン・ピンク　ホーム"]}, {"code": 408, "indent": 0, "parameters": ["レス３"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-ts_pp_ntrs3\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["潮ふき"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["opa = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,opa,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "冒険者の宿営地", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 1}, {"id": 8, "name": "ダークエルフの拠点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー手マン１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-SexMuki\""]}, {"code": 108, "indent": 0, "parameters": ["20精子"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 1}]}