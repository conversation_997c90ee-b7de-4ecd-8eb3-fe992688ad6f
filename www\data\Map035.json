{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "司祭ソロ：ナンパ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [130]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [102]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 0, "parameters": [102]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 7]}, {"code": 117, "indent": 0, "parameters": [102]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6-7"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**********"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*************"]}, {"code": 408, "indent": 0, "parameters": ["寝取らせと寝取られが両方オフ"]}, {"code": 408, "indent": 0, "parameters": ["**********"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************"]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ寝取られ両方オン"]}, {"code": 408, "indent": 0, "parameters": ["**********"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [1, 83, 1, 82, 1]}, {"code": 108, "indent": 3, "parameters": ["純愛が淫欲以上なら寝取らせに飛ぶ"]}, {"code": 119, "indent": 3, "parameters": ["寝取らせ"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["純愛が淫欲未満なら寝取られに飛ぶ"]}, {"code": 119, "indent": 3, "parameters": ["寝取られ"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************"]}, {"code": 408, "indent": 0, "parameters": ["寝取らせのみオン"]}, {"code": 408, "indent": 0, "parameters": ["**********"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 1]}, {"code": 118, "indent": 2, "parameters": ["寝取らせ"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************"]}, {"code": 408, "indent": 0, "parameters": ["寝取られのみオン"]}, {"code": 408, "indent": 0, "parameters": ["**********"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 1]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 118, "indent": 2, "parameters": ["寝取られ"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [75]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["（あ……！　あそこにいるのは\\v[50]……！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 102, "indent": 0, "parameters": [["主人公に甘える", "寝取らせ", "寝取られ"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "主人公に甘える"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["\\v[50]～～！！！"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [103]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["もぉ～！いつからそこに居たんですかぁ？\\I[122]\\I[122]"]}, {"code": 401, "indent": 1, "parameters": ["もしかして私のこと探してたんですか？\\I[122]\\I[122]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["え、今は用事があるからまだ合流できない？"]}, {"code": 401, "indent": 1, "parameters": ["もぉ～！早く用事終わらせてくださいっ！"]}, {"code": 401, "indent": 1, "parameters": ["寂しくて死んじゃいそうですよぉ～\\I[122]\\I[122]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][*]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["（え、なに！？俺のときと反応違いすぎない！？）"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "寝取らせ"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [17]}, {"code": 235, "indent": 1, "parameters": [18]}, {"code": 235, "indent": 1, "parameters": [19]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["（\\v[50]ったらあんな顔で私を見て……"]}, {"code": 401, "indent": 1, "parameters": ["ふふ、本当かわいいんですから……\\I[122]\\I[122]）"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "寝取られ"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, -1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [17]}, {"code": 235, "indent": 1, "parameters": [18]}, {"code": 235, "indent": 1, "parameters": [19]}, {"code": 231, "indent": 1, "parameters": [20, "event-0-hand", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "主人公以外のメンバーを外す", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["var i = 3;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["while (i > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["  if ($gameParty.members()[i] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameParty.members()[i].actorId();"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.removeActor(id);"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["  i -= 1;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "Aキー会話イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["闇のゲーム"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["※未実装"]}, {"code": 401, "indent": 1, "parameters": ["日常など汎用的なことから攻略中のクエストについてなどを"]}, {"code": 401, "indent": 1, "parameters": ["喋ったりする。"]}, {"code": 401, "indent": 1, "parameters": ["寝取られオンの場合は司祭ちゃんのステ次第で塩対応になっていく。"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 231, "indent": 1, "parameters": [1, "test-intimacy", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["ご主人様、ゲームしませんか？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["はい、\\v[50]が私に\\c[2]100回好きっていうだけ\\c[0]の簡単なゲームです。"]}, {"code": 401, "indent": 1, "parameters": ["言えなかった場合は\\v[50]の負けです。"]}, {"code": 401, "indent": 1, "parameters": ["\\v[50]が負けたときは……ふふ……\\I[122]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["ゲーム、します？"]}, {"code": 102, "indent": 1, "parameters": [["はい", "いいえ"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "はい"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [103]}, {"code": 122, "indent": 2, "parameters": [52, 52, 0, 0, 100]}, {"code": 112, "indent": 2, "parameters": []}, {"code": 118, "indent": 3, "parameters": ["選択肢"]}, {"code": 108, "indent": 3, "parameters": ["選択肢位置(480, 280)"]}, {"code": 102, "indent": 3, "parameters": [["好き", "\\n[2]が好き if(s[100])", "ギブアップ"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "好き"]}, {"code": 122, "indent": 4, "parameters": [52, 52, 2, 0, 1]}, {"code": 111, "indent": 4, "parameters": [1, 52, 0, 79, 0]}, {"code": 119, "indent": 5, "parameters": ["失敗"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 52, 0, 80, 0]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["ところで、ポーションメーカーさんのことどう思ってます？"]}, {"code": 121, "indent": 5, "parameters": [100, 100, 0]}, {"code": 119, "indent": 5, "parameters": ["選択肢"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 122, "indent": 4, "parameters": [20, 20, 0, 2, 1, 5]}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 1, 0]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [103]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["私も好きぃ……\\I[122]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 2, 0]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 5, "parameters": [103]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["あと\\v[52]回ですよっ！\\I[122]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 3, 0]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 5, "parameters": [103]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["もっと！\\I[122]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 4, 0]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [103]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["しゅきっ！\\I[122]しゅきぃぃ……\\I[122]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 5, 0]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [103]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["私は\\v[50]だけのものですよぉ\\I[122]\\I[122]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 52, 0, 0, 0]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 5, "parameters": ["わぁ、負けちゃいました\\I[122]"]}, {"code": 401, "indent": 5, "parameters": ["こんなに愛されてるなんて幸せです……\\I[122]"]}, {"code": 221, "indent": 5, "parameters": []}, {"code": 117, "indent": 5, "parameters": [58]}, {"code": 222, "indent": 5, "parameters": []}, {"code": 355, "indent": 5, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 111, "indent": 5, "parameters": [1, 183, 0, 5, 2]}, {"code": 122, "indent": 6, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 6, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 6, "parameters": [2]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 113, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "\\n[2]が好き if(s[100])"]}, {"code": 122, "indent": 4, "parameters": [52, 52, 2, 0, 1]}, {"code": 121, "indent": 4, "parameters": [100, 100, 1]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["さすが\\v[50]！\\I[122]"]}, {"code": 119, "indent": 4, "parameters": ["選択肢"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [2, "ギブアップ"]}, {"code": 118, "indent": 4, "parameters": ["失敗"]}, {"code": 250, "indent": 4, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 4, "parameters": [[0, 0, 0, 255], 20, false]}, {"code": 122, "indent": 4, "parameters": [23, 23, 0, 0, 10]}, {"code": 117, "indent": 4, "parameters": [103]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["……！"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["……"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["…"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["まだまだ私への愛が足りないみたいですね……"]}, {"code": 401, "indent": 4, "parameters": ["もっと好きになってもらえるように、\\v[50]の体に愛を刻み込んで"]}, {"code": 401, "indent": 4, "parameters": ["あげますね……\\I[122]"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 4, "parameters": ["めちゃくちゃセックスされた。"]}, {"code": 117, "indent": 4, "parameters": [58]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 4, "parameters": ["tp_heal(1,-100)"]}, {"code": 111, "indent": 4, "parameters": [1, 184, 0, 5, 2]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 413, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "いいえ"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["……"]}, {"code": 111, "indent": 2, "parameters": [1, 184, 0, 5, 2]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "アイス売りおばさん", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 35, 4, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 35, 4, 1, 20, 0)"]}, {"code": 122, "indent": 0, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[11]>=10)", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[11]>=10)"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["あいすを買う"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 125, "indent": 1, "parameters": [1, 0, 10]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 10]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 4)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [1620, 1620, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["あいすを買わない"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "寝取らせ報告", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ報告"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 243, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [197]}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 655, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [110]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["****************************************************"]}, {"code": 408, "indent": 1, "parameters": ["報告（リピート）開始"]}, {"code": 118, "indent": 1, "parameters": ["報告リピート"]}, {"code": 108, "indent": 1, "parameters": ["*誰と？"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 4]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [479, 479, 0, 0, 1000]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [479, 479, 0, 0, 1100]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 3, 0]}, {"code": 122, "indent": 2, "parameters": [479, 479, 0, 0, 1200]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 4, 0]}, {"code": 122, "indent": 2, "parameters": [479, 479, 0, 0, 1300]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(479)"]}, {"code": 655, "indent": 1, "parameters": ["val_in_database(390, `Race${num}`)"]}, {"code": 122, "indent": 1, "parameters": [463, 463, 0, 2, 561, 572]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(463)"]}, {"code": 655, "indent": 1, "parameters": ["val_in_database(391, `job${num}`)"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 111, "indent": 1, "parameters": [1, 1, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*アクメ回数"]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 2, 1, 10]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 1, "parameters": ["*ランダムトーク"]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 6, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_31"]}, {"code": 119, "indent": 2, "parameters": ["計上処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 1, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_32"]}, {"code": 119, "indent": 2, "parameters": ["計上処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 0, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_33"]}, {"code": 119, "indent": 2, "parameters": ["計上処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["数値計上"]}, {"code": 118, "indent": 1, "parameters": ["計上処理"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["種族計上"]}, {"code": 111, "indent": 1, "parameters": [1, 479, 0, 1000, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 501]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 479, 0, 1100, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 502]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 479, 0, 1200, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 503]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 479, 0, 1300, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 505]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["ジョブ計上"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 1, 463]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["部位選定"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 402]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 403]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 3, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 404]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["アクメ回数"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 1, 54]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["主人公の興奮"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 2, 5, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["次の報告へ"]}, {"code": 118, "indent": 1, "parameters": ["次の報告へ"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [167, 167, 2, 0, 1]}, {"code": 111, "indent": 1, "parameters": [1, 167, 0, 0, 2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [104]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_90-91"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_40"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 3, "parameters": ["報告リピート"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["主人公の興奮が100の場合に限り下記イベント発生"]}, {"code": 111, "indent": 0, "parameters": [1, 185, 0, 100, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 35, 5, 1, 210, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 35, 5, 1, 220, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(35, 5, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(35, 5, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[80]"], 0, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 244, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ報告　手コキ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"手\""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [160]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_out_once", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [160]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["主人公の興奮"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -100]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["純愛"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 2, 5, 10]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ報告　首絞めックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [183]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [183]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["主人公の興奮"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -100]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["純愛"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 2, 5, 10]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 1}]}