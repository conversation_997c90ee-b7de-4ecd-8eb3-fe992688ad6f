[null, {"id": 1, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Slime", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 3, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 4, "value": 2}], "gold": 5, "name": "スライム", "note": "", "params": [100, 0, 12, 4, 8, 8, 10, 10]}, {"id": 2, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Bat", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 5, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 3, "value": 0.25}, {"code": 11, "dataId": 8, "value": 2}, {"code": 11, "dataId": 7, "value": 0.5}], "gold": 10, "name": "こうもり", "note": "", "params": [140, 0, 14, 6, 9, 9, 12, 12]}, {"id": 3, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "<PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 8, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 2, "value": 0.25}, {"code": 11, "dataId": 8, "value": 0.1}, {"code": 11, "dataId": 7, "value": 0.5}], "gold": 15, "name": "ホーネット", "note": "", "params": [180, 0, 16, 7, 12, 12, 16, 16]}, {"id": 4, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Spider", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 13, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 2, "value": 0.25}, {"code": 11, "dataId": 3, "value": 2}], "gold": 25, "name": "大グモ", "note": "", "params": [220, 0, 18, 8, 14, 14, 18, 18]}, {"id": 5, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 21}], "battlerHue": 0, "battlerName": "Rat", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 19, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 3, "value": 1.5}], "gold": 40, "name": "大ネズミ", "note": "", "params": [300, 0, 20, 9, 16, 16, 22, 22]}, {"id": 6, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 51}], "battlerHue": 0, "battlerName": "Willowisp", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 26, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 7, "value": 0.15}, {"code": 11, "dataId": 7, "value": 0.5}], "gold": 50, "name": "ウィスプ", "note": "", "params": [350, 60, 30, 20, 18, 18, 24, 24]}, {"id": 7, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 4}], "battlerHue": 0, "battlerName": "Snake", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 34, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 4, "value": 2}], "gold": 70, "name": "大蛇", "note": "", "params": [400, 0, 28, 20, 20, 20, 28, 28]}, {"id": 8, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "<PERSON><PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 43, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 2, "value": 0.25}, {"code": 11, "dataId": 4, "value": 1.5}], "gold": 90, "name": "大サソリ", "note": "", "params": [450, 0, 36, 30, 25, 25, 30, 30]}, {"id": 9, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Jellyfish", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 53, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 3, "value": 0.25}, {"code": 11, "dataId": 5, "value": 2}], "gold": 105, "name": "大クラゲ", "note": "", "params": [500, 0, 32, 15, 25, 25, 32, 32]}, {"id": 10, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 17}], "battlerHue": 0, "battlerName": "Plant", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 63, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 3, "value": 1.5}, {"code": 11, "dataId": 6, "value": 0.5}], "gold": 130, "name": "食人花", "note": "", "params": [600, 0, 40, 17, 30, 30, 36, 36]}, {"id": 11, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 51}], "battlerHue": 0, "battlerName": "Ghost", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 75, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 10, "value": 100}, {"code": 11, "dataId": 1, "value": 0.75}, {"code": 11, "dataId": 2, "value": 0}, {"code": 11, "dataId": 9, "value": 2}, {"code": 11, "dataId": 10, "value": 0}], "gold": 150, "name": "ゴースト", "note": "", "params": [700, 80, 48, 20, 40, 40, 38, 38]}, {"id": 12, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Skeleton", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 87, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 3, "value": 0.25}, {"code": 11, "dataId": 2, "value": 0}, {"code": 11, "dataId": 3, "value": 1.5}, {"code": 11, "dataId": 9, "value": 2}], "gold": 180, "name": "スケルトン", "note": "", "params": [800, 0, 50, 25, 35, 35, 40, 40]}, {"id": 13, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 20}], "battlerHue": 0, "battlerName": "Orc", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 99, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 6, "value": 2}, {"code": 22, "dataId": 2, "value": 0.1}], "gold": 200, "name": "オーク", "note": "", "params": [850, 0, 55, 30, 30, 30, 42, 42]}, {"id": 14, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 55}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 50}], "battlerHue": 0, "battlerName": "Imp", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 110, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 8, "value": 2}], "gold": 220, "name": "インプ", "note": "", "params": [900, 150, 60, 32, 40, 40, 45, 45]}, {"id": 15, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 23}], "battlerHue": 0, "battlerName": "<PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 130, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 6, "value": 0.25}, {"code": 11, "dataId": 5, "value": 2}], "gold": 250, "name": "ゲイザー", "note": "", "params": [950, 0, 65, 35, 40, 40, 50, 50]}, {"id": 16, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 53}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 65}], "battlerHue": 0, "battlerName": "<PERSON><PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 160, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 6, "value": 1.5}, {"code": 11, "dataId": 7, "value": 1.5}], "gold": 280, "name": "パペット", "note": "", "params": [1000, 200, 70, 40, 35, 35, 55, 55]}, {"id": 17, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 4}], "battlerHue": 0, "battlerName": "Zombie", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 180, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 2, "value": 0}, {"code": 11, "dataId": 3, "value": 1.5}, {"code": 11, "dataId": 9, "value": 2}], "gold": 320, "name": "ゾンビ", "note": "", "params": [1050, 0, 75, 40, 45, 45, 50, 50]}, {"id": 18, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 10}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 19}], "battlerHue": 0, "battlerName": "Cockatrice", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 260, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 8, "value": 2}], "gold": 400, "name": "コカトリス", "note": "", "params": [1100, 0, 80, 50, 40, 40, 60, 60]}, {"id": 19, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 4}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 9}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 18}], "battlerHue": 0, "battlerName": "Chimera", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 450, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 3, "value": 0.5}, {"code": 11, "dataId": 4, "value": 0.5}, {"code": 11, "dataId": 5, "value": 0.5}, {"code": 11, "dataId": 6, "value": 2}], "gold": 900, "name": "キマイラ", "note": "", "params": [1200, 0, 85, 45, 50, 50, 60, 60]}, {"id": 20, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 12}], "battlerHue": 0, "battlerName": "Mimic", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 700, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 5, "value": 0.2}, {"code": 22, "dataId": 2, "value": 0.2}, {"code": 61, "dataId": 0, "value": 0.5}], "gold": 1600, "name": "ミミック", "note": "", "params": [2000, 0, 100, 65, 60, 60, 65, 65]}, {"id": 21, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 22}], "battlerHue": 0, "battlerName": "Werewolf", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 660, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 2, "value": 0.25}, {"code": 11, "dataId": 3, "value": 1.5}, {"code": 22, "dataId": 2, "value": 0.1}], "gold": 1300, "name": "ウェアウルフ", "note": "", "params": [1600, 0, 90, 70, 65, 65, 70, 70]}, {"id": 22, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 4, "skillId": 13}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 64}], "battlerHue": 0, "battlerName": "<PERSON><PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 690, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 5, "value": 2}, {"code": 11, "dataId": 6, "value": 0.5}], "gold": 1800, "name": "サハギン", "note": "", "params": [1800, 400, 110, 75, 60, 60, 80, 80]}, {"id": 23, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 21}], "battlerHue": 0, "battlerName": "Ogre", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 1600, "traits": [{"code": 22, "dataId": 0, "value": 0.75}, {"code": 22, "dataId": 1, "value": 0}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 3, "value": 1.5}, {"code": 11, "dataId": 5, "value": 1.5}, {"code": 22, "dataId": 2, "value": 0.15}], "gold": 3200, "name": "オーガ", "note": "", "params": [2600, 0, 140, 80, 50, 50, 70, 70]}, {"id": 24, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 4}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 8}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 19}], "battlerHue": 0, "battlerName": "G<PERSON><PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 1400, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 5, "value": 1.5}, {"code": 11, "dataId": 10, "value": 0.5}], "gold": 2400, "name": "ガーゴイル", "note": "", "params": [2400, 0, 130, 80, 70, 70, 90, 90]}, {"id": 25, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 16}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 4, "skillId": 15}], "battlerHue": 0, "battlerName": "<PERSON><PERSON>", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 2600, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 3, "value": 0.25}, {"code": 11, "dataId": 4, "value": 2}], "gold": 4400, "name": "ラミア", "note": "", "params": [3200, 1000, 120, 90, 80, 80, 95, 95]}, {"id": 26, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 3}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 23}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 3, "skillId": 11}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 58}], "battlerHue": 0, "battlerName": "Vampire", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 3000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 4, "value": 0.5}, {"code": 11, "dataId": 9, "value": 2}, {"code": 11, "dataId": 10, "value": 0.5}, {"code": 22, "dataId": 2, "value": 0.1}], "gold": 5600, "name": "ヴァンパイア", "note": "", "params": [5200, 2000, 135, 100, 90, 90, 100, 100]}, {"id": 27, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 2}, {"conditionParam1": 0, "conditionParam2": 4, "conditionType": 1, "rating": 5, "skillId": 38}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 4, "skillId": 39}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 49}], "battlerHue": 0, "battlerName": "Succ<PERSON>us", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 4000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 32, "dataId": 5, "value": 0.2}], "gold": 8000, "name": "サキュバス", "note": "", "params": [6000, 3000, 150, 120, 100, 100, 110, 110]}, {"id": 28, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 4}, {"conditionParam1": 1, "conditionParam2": 3, "conditionType": 1, "rating": 4, "skillId": 36}, {"conditionParam1": 2, "conditionParam2": 7, "conditionType": 1, "rating": 8, "skillId": 41}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 4, "skillId": 71}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 4, "skillId": 54}], "battlerHue": 0, "battlerName": "Demon", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 6500, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 3, "value": 0.5}, {"code": 11, "dataId": 9, "value": 1.5}, {"code": 11, "dataId": 10, "value": 0.5}], "gold": 12000, "name": "デーモン", "note": "", "params": [8000, 5000, 170, 120, 120, 120, 120, 120]}, {"id": 29, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 6, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 54}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 58}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 62}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 72}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 46}, {"conditionParam1": 0, "conditionParam2": 0.8, "conditionType": 2, "rating": 5, "skillId": 24}], "battlerHue": 0, "battlerName": "Darklord", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 15000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 10, "value": 0}, {"code": 13, "dataId": 1, "value": 0}, {"code": 13, "dataId": 5, "value": 0}, {"code": 22, "dataId": 2, "value": 0.1}, {"code": 22, "dataId": 3, "value": 0.2}, {"code": 61, "dataId": 0, "value": 1}, {"code": 63, "dataId": 1, "value": 0}], "gold": 30000, "name": "魔王", "note": "", "params": [12000, 9999, 200, 150, 140, 140, 1, 140]}, {"id": 30, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 6, "skillId": 1}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 74}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 8}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 9}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 15}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 13}, {"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 12}], "battlerHue": 0, "battlerName": "Evilgod", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 25000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 100}, {"code": 11, "dataId": 10, "value": 0}, {"code": 13, "dataId": 1, "value": 0}, {"code": 13, "dataId": 5, "value": 0}, {"code": 61, "dataId": 0, "value": 1}, {"code": 63, "dataId": 1, "value": 0}], "gold": 50000, "name": "魔神", "note": "", "params": [20000, 9999, 240, 150, 160, 160, 150, 150]}]