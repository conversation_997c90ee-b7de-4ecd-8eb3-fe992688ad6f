// Coex 1.1

"use strict";

let CoEx;

{
/*
    Future Features:
    --------------------------------------
    Property Variablen -> sie können per 
    laufzeit manipuliert werden!
    ++++++++++++++++++++++++++++++++++++++
    isIf -> isBranch

    else
    else if
    ++++++++++++++++++++++++++++++++++++++
    Preprozessor Variablen
    ++++++++++++++++++++++++++++++++++++++
    better error messages!
    ++++++++++++++++++++++++++++++++++++++
    loops?
    --------------------------------------

    opcodes:
    --------------------------------------

    call: 
    { op:"call", args:[func, arguments...] }
    ; Aufrufen einer Funktion mit gegebenen Argumenten

    move: 
    { op:"move", args:[destionation, target] }
    ; Lagert einen Wert in eine Variable
    ; _ besondere stack variable, jeder call oder eval speichert darin sein ergebnis!

    jump:
    { op:"jump", args:[jump_index]}
    ; 

    slot:
    { op:"slot", args:[variable, index_true, index_false] }
    ; 

    link:
    { op:"link", args:[jump_and_link_index] }
    ;

    back:
    { op:"back", args:[] }
    ;

    noop:
    { op:"noop", args:[] }
    ; No Operation

    eval:
    { op:"eval",  }

    check? bool cond zero open cond if set new create setup slot
    args[a, ] a == true

    [
        { op:"call", args:[f2] },
        { op:"move", args:["result", "_"] },
        { op:"slot", args:["result", 3, 6] },
        { op:"call", args:[f0] },
        { op:"call", args:[f0] },
        { op:"call", args:[f0] },
        { op:"call", args:[f0] },
        { op:"call", args:[f0] },
        { op:"call", args:[f0] },
    ];

    if regex:
    /if[ ]*\([ ]*(.)*[ ]*\)(\n*{\n*(.*)\n*})?/g oder auch gm
*/

    // EVAL PARSE
    // Operation Regex
    //let isInstruction       = /^([a-zA-Z ]*)\((.*)\)$/;
    //let isVariableAssign    = /^[ ]*(\w*)[ ]*=[ ]*([^=]*)$/;    ///^[ ]*(\w*)[ ]*=[^=]*$/g;

    let isInvoke = /^([a-zA-Z ]*)\((.*)\)$/;
    let isAssign = /^[ ]*(\w*)[ ]*=[ ]*([^=]*)$/;
    let isIf = /^[ ]*if[ ]*\([ ]*([^\)]*)[ ]*\)([ ]*{[ ]*(.*)[ ]*})?$/; 
    // /^[ ]*if[ ]*\([ ]*(.*)[ ]*\)([ ]*{[ ]*(.*)[ ]*})?$/
    // /^[ ]*if[ ]*\([ ]*(.)*[ ]*\)(\n*{\n*(.*)\n*})?$/;
    
    //let seperateComma = /,(?=(?:(?:[^\"]*+\"){2})*+[^\"]*+$)/;

    let isVariable = /^[ ]*([^0-9 "]+[\w]*)[ ]*$/;        // /^[ ]*(\w*)[ ]*$/; 

    // Datentyp Regex
    let isString = /^[ ]*"([^"]*)"[ ]*$/;
    let isNumber = /^[ ]*((\d*)(\.\d+)?((e[+|\-]?)\d*)?)[ ]*$/;
    let isArray = /^[ ]*\[(.*)\][ ]*$/;                                     // simple
    let isObject = /^[ ]*\{(.*)\}[ ]*$/;

    function coex_split_instructions(script)
    {
        let output = [];
        let i, length = script.length, char, target_index;
        let flag_0 = true; // set target index

        let open_count = 0;
        let str_open_count = 0;
        let flag_1 = false; // string open


        let _colors = ["red", "blue", "green", "brown", "purple"];
        let lineCount = 0;

        console.time("coex-parse");
        for (i = 0; i < length; i++) 
        {
            // 
            if(flag_0)
            {
                flag_0 = false;
                target_index = i;
            }

            char = script[i];
            //console.log(char);

            if(char === '"')
            {
                flag_1 = !flag_1; 
            }

            if(char === "{")
            {
                open_count++;
            }

            if(char === "}")
            {
                open_count--;
            }

            if(char === ";")
            {
                if(open_count == 0 && !flag_1)
                {
                    flag_0 = true;
                    //console.log( script.substr(target_index, i - target_index) );  
                    //console.log(`%c${lineCount} %c${match[1]}`, "color:blue", `color:${_colors[lineCount % _colors.length]};`);
                    //console.log(`%c${lineCount} %c${script.substr(target_index, i - target_index)}`, "color:blue", `color:${_colors[lineCount % _colors.length]};`); 
                    output.push(script.substr(target_index, i - target_index));
                    lineCount++;
                }
                
            }
        }
        console.timeEnd("coex-parse");

        return output;
    }


    function coex_split_argument(str)
    {
        let output = [];
        let i, length = str.length, char, target_index, flag_0 = true, flag_1;

        for (i = 0; i < length; i++) 
        {
            if(flag_0)
            {
                flag_0 = false;
                target_index = i;
            }

            char = str[i];

            if(char === '"')
            {
                flag_1 = !flag_1; 
            }

            if(char === ",")
            {
                if(!flag_1)
                {
                    flag_0 = true;
                    output.push(str.substr(target_index, i - target_index));
                }
            }
        }

        output.push(str.substr(target_index, i - target_index));

        return output;
    }

    // todo
    //function coex_get_parameter

    // COEX
    CoEx = function(script, commands)
    {
        this.compiled = [];
        this.running = false;

        // ggf preprozessor variablen
        let variables = {};

        //let variables
        //console.log("COEX");
        //console.log(script);
        //console.log(commands);

        // Entfernt Inline Kommentare, Block Kommentare, Tabs und Zeilenumbrüche
        /* 
            Block: \/\*+([^*]|\*+[^\/]|[\n])*\*+\/|\/\/.*\n
            Inline: \/\/.*\n
            Tab & Zeilenumbruch: (?:\r\n|\r|\n)
        */
        script = script.replace(/\/\*+([^*]|\*+[^\/]|[\n])*\*+\/|\/\/.*\n|(?:\r\n|\r|\n)/g, '');

        function analyze(stack, instructions)
        {
            let length = instructions.length;
            for (let i = 0; i < length; i++) 
            {
                let instruction = instructions[i];

                // invoke           : x(args)
                if(isInvoke.test(instruction))
                {
                    invoke(stack, instruction);
                }

                // assign           : y = x(args)
                else if(isAssign.test(instruction))
                {
                    assign(stack, instruction);
                }

                // if               : 
                else if(isIf.test(instruction))
                {
                    branch(stack, instruction);
                }
                else
                {
                    // todo: improve
                    //console.error(`${ (script.substr(0, script.indexOf(line)).match(/\n/g) || []).length+1 }\n${i}: ${line}\ncannot parse line`);
                    console.error(`cannot parse @${i}: ${instruction} `)
                }
            }
        }

        function invoke(stack, str)
        {
            //console.log("%cisInvoke", "color:red");
            let match = str.match(isInvoke);
            let args = coex_split_argument(match[2]);

            for (let j = 0; j < args.length; j++) 
            {
                args[j] = parseValue2(args[j]);
            }

            stack.push({ op:"call", args: [commands[match[1].trim()], args]});
        }

        function assign(stack, str)
        {
            //console.log("%cisAssign", "color:green");

            // ggf nur wenn kein fehler auftritt
            let match = str.match(isAssign);
            //console.log(match);

            if(isInvoke.test(match[2]))
            {
                // special case
                //console.log("true");
                invoke(stack, match[2]);
                //console.log(match);
                stack.push({ op:"move", args:[match[1], "_"] });
            }
            else
            {
                //console.log("else");
                
                stack.push({ op:"eval", args:[match[1], match[2]] });

                //args = [match[1], parseValue(match[2], variables)];
                //String.fromCharCode(0x02)+
                //this.compiled.push({ op:"move", args: [match[1], String.fromCharCode(0x02)+match[2]]})
            }
        }

        function branch(stack, str)
        {
            //console.log("%cisIf", "color:blue");
            //console.log(str);

            let match = str.match(isIf);

            //console.log("%cmatch[1]", "color:orange");
            //console.log(match[1]);
            stack.push({ op:"eval", args:["_", match[1]] });

            let slot = { op:"slot", args:["_", stack.length+1, null] }
            stack.push(slot);

            //console.log("%cAAAAAA", "color:cyan");
            analyze(stack, coex_split_instructions(match[3]));

            // wenn vorgang vorbei ist, spring zurück und notier den index
            slot.args[2] = stack.length;

            //file:///D:/Home/Dropbox/Denedox/Projects/_playground/coex/index.html
        }

        // MAIN ANALYZE
        analyze(this.compiled, coex_split_instructions(script));
    }

    // Version 0.4
    // Coex runtime
    CoEx.prototype.Run = async function(completed)
    {
        // ggf als feature?
        if(!this.running)
        {
            this.running = true;

            let variables = {};
            let stack = [];

            let promise, opcode, args, arg, item, i = 0, j = 0, length = this.compiled.length, variable_identifier = String.fromCharCode(0x02);
            while(i < length) 
            {
                opcode = this.compiled[i].op;
                args = this.compiled[i].args;
                //console.log(`%c${i}: %c${opcode}`, "color:blue", "color:purple");

                // call move jump slot link back eval noop
                switch(opcode)
                {
                    case "call":
                        promise = new Promise((resolve, reject) => 
                        {
                            //console.log(`%c${args}`, "color:green");

                            arg = Object.assign([], args[1]);

                            // checking for variables
                            for (j = 0; j < arg.length; j++) 
                            {
                                item = arg[j];
                                if(typeof item === "string")
                                {
                                    if(item[0] === variable_identifier)
                                    {
                                        arg[j] = variables[item.substr(1, item.length)];
                                    }
                                }
                            }

                            args[0](resolve, arg);
                        });
                        variables["_"] = await promise;
                        i++;
                    break;

                    case "move":
                        variables[args[0]] = variables[args[1]];
                        i++;
                    break;

                    case "jump":
                        i = args[0];
                    break;

                    case "slot":
                        i = variables[args[0]] ? args[1] : args[2]; 
                    break;

                    case "link":
                        stack.push(i+1);
                        i = args[0];
                    break;

                    case "back":
                        i = stack.pop();
                    break;

                    case "eval":
                        variables[args[0]] = coex_eval(args[1], variables);
                        i++;
                    break;

                    case "noop":
                        i++;
                    break;

                    default:
                        i++;
                    break;
                }
            }

            //console.log(variables);
            //console.log("done");

            this.running = false;
            
            // possible to execute callback
            if(completed) completed();
            console.log("done!!!");
        }
    }

    async function Run(ejson)
    {
        //console.log(ejson);
        //
        let variables = {};
        let stack = [];

        let promise, opcode, args, arg, item, i = 0, j = 0, length = ejson.length, variable_identifier = String.fromCharCode(0x02);
        while(i < length) 
        {
            opcode = ejson[i].op;
            args = ejson[i].args;
            //console.log(`%c${i}: %c${opcode}`, "color:blue", "color:purple");


            /// ggf set get

            // call move jump slot link back eval noop
            //(String.fromCharCode(0x02)+"actor").includes(String.fromCharCode(0x02));
            switch(opcode)
            {
                case "call":
                    promise = new Promise((resolve, reject) => 
                    {
                        //console.log(`%c${args}`, "color:green");

                        arg = Object.assign([], args[1]);

                        // checking for variables
                        for (j = 0; j < arg.length; j++) 
                        {
                            item = arg[j];
                            if(typeof item === "string")
                            {
                                if(item[0] === variable_identifier)
                                {
                                    arg[j] = variables[item.substr(1, item.length)];
                                }
                            }
                        }

                        args[0](resolve, arg);
                    });
                    variables["_"] = await promise;
                    i++;
                break;

                case "move":
                    variables[args[0]] = variables[args[1]];
                    i++;
                break;

                case "jump":
                    i = args[0];
                break;

                case "slot":
                    i = variables[args[0]] ? args[1] : args[2]; 
                break;

                case "link":
                    stack.push(i+1);
                    i = args[0];
                break;

                case "back":
                    i = stack.pop();
                break;

                case "eval":
                    variables[args[0]] = coex_eval(args[1], variables);
                    i++;
                break;

                case "noop":
                    i++;
                break;

                default:
                    i++;
                break;
            }
        }

        //console.log(variables);
        //console.log("done");

        // possible to execute callback
        //if(completed) completed();
    }

    function parseType(str)
    {
        let output = null;

        if(isString.test(str)) output = "string";
        else if(isNumber.test(str)) output = "number";
        else if(isObject.test(str)) output = "object";
        else if(isArray.test(str)) output = "array";
        else if(isVariable.test(str)) output = "variable";
        else console.error(`invalid input: ${str}`);

        return output;
    }

    function parseValue(str, context)
    {
        let output = null; 
        //console.log(str);

        if(isString.test(str)) output = str.match(isString)[1];
        else if(isNumber.test(str)) output = Number.parseFloat(str);
        // TODO: put isObject and isArray to one regex
        else if(isObject.test(str)) output = JSON.parse(str.match(isObject)[0]);
        else if(isArray.test(str)) output = JSON.parse(str.match(isArray)[0])
        else if(isVariable.test(str)) output = context[str];
        else console.error(`invalid input: ${input}`);

        return output;
    }

    function parseValue2(str)
    {
        let output = null; 
        //console.log(str);

        if(isString.test(str)) output = str.match(isString)[1];
        else if(isNumber.test(str)) output = Number.parseFloat(str);
        // TODO: put isObject and isArray to one regex
        else if(isObject.test(str)) output = JSON.parse(str.match(isObject)[0]);
        else if(isArray.test(str)) output = JSON.parse(str.match(isArray)[0])
        else if(isVariable.test(str)) output = String.fromCharCode(0x02)+str;
        else console.error(`invalid input: ${input}`);

        return output;
    }

    function coex_eval(expr, context)
    {
        let output = null, regex = /[+*\-\/%<>]|==|!=|>=|<=|<<|>>/gm, values = expr.split(regex), index = 0, match, value;
        output = parseValue(values[index++].trim(), context);

        while(match = regex.exec(expr))
        {
            value = parseValue(values[index++].trim(), context);

            switch(match[0])
            {
                case "+":
                    output = output + value;
                break;

                case "-":
                    output = output - value;
                break;

                case "*":
                    output = output * value;
                break;

                case "/":
                    output = output / value;
                break;

                case "%":
                    output = output % value;
                break;

                case "<":
                    output = output < value;
                break;

                case ">":
                    output = output > value;
                break;

                case "==":
                    output = output == value;
                break;

                case "!=":
                    output = output != value;
                break;

                case ">=":
                    output = output >= value;
                break;

                case "<=":
                    output = output <= value;
                break;

                case ">>":
                    output = output >> value;
                break;

                case "<<":
                    output = output << value;
                break;
            }
        }

        return output;
    }

    CoEx.Run = Run;
    CoEx.eval = coex_eval;
}