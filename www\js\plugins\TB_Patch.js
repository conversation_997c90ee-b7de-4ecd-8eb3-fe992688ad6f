/*:
 * @plugindesc Check if a specific file exists in the www/data folder.
 * @help This plugin allows you to check if a specific file exists in the www/data folder.
 */

(function() {
    Game_Interpreter.prototype.checkFileExistence = function(filename) {
        var fs = require('fs');
        var path = require('path');
        var base = path.dirname(process.mainModule.filename);
        var filePath = path.join(base, 'www/data', filename);
        return fs.existsSync(filePath);
    };
})();
