/*:
 * @plugindesc ver.1.0.0 csv/DB_NPC.csvをプラグインコマンドから読み取ります。
 * <AUTHOR>
 * 
 * @param name_row
 * @text 名前 開始列
 * @type struct<RowValue>
 * @default {"row":"E","var_id":"462","type":"string"}
 * 
 * @param profile_row
 * @text プロフィール 開始列
 * @type struct<RowValue>
 * @default {"row":"J","var_id":"463","type":"string"}
 * 
 * @param rowVarList
 * @text 列・変数対応リスト
 * @type struct<RowValue>[]
 * @default []
 * 
 * @help
 * ※このプラグインはKNS_EnemyData以降に設置してください。
 * 
 * ■概要
 * csv/DB_NPC.csvをプラグインコマンドから読み取ります。
 * csvの以下の列に指定の要素を格納してください。
 * B ... マップID
 * C ... イベントID
 * D ... ページID（１始まり）
 * 
 * その他の列はプラグインパラメータから代入先変数を指定できます。
 * 
 * ■プラグインコマンド
 * KNS_NpcData load
 * 　現在のイベントのNPCデータを読み込みます。
 * （コマンド実行前に同イベント内でマップ移動が行われている場合は
 * 　イベントデータが取得できないためページ番号が1として扱われます）
 * 
 * KNS_NpcData load (マップid) (イベントid) (ページid)
 * 　指定したイベントのNPCデータを読み込みます。
 * 　各数値の指定を\v[1]の形式にすることで変数からの取得が可能です。
 * 
 * ■更新履歴
 * ver.1.0.0(2024/04/01)
 * - デモ
 */
/*~struct~RowValue:
 * @param row
 * @text 列
 * @type string
 * @default O
 * 
 * @param var_id
 * @text 代入先変数ID
 * @type variable
 * @default 462
 * 
 * @param type
 * @text 代入タイプ
 * @type select
 * @default number
 * 
 * @option 文字列
 * @value string
 * 
 * @option 数値
 * @value number
 */

const KNS_NpcData = {
    name: "KNS_NpcData",
    param: null,
    reVariable: /\\v\[(\d+)\]/,
    parseVariable: function(arg){
        if (this.reVariable.test(arg)){
            return $gameVariables.value(Number(RegExp.$1));
        }else{
            return Number(arg);
        }
    },
    parseRowValue: function(json){
        const obj = JsonEx.parse(json);
        obj.row = KNS_EnemyData.parseSheetRowIndex(obj.row);
        obj.var_id = Number(obj.var_id) || 0;
        obj.type = String(obj.type) || "string";
        return obj;
    },
    setRowValues: function(csv){
        let languageIndex = $gameVariables.value(1);
        [this.param.name_row, this.param.profile_row].forEach(function(rowValue){
            this.setRowValue(csv, rowValue.row + languageIndex, rowValue.var_id, rowValue.type);
        }, this);
        this.param.rowVarList.forEach(function(rowValue){
            this.setRowValue(csv, rowValue.row, rowValue.var_id, rowValue.type);
        }, this);
    },
    setRowValue: function(csv, row, varId, type){
        let value = csv ? csv[row] : 0;
        switch (type){
            case 'number': value = Number(value) || 0; break;
            default:       value = String(value) || ""; break;
        }
        $gameVariables.setValue(varId, value);
    },
    getCsv: function(mapId, eventId, pageId){
        if (
            $csvNpcs[mapId]
            && $csvNpcs[mapId][eventId]
            && $csvNpcs[mapId][eventId][pageId]
        ){
            return $csvNpcs[mapId][eventId][pageId];
        }else{
            return null;
        }
    }
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.name_row = this.parseRowValue(this.param.name_row);
    this.param.profile_row = this.parseRowValue(this.param.profile_row);
    this.param.rowVarList = JsonEx.parse(this.param.rowVarList).map(function(json){
        return this.parseRowValue(json);
    }, this);

    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_NpcData.name){ return; }
        switch (args[0].toLowerCase()){
            case 'load':{
                let mapId, eventId, pageIndex;
                if (args.length >= 2){
                    mapId = KNS_NpcData.parseVariable(args[1]);
                    eventId = KNS_NpcData.parseVariable(args[2]);
                    pageIndex = KNS_NpcData.parseVariable(args[3]);
                }else{
                    mapId = this._mapId;
                    eventId = this._eventId;
                    if (mapId !== $gameMap.mapId()){
                        console.error(`[${KNS_NpcData.name}]マップ移動済みのためページ番号が取得できません！`)
                        pageIndex = 1;
                    }else{
                        pageIndex = $gameMap.event(eventId)._pageIndex + 1;
                    }
                }
                const csv = KNS_NpcData.getCsv(mapId, eventId, pageIndex);
                if (!csv){
                    console.error(`[${KNS_NpcData.name}]No data(${mapId}, ${eventId}, ${pageIndex})`)
                }
                KNS_NpcData.setRowValues(csv);
                break;
            }
        }
    };

}).call(KNS_NpcData);