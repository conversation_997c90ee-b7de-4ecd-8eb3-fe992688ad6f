﻿/*:
 * @plugindesc ver.1.1.1 固定変数を実装します。
 * <AUTHOR>
 *
 * @param IdToFunctions
 * @name IDと指定変数
 * @type struct<IdToFunction>[]
 * @default ["{\"id\":\"10\",\"method\":\"\\\"//パーティ人数取得\\\\nreturn $gameParty ? $gameParty.size() : 0;\\\"\"}"]
 * @desc 固定する変数IDと取得時に実行するスクリプトを記述してください
 *
 * @help
 * 固定したい変数IDと実行内容をパラメータで指定し動的にその値を取得します。
 * 固定された変数は代入の影響を受けません。
 *
 * ver.1.0.0(2023-06-04)
 * - デモ
 * ver.1.1.0(2023-06-07)
 * - 固定変数をパラメータで指定できるよう変更しました。
 * - tp_healの回復量の符号を反転し、負数のときにTPが
 *   増えるよう変更しました。
 * ver.1.1.1(2023-06-10)
 * - tp_heal関数をKNS_EventLogに移動しました。
 */
/*~struct~IdToFunction:
 * @param id
 * @name 変数ID
 * @type variable
 * @default 10
 *
 * @param method
 * @name 取得関数
 * @type note
 * @default "//パーティ人数取得\nreturn $gameParty ? $gameParty.size() : 0;"
 *
 */
//=====================================================
// new KNS_Variables
//=====================================================
const KNS_Variables = {
	name: "KNS_Variables",
	param: null,
	idToFunctions: {}
};

(function(){
	this.param = PluginManager.parameters(this.name);
	JsonEx.parse(this.param.IdToFunctions).forEach(function(json){
		const obj = JsonEx.parse(json);
		this.idToFunctions[Math.floor(obj.id)] = new Function(JsonEx.parse(obj.method));
	}, this);

	const _Game_Variables_value = Game_Variables.prototype.value;
	Game_Variables.prototype.value = function(id){
		if (KNS_Variables.idToFunctions[id] === undefined){
			return _Game_Variables_value.apply(this, arguments);
		}
		return KNS_Variables.idToFunctions[id]();
	}

	const _Game_Variables_setValue = Game_Variables.prototype.setValue;
	Game_Variables.prototype.setValue = function(id, n){
		if (KNS_Variables.idToFunctions[id] === undefined){
			return _Game_Variables_setValue.apply(this, arguments);
		}
		this.onChange();
	}
}).call(KNS_Variables);