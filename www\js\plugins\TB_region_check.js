/*:
* @plugindesc Custom Step Event
* <AUTHOR> Name
*
*/

(function() {

    var _Game_Player_increaseSteps = Game_Player.prototype.increaseSteps;
    Game_Player.prototype.increaseSteps = function() {
        _Game_Player_increaseSteps.call(this);

        $gameVariables.value(333) >= 2 && AudioManager.playSe({name: "kuchu1_short", pan: 0, pitch: 100, volume: 100});

        if ($dataMap.note.includes("ダンジョン")) {
            $gameTemp.reserveCommonEvent(453);
        }

        if ($gameVariables.value(48) === 1 && $gameVariables.value(287) >= 1 && $dataMap.note.includes("ダンジョン") && $dataMap.note.includes("暗闇")) {
            $gameVariables.setValue(287, $gameVariables.value(287) - 1);
            if ($gameVariables.value(287) <= 0) {
                $gameTemp.reserveCommonEvent(469);
            }
        }

        if ($gameVariables.value(48) === 1 && $gameVariables.value(288) >= 1 && $dataMap.note.includes("ダンジョン") && $dataMap.note.includes("暗闇")) {
            $gameVariables.setValue(288, $gameVariables.value(288) - 1);
            if ($gameVariables.value(288) <= 0) {
                $gameTemp.reserveCommonEvent(469);
            }
        }

        var region = $gameMap.regionId(this.x, this.y);
        if ((region === 11 && !$gameSwitches.value(109)) || (region === 0 && $gameSwitches.value(109))) {
            $gameTemp.reserveCommonEvent(458);
        }
    };

})();
