"use strict";let CoEx;{let e=/^([\w ]*)\((.*)\)$/,t=/^[ ]*(\w*)[ ]*=[ ]*([^=]*)$/,s=/^[ ]*if[ ]*\([ ]*([^\)]*)[ ]*\)([ ]*{[ ]*(.*)[ ]*})?$/,a=/^[ ]*([^0-9 "]+[\w]*)[ ]*$/,r=/^[ ]*"([^"]*)"[ ]*$/,n=/^[ ]*((\d*)(\.\d+)?((e[+|\-]?)\d*)?)[ ]*$/,o=/^[ ]*\[(.*)\][ ]*$/,c=/^[ ]*\{(.*)\}[ ]*$/;function coex_split_instructions(e){let t,s,a,r=[],n=e.length,o=!0,c=0,l=!1;for(console.time("coex-parse"),t=0;t<n;t++)o&&(o=!1,a=t),'"'===(s=e[t])&&(l=!l),"{"===s&&c++,"}"===s&&c--,";"===s&&(0!=c||l||(o=!0,r.push(e.substr(a,t-a)),0));return console.timeEnd("coex-parse"),r}function coex_split_argument(e){let t,s,a,r,n=[],o=e.length,c=!0;for(t=0;t<o;t++)c&&(c=!1,a=t),'"'===(s=e[t])&&(r=!r),","===s&&(r||(c=!0,n.push(e.substr(a,t-a))));return n.push(e.substr(a,t-a)),n}async function Run(e){let t,s,a,r,n,o={},c=[],l=0,i=0,u=e.length,p=String.fromCharCode(2);for(;l<u;)switch(s=e[l].op,a=e[l].args,s){case"call":t=new Promise((e,t)=>{for(r=Object.assign([],a[1]),i=0;i<r.length;i++)"string"==typeof(n=r[i])&&n[0]===p&&(r[i]=o[n.substr(1,n.length)]);a[0](e,r)}),o._=await t,l++;break;case"move":o[a[0]]=o[a[1]],l++;break;case"jump":l=a[0];break;case"slot":l=o[a[0]]?a[1]:a[2];break;case"link":c.push(l+1),l=a[0];break;case"back":l=c.pop();break;case"eval":o[a[0]]=coex_eval(a[1],o),l++;break;case"noop":default:l++}}function parseType(e){let t=null;return r.test(e)?t="string":n.test(e)?t="number":c.test(e)?t="object":o.test(e)?t="array":a.test(e)?t="variable":console.error(`invalid input: ${e}`),t}function parseValue(e,t){let s=null;return r.test(e)?s=e.match(r)[1]:n.test(e)?s=Number.parseFloat(e):c.test(e)?s=JSON.parse(e.match(c)[0]):o.test(e)?s=JSON.parse(e.match(o)[0]):a.test(e)?s=t[e]:console.error(`invalid input: ${input}`),s}function parseValue2(e){let t=null;return r.test(e)?t=e.match(r)[1]:n.test(e)?t=Number.parseFloat(e):c.test(e)?t=JSON.parse(e.match(c)[0]):o.test(e)?t=JSON.parse(e.match(o)[0]):a.test(e)?t=String.fromCharCode(2)+e:console.error(`invalid input: ${input}`),t}function coex_eval(e,t){let s,a,r=null,n=/[+*\-\/%<>]|==|!=|>=|<=|<<|>>/gm,o=e.split(n),c=0;for(r=parseValue(o[c++].trim(),t);s=n.exec(e);)switch(a=parseValue(o[c++].trim(),t),s[0]){case"+":r+=a;break;case"-":r-=a;break;case"*":r*=a;break;case"/":r/=a;break;case"%":r%=a;break;case"<":r=r<a;break;case">":r=r>a;break;case"==":r=r==a;break;case"!=":r=r!=a;break;case">=":r=r>=a;break;case"<=":r=r<=a;break;case">>":r>>=a;break;case"<<":r<<=a}return r}(CoEx=function(a,r){function n(a,r){let n=r.length;for(let i=0;i<n;i++){let n=r[i];e.test(n)?o(a,n):t.test(n)?c(a,n):s.test(n)?l(a,n):console.error(`cannot parse @${i}: ${n} `)}}function o(t,s){let a=s.match(e),n=coex_split_argument(a[2]);for(let e=0;e<n.length;e++)n[e]=parseValue2(n[e]);t.push({op:"call",args:[r[a[1].trim()],n]})}function c(s,a){let r=a.match(t);e.test(r[2])?(o(s,r[2]),s.push({op:"move",args:[r[1],"_"]})):s.push({op:"eval",args:[r[1],r[2]]})}function l(e,t){let a=t.match(s);e.push({op:"eval",args:["_",a[1]]});let r={op:"slot",args:["_",e.length+1,null]};e.push(r),n(e,coex_split_instructions(a[3])),r.args[2]=e.length}this.compiled=[],this.running=!1,a=a.replace(/\/\*+([^*]|\*+[^\/]|[\n])*\*+\/|\/\/.*\n|(?:\r\n|\r|\n)/g,""),n(this.compiled,coex_split_instructions(a))}).prototype.Run=async function(e){if(!this.running){this.running=!0;let t,s,a,r,n,o={},c=[],l=0,i=0,u=this.compiled.length,p=String.fromCharCode(2);for(;l<u;)switch(s=this.compiled[l].op,a=this.compiled[l].args,s){case"call":t=new Promise((e,t)=>{for(r=Object.assign([],a[1]),i=0;i<r.length;i++)"string"==typeof(n=r[i])&&n[0]===p&&(r[i]=o[n.substr(1,n.length)]);a[0](e,r)}),o._=await t,l++;break;case"move":o[a[0]]=o[a[1]],l++;break;case"jump":l=a[0];break;case"slot":l=o[a[0]]?a[1]:a[2];break;case"link":c.push(l+1),l=a[0];break;case"back":l=c.pop();break;case"eval":o[a[0]]=coex_eval(a[1],o),l++;break;case"noop":default:l++}this.running=!1,e&&e()}},CoEx.Run=Run,CoEx.eval=coex_eval}