let debug_sync_root,$HTML={};function Component(t,e){Object.assign(this,e),this.Element=t,this.Markup=t.innerHTML,this.Start&&this.Start()}function Template(t="",e=""){this.Markup=t,this.Script=e}$HTML.Templates={},$HTML.ComponentList=[],Component.prototype.Show=function(){this.Element.style.display=""},Component.prototype.Hide=function(){this.Element.style.display="none"},Template.prototype.Create=function({display:t="none"}={}){let e=document.createElement("div");e.className="component",e.innerHTML=this.Markup,e.style.display=t;let n=new Function(this.Script)();return n.Construct&&n.Construct(n),new Component(e,n)},$HTML.CreateComponent=function(t,{display:e="none"}={}){let n,i=$HTML.Templates[t];return i&&(n=i.Create(arguments[1]),this.ComponentList.push(n),this.ElementView.appendChild(n.Element),n.Append&&n.Append()),n};{let t=document.createElement("div");t.id="root",document.body.appendChild(t);let e=document.createElement("div");e.id="view",t.appendChild(e),$HTML.ElementRoot=t,$HTML.ElementView=e;let n=Require("fs"),i=n.readdirSync("./html"),o=[],s=[];for(const t of i)n.statSync(`./html/${t}`).isDirectory()&&o.push(t);for(const t of o){let e=n.readdirSync(`./html/${t}`),i={};i.name=t;for(const n of e)n===`${t}.html`?i.markup=`./html/${t}/${t}.html`:n===`${t}.js`&&(i.script=`./html/${t}/${t}.js`);s.push(i)}for(const t of s){let e="<div>NO MARKUP FOUND!</div>",i="return {};";t.markup&&(e=n.readFileSync(t.markup).toString()),t.script&&(i=n.readFileSync(t.script).toString()),$HTML.Templates[t.name]=new Template(e,i)}let a=function(){t.style.width=GameCanvas.style.width,t.style.height=GameCanvas.style.height,e.style.width=GameCanvas.width+"px",e.style.height=GameCanvas.height+"px";let n=GameCanvas.getBoundingClientRect();e.style.transform=`scale(${n.width/GameCanvas.width},${n.height/GameCanvas.height})`};window.addEventListener("load",function(){a(),window.addEventListener("resize",function(){a()})}),window.addEventListener("keydown",function(t){114==t.keyCode&&a()})}