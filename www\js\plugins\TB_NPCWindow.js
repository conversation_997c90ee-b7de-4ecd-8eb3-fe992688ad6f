/*:
 * @plugindesc Info Window Plugin that shows and hides an information window. Position can be set via plugin commands.
 * <AUTHOR> Name
 *
 * @help Use the plugin commands:
 * ShowInfoWindow x y  - Shows the window at position (x, y).
 * HideInfoWindow      - Hides the window.
 */

(function() {
    var _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.call(this, command, args);
        if (command === 'ShowInfoWindow') {
            var x = parseInt(args[0]);
            var y = parseInt(args[1]);
            SceneManager._scene.createInfoWindow(x, y);
        } else if (command === 'HideInfoWindow') {
            SceneManager._scene.hideInfoWindow();
        }
    };

    Scene_Map.prototype.createInfoWindow = function(x, y) {
        if (!this._infoWindow) {
            this._infoWindow = new Window_Info(x, y);
            this.addChild(this._infoWindow);
        }
    };

    Scene_Map.prototype.hideInfoWindow = function() {
        if (this._infoWindow) {
            this.removeChild(this._infoWindow);
            this._infoWindow = null;
        }
    };

    function Window_Info(x, y) {
        this.initialize(x, y);
    }

    Window_Info.prototype = Object.create(Window_Base.prototype);
    Window_Info.prototype.constructor = Window_Info;

    Window_Info.prototype.initialize = function(x, y) {
        var width = 400;
        var height = this.fittingHeight(6);
        Window_Base.prototype.initialize.call(this, x, y, width, height);
        this.refresh();
    };

    Window_Info.prototype.refresh = function() {
        this.contents.clear();
        this.displayVariable(462, 0);
        this.displayVariable(463, this.lineHeight());
        this.displayVariableWithName(465, this.lineHeight() * 2);
        this.displayVariableWithName(466, this.lineHeight() * 3);
        this.drawLine(this.lineHeight() * 4);
        this.displayVariable(474, this.lineHeight() * 5);
    };

    Window_Info.prototype.displayVariable = function(variableId, yPos) {
        var value = $gameVariables.value(variableId);
        this.drawText(value, 0, yPos, this.contents.width, 'left');
    };

    Window_Info.prototype.displayVariableWithName = function(variableId, yPos) {
        var name = $dataSystem.variables[variableId];
        var value = $gameVariables.value(variableId);
        this.drawText(name + ": " + value, 0, yPos, this.contents.width, 'left');
    };

    Window_Info.prototype.drawLine = function(yPos) {
        this.contents.fillRect(0, yPos, this.contents.width, 2, this.normalColor());
    };
})();
