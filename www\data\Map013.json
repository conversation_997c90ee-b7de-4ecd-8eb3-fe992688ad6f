{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 80}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 50, "note": "別行動不可\n夜固定BGM\n王都\n拠点\n冒険者ギルド\n公共の場", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 50, "data": [5988, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6374, 1609, 1610, 1611, 6371, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6380, 1609, 1610, 1611, 6377, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5986, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6020, 0, 0, 6018, 6010, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6022, 0, 0, 6024, 6022, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3428, 3962, 3426, 3412, 3412, 3412, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 3952, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3430, 3952, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3953, 3953, 3953, 3953, 3953, 3953, 3953, 3953, 3953, 3953, 3959, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3412, 3393, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3420, 3400, 3396, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 874, 0, 785, 0, 0, 0, 0, 0, 833, 834, 0, 0, 0, 0, 0, 785, 0, 874, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 882, 0, 793, 0, 0, 0, 0, 0, 841, 842, 0, 0, 0, 0, 0, 793, 0, 882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 890, 0, 591, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 591, 0, 890, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 408, 409, 408, 409, 408, 409, 408, 409, 408, 409, 408, 409, 408, 411, 0, 0, 0, 408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 366, 365, 366, 365, 366, 0, 322, 0, 500, 501, 0, 0, 276, 0, 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 373, 374, 373, 374, 373, 374, 0, 0, 0, 508, 509, 0, 0, 284, 0, 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 381, 382, 381, 382, 381, 382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 728, 729, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 736, 737, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 368, 369, 368, 369, 0, 0, 0, 368, 369, 368, 369, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 349, 350, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 357, 358, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["新OP - 司祭の冒険者登録"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 125, "indent": 0, "parameters": [0, 0, 50]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-12"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 121, "indent": 0, "parameters": [207, 207, 0]}, {"code": 201, "indent": 0, "parameters": [0, 8, 3, 11, 2, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 207, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [176, 176, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 35, "indent": null}, {"code": 34, "indent": null}, {"code": 32, "indent": null}, {"code": 30, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 30, "parameters": [3], "indent": null}, {"code": 31, "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 30, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 30, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [176, 176, 1]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15-24"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_25-26"]}, {"code": 121, "indent": 0, "parameters": [176, 176, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [176, 176, 1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レオンが来る"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_27"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_28-29"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 37, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 37, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レオンたちとの会話"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 3, 60, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-56"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 3, 70, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_61"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_71"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 3, 120, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-102"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_121"]}, {"code": 118, "indent": 0, "parameters": ["ギルドボード"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [326, 326, 0, 3, 3, 1, 0]}, {"code": 122, "indent": 0, "parameters": [327, 327, 0, 3, 3, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["SceneManager.call(Scene_KNSReceiveQuest)"]}, {"code": 117, "indent": 0, "parameters": [936]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [1, 903, 0, 1, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_150-152"]}, {"code": 121, "indent": 1, "parameters": [228, 228, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_160"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_170"]}, {"code": 119, "indent": 1, "parameters": ["ギルドボード"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 121, "indent": 0, "parameters": [262, 262, 0]}, {"code": 121, "indent": 0, "parameters": [228, 228, 0]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, \"quest_900102\")"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_quest_start\")"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 228, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 903, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１　初心者冒険者訓練"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 122, "indent": 0, "parameters": [904, 904, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエスト　行方不明者の捜索"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [960]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [960]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [9, 0, 16, 15, 0]}, {"code": 203, "indent": 0, "parameters": [10, 0, 17, 15, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [9, 7, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 7, 10, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [9, 1, false]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-26"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 7, 30, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 13, 1, 7, 40, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41-42"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [9, 0, 49, 2, 0]}, {"code": 203, "indent": 0, "parameters": [10, 0, 49, 3, 0]}, {"code": 122, "indent": 0, "parameters": [905, 905, 0, 0, 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, \"quest_900104\")"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_quest_start\")"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 905, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 906, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエスト　『殉教者』"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [960]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [960]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [47, 0, 15, 13, 2]}, {"code": 205, "indent": 0, "parameters": [47, {"list": [{"code": 41, "parameters": ["Actor1", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Actor1", 2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [47, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [47, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 9, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 13, 1, 9, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-6"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-38"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [7, 0, 11, 13, 8]}, {"code": 203, "indent": 0, "parameters": [8, 0, 10, 13, 8]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([13, 7, 'A'], true);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([13, 8, 'A'], true);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-52"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([13, 7, 'A'], false);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([13, 8, 'A'], false);"]}, {"code": 122, "indent": 0, "parameters": [908, 908, 0, 0, 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, \"quest_900107\")"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_quest_start\")"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 908, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 910, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１　クエスト　『物資輸送』"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 122, "indent": 0, "parameters": [910, 910, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 910, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 911, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１　クエスト　『神の意思』　ラストシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 201, "indent": 0, "parameters": [0, 335, 9, 6, 2, 0]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 223, "indent": 0, "parameters": [[68, -34, -34, 0], 60, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-25"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 13, 30, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 13, 1, 13, 40, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 243, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 13, 60, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 1, 13, 70, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 13, 1, 13, 80, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_71"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81-82"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_83-86"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 121, "indent": 0, "parameters": [177, 177, 0]}, {"code": 122, "indent": 0, "parameters": [911, 911, 0, 0, 98]}, {"code": 117, "indent": 0, "parameters": [46]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 0, "parameters": [177, 177, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 122, "indent": 0, "parameters": [334, 334, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(1502, \"location_name1\")"]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1522]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 0, 17]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 0, 8]}, {"code": 122, "indent": 0, "parameters": [335, 335, 0, 1, 42]}, {"code": 122, "indent": 0, "parameters": [336, 336, 0, 1, 43]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["$WMTS.X = $gameVariables.value(335)"]}, {"code": 655, "indent": 0, "parameters": ["$WMTS.Y = $gameVariables.value(336)"]}, {"code": 121, "indent": 0, "parameters": [6, 6, 1]}, {"code": 314, "indent": 0, "parameters": [0, 0]}, {"code": 326, "indent": 0, "parameters": [0, 0, 1, 0, 100]}, {"code": 121, "indent": 0, "parameters": [6, 6, 0]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 201, "indent": 0, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["オマケモード　イベント『サリー IF』開放"]}, {"code": 355, "indent": 0, "parameters": ["KNS_Gallery.setProgress(97, 1, 1);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, `GalleryNewSally1`)"]}, {"code": 356, "indent": 0, "parameters": ["KNS_SlideShow show serif left 360 5"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 911, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 205, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 8, 3, 11, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 19}, {"id": 3, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 205, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 8, 3, 11, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 19}, {"id": 4, "name": "ギルドボード", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 264, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 6, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエストボード本体"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [326, 326, 0, 3, 3, 1, 0]}, {"code": 122, "indent": 0, "parameters": [327, 327, 0, 3, 3, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["SceneManager.call(Scene_KNSReceiveQuest)"]}, {"code": 117, "indent": 0, "parameters": [936]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 14}, {"id": 5, "name": "ちょっかい出してくるヤツ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "worrior_male", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 11}, {"id": 6, "name": "ちょっかい出してくるヤツの相方", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "wizard_female", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 10}, {"id": 7, "name": "レオン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "npc_leon", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "npc_leon", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 12}, {"id": 8, "name": "アイリス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 12}, {"id": 9, "name": "ミカエル", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "h001", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 1"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 49, "y": 3}, {"id": 10, "name": "サリー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "$sally", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 1"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 49, "y": 2}, null, {"id": 12, "name": "ギルド受付担当", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["旧OPイベントあった場所"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["通常ギルド"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****"]}, {"code": 408, "indent": 0, "parameters": ["* クエスト受けてないときは色々話きけるページ"]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ギルド受付嬢\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_quest_info\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_quest_report\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_map13_ev12_201\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(64, \"select_rank_up\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(70, \"select_rescue_pri\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] if(v[1]>9999)", "\\v[62]", "\\v[63] if(v[1]==0)", "\\v[64] if(v[1]>9999)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] if(v[1]>9999)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 117, "indent": 1, "parameters": [46]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] if(v[1]==0)"]}, {"code": 118, "indent": 1, "parameters": ["質問"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map13_ev12_2011\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_map13_ev12_2012\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(63, \"select_map13_ev12_2013\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(64, \"select_map13_ev12_2014\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2-5"]}, {"code": 119, "indent": 2, "parameters": ["質問"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10-12"]}, {"code": 119, "indent": 2, "parameters": ["質問"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_20-25"]}, {"code": 119, "indent": 2, "parameters": ["質問"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\v[64]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_30-35"]}, {"code": 119, "indent": 2, "parameters": ["質問"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64] if(v[1]>9999)"]}, {"code": 108, "indent": 1, "parameters": ["昇級申請"]}, {"code": 108, "indent": 1, "parameters": ["※キャップ"]}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 3, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_not_yet\")"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["データ処理"]}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 150]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 3, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 400]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 4, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 1000]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 9, 0, 5, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 5000]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["昇級確認"]}, {"code": 111, "indent": 1, "parameters": [1, 322, 1, 54, 1]}, {"code": 249, "indent": 2, "parameters": [{"name": "harmonic-victory3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 9]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 111, "indent": 2, "parameters": [1, 9, 0, 2, 0]}, {"code": 126, "indent": 3, "parameters": [886, 1, 0, 1]}, {"code": 126, "indent": 3, "parameters": [887, 0, 0, 1]}, {"code": 111, "indent": 3, "parameters": [1, 990, 0, 2, 0]}, {"code": 122, "indent": 4, "parameters": [990, 990, 0, 0, 99]}, {"code": 250, "indent": 4, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 4, "parameters": ["val_in_database(54, \"quest_1\")"]}, {"code": 355, "indent": 4, "parameters": ["$qname = $gameVariables.value(54)"]}, {"code": 355, "indent": 4, "parameters": ["set_mlog(`Quest Complete : ${$qname}`)"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 4, "parameters": [""]}, {"code": 355, "indent": 4, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 9, 0, 3, 0]}, {"code": 126, "indent": 3, "parameters": [887, 1, 0, 1]}, {"code": 126, "indent": 3, "parameters": [888, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 9, 0, 4, 0]}, {"code": 126, "indent": 3, "parameters": [888, 1, 0, 1]}, {"code": 126, "indent": 3, "parameters": [889, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 9, 0, 5, 0]}, {"code": 126, "indent": 3, "parameters": [889, 1, 0, 1]}, {"code": 126, "indent": 3, "parameters": [890, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_rank_up_need\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[70] if(v[261]>=1)"], 0, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[70] if(v[261]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["司祭救出要請"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(13, 53, 1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(903) == 1;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１クエスト　\"地下下水の調査\""]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ギルド受付嬢\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 126, "indent": 0, "parameters": [62, 0, 0, 3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [903, 903, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, \"quest_901002\")"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_quest_update\")"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(904) == 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１クエスト　\"初心者冒険者の訓練\""]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ギルド受付嬢\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 111, "indent": 0, "parameters": [1, 322, 0, 10, 1]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 13, 12, 4, 30, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 13, 12, 4, 40, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [904, 904, 0, 0, 3]}, {"code": 201, "indent": 2, "parameters": [0, 336, 17, 17, 8, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(13, 12, 2)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(905) == 7;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１クエスト　\"祈りと対価\""]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ギルド受付嬢\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-9"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-22"]}, {"code": 122, "indent": 0, "parameters": [905, 905, 0, 0, 98]}, {"code": 117, "indent": 0, "parameters": [46]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(906) == 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().some(actor => actor.actorId() === 1) && $gameParty.members().some(actor => actor.actorId() === 2)"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チャプター１クエスト　\"ゴブリン掃討作戦\""]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ギルド受付嬢\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [1, 322, 0, 30, 1]}, {"code": 122, "indent": 1, "parameters": [72, 72, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [72, 72, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 12, 6, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 13, 12, 6, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[72]>=1)", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[72]>=1)"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [906, 906, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 340, 34, 69, 8, 0]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(13, 12, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 262, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 17, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["!$gameParty.members().some(actor => actor.actorId() === 1) || !$gameParty.members().some(actor => actor.actorId() === 2)"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["パーティに主人公と司祭がそろっていないときは強制的に通常ページを呼び出す"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(13, 12, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 10}, {"id": 13, "name": "受付嬢", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 10}, {"id": 14, "name": "受付嬢", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 10}, {"id": 15, "name": "受付嬢", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 10}, {"id": 16, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 14}, {"id": 17, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 15}, {"id": 18, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 12}, {"id": 19, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 12}, {"id": 20, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 12}, {"id": 21, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_10\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 17}, {"id": 22, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 9, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 16}, {"id": 23, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 6, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 4}, {"id": 24, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Monster1", "direction": 4, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 4}, {"id": 25, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 6, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 13}, {"id": 26, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 13}, {"id": 27, "name": "NPC-冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 9, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 4, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 14}, {"id": 28, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 8}, {"id": 29, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor4", "direction": 4, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 9}, {"id": 30, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 10}, {"id": 31, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 9}, {"id": 32, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 14}, {"id": 33, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 16}, {"id": 34, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor4", "direction": 6, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 15}, {"id": 35, "name": "NPC-冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 1, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 13, 35, 1, 100, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 13, 35, 1, 200, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_101-103"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 122, "indent": 2, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 122, "indent": 2, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 117, "indent": 2, "parameters": [4]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["var key = [129, 12, \"A\"];"]}, {"code": 655, "indent": 2, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 201, "indent": 2, "parameters": [0, 129, 6, 7, 2, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_201"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者　その１　リピート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 1, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 1, 200, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var key = [129, 12, \"A\"];"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 201, "indent": 1, "parameters": [0, 129, 6, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者　その２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 3, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 3, 30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["ダンジョンへ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var key = [206, 11, \"A\"];"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 201, "indent": 1, "parameters": [0, 206, 2, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-32"]}, {"code": 213, "indent": 1, "parameters": [-1, 2, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_33"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者　その３"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 4, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 4, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var key = [129, 12, \"B\"];"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 201, "indent": 1, "parameters": [0, 129, 6, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者　その４"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 4, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 4, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 121, "indent": 1, "parameters": [19, 19, 1]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var key = [129, 12, \"C\"];"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 201, "indent": 1, "parameters": [0, 129, 6, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["協力-マジメな冒険者　その５（FINAL）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 35, 4, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 35, 4, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 117, "indent": 1, "parameters": [4]}, {"code": 121, "indent": 1, "parameters": [19, 19, 1]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var key = [129, 12, \"D\"];"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 129, 6, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.size() === 1;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 3}, {"id": 36, "name": "NPC-冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 270, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "chara-majime", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 9, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 830, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.size() === 1;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 3}, {"id": 37, "name": "アルファ用クエスト開放", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [1, 906, 0, 0, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(54, \"quest_900105\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_quest_start\")"]}, {"code": 122, "indent": 1, "parameters": [906, 906, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_story_navi_ch1_10\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 906, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 19}, {"id": 38, "name": "NPC-冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior2", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [61, 61, 0, 4, "\"戦闘について\""]}, {"code": 122, "indent": 1, "parameters": [62, 62, 0, 4, "\"敗北について\""]}, {"code": 122, "indent": 1, "parameters": [63, 63, 0, 4, "\"クエスト中のイベントについて\""]}, {"code": 122, "indent": 1, "parameters": [64, 64, 0, 4, "\"トラップについて\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["選択"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-106"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-203"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301-302"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_401"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["一旦削除"]}, {"code": 408, "indent": 0, "parameters": ["0.11から復活"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 8}, {"id": 39, "name": "レオン 階段上", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 261, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "npc_leon", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon=8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(1005) == 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_quest103_9a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_quest103_9b\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3-6"]}, {"code": 117, "indent": 1, "parameters": [55]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_7-10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 5}, {"id": 40, "name": "アイリス　階段上", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 261, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon=8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return v.value(1005) == 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 39, 3)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 4}, null, {"id": 42, "name": "ケツデカ冒険者ダークエルフ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "op_ad_de", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 202, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 15}, {"id": 43, "name": "ケツデカ冒険者人間", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "op_ad_hu", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 202, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 15}, {"id": 44, "name": "ケツデカ冒険者エルフ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "op_ad_el", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 202, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 15}, {"id": 45, "name": "ヤリゾー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 291, "switch1Valid": true, "switch2Id": 302, "switch2Valid": false, "variableId": 9, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ヤリゾー\""]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-10"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 45, 1, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 45, 1,30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-32"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 45, 1, 40, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_43-51"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 13, 45, 1, 60, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 13, 45, 1,80, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_61-65"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 13, 45, 1, 70, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_71-73"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_74"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [643, 643, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [496]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81-82"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 503, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  const v = $gameVariables;"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.size() === 1;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 11}, {"id": 46, "name": "チャプ３以降：操作キャラ切り替え", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [940]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 9}, {"id": 47, "name": "配置用キャラ１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 2}, {"id": 48, "name": "配置用キャラ２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 3}, {"id": 49, "name": "配置用キャラ３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 4}, {"id": 50, "name": "配置用キャラ４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 5}, {"id": 51, "name": "配置用キャラ５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 6}, {"id": 52, "name": "夜は強制移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 8, 3, 11, 2, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 49, "y": 0}, {"id": 53, "name": "司祭救出依頼", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 122, "indent": 0, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[11]>=300)", "\\v[62]"], 1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[11]>=300)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 1, "parameters": [47, 0, 10, 12, 6]}, {"code": 203, "indent": 1, "parameters": [48, 0, 10, 13, 6]}, {"code": 205, "indent": 1, "parameters": [47, {"list": [{"code": 41, "parameters": ["Actor3", 6], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["Actor3", 6], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [48, {"list": [{"code": 41, "parameters": ["Actor3", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["Actor3", 0], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 125, "indent": 1, "parameters": [1, 0, 300]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 322]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ぶっかけ関連イニシャライズ"]}, {"code": 122, "indent": 1, "parameters": [1701, 2600, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 1, "parameters": [90, 90, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 71]}, {"code": 122, "indent": 1, "parameters": [91, 91, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 72]}, {"code": 122, "indent": 1, "parameters": [92, 92, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 73]}, {"code": 122, "indent": 1, "parameters": [93, 93, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 74]}, {"code": 122, "indent": 1, "parameters": [94, 94, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 75]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 31]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 31]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["合流処理"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90"]}, {"code": 121, "indent": 1, "parameters": [128, 128, 0]}, {"code": 117, "indent": 1, "parameters": [287]}, {"code": 121, "indent": 1, "parameters": [128, 128, 1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 203, "indent": 1, "parameters": [47, 0, 10, 12, 6]}, {"code": 203, "indent": 1, "parameters": [48, 0, 10, 13, 6]}, {"code": 205, "indent": 1, "parameters": [47, {"list": [{"code": 41, "parameters": ["Actor3", 6], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["Actor3", 6], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [48, {"list": [{"code": 41, "parameters": ["Actor3", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["Actor3", 0], "indent": null}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_91"]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 150, 1]}, {"code": 111, "indent": 2, "parameters": [1, 82, 1, 83, 3]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_101-102"]}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 213, "indent": 3, "parameters": [-1, 2, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_110"]}, {"code": 122, "indent": 3, "parameters": [501, 501, 1, 0, 2]}, {"code": 122, "indent": 3, "parameters": [402, 411, 1, 0, 2]}, {"code": 122, "indent": 3, "parameters": [420, 420, 1, 0, 10]}, {"code": 122, "indent": 3, "parameters": [2620, 2620, 1, 2, 100, 200]}, {"code": 122, "indent": 3, "parameters": [2720, 2720, 1, 2, 100, 200]}, {"code": 122, "indent": 3, "parameters": [2820, 2820, 1, 2, 100, 200]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [47, {"list": [{"code": 41, "parameters": ["", 6], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["", 6], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [48, {"list": [{"code": 41, "parameters": ["", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["", 0], "indent": null}]}, {"code": 203, "indent": 1, "parameters": [47, 0, 0, 2, 6]}, {"code": 203, "indent": 1, "parameters": [48, 0, 0, 3, 6]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 0}]}