/*:
 * @plugindesc Info Window Plugin that shows and hides an information window with specific variable contents on the left side of the screen.
 * <AUTHOR> Name
 *
 * @help This plugin does not provide plugin commands.
 */

(function() {
    var _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.call(this, command, args);
        if (command === 'ShowInfoWindow') {
            SceneManager._scene.createInfoWindow();
        }
        if (command === 'HideInfoWindow') {
            SceneManager._scene.hideInfoWindow();
        }
    };

    Scene_Map.prototype.createInfoWindow = function() {
        this._infoWindow = new Window_Info();
        this.addChild(this._infoWindow);
    };

    Scene_Map.prototype.hideInfoWindow = function() {
        if (this._infoWindow) {
            this.removeChild(this._infoWindow);
            this._infoWindow = null;
        }
    };

    function Window_Info() {
        this.initialize.apply(this, arguments);
    }

    Window_Info.prototype = Object.create(Window_Base.prototype);
    Window_Info.prototype.constructor = Window_Info;

    Window_Info.prototype.initialize = function() {
        var width = 400;
        var height = this.fittingHeight(6); // Enough space for 6 lines
        var x = 0; // Set x to 0 to align to the left side
        var y = (Graphics.boxHeight - height) / 2; // Center vertically
        Window_Base.prototype.initialize.call(this, x, y, width, height);
        this.refresh();
    };

    Window_Info.prototype.refresh = function() {
        this.contents.clear();
        var yPos = 0;
        // Displaying Variable 462
        this.displayVariable(462, yPos);
        yPos += this.lineHeight();

        // Displaying Variable 463
        this.displayVariable(463, yPos);
        yPos += this.lineHeight();

        // Displaying Variable 465 with name
        this.displayVariableWithName(465, yPos);
        yPos += this.lineHeight();

        // Displaying Variable 466 with name
        this.displayVariableWithName(466, yPos);
        yPos += this.lineHeight();

        // Drawing line after Variable 466
        this.drawLine(yPos);
        yPos += this.lineHeight();

        // Displaying Variable 474
        this.displayVariable(474, yPos);
    };

    Window_Info.prototype.displayVariable = function(variableId, yPos) {
        var value = $gameVariables.value(variableId);
        this.drawText(value, 0, yPos, this.contents.width, 'left');
    };

    Window_Info.prototype.displayVariableWithName = function(variableId, yPos) {
        var name = $dataSystem.variables[variableId];
        var value = $gameVariables.value(variableId);
        this.drawText(name + ": " + value, 0, yPos, this.contents.width, 'left');
    };

    Window_Info.prototype.drawLine = function(yPos) {
        this.contents.fillRect(0, yPos, this.contents.width, 2, this.normalColor());
    };
})();
