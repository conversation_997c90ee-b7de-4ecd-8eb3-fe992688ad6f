{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "\n", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 7, "width": 21, "data": [4882, 4882, 4886, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4499, 4498, 4498, 4498, 4888, 4888, 4892, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4505, 4504, 4504, 4504, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4931, 4930, 4930, 4930, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4937, 4936, 4936, 4936, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1626, 1626, 1626, 1626, 1626, 1583, 2763, 2729, 2720, 2720, 2720, 2720, 2726, 2765, 1583, 1626, 1626, 1626, 1626, 1626, 1626, 1626, 1626, 1626, 1626, 1626, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1626, 1626, 1626, 1626, 1626, 1626, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4643, 4642, 4642, 4642, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4641, 4640, 4640, 4640, 1583, 1583, 1583, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4649, 4648, 4648, 4648, 4930, 4930, 4934, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4931, 4930, 4930, 4930, 4928, 4928, 4932, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 4937, 4936, 4595, 4594, 4928, 4928, 4932, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 1583, 1583, 4601, 4600, 4936, 4936, 4940, 1583, 1583, 1583, 1591, 2736, 2720, 2720, 2720, 2720, 2744, 1591, 1583, 1583, 1583, 1583, 1583, 5083, 5082, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3892, 3892, 3892, 3892, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3892, 3892, 3892, 3892, 3892, 3900, 3900, 3900, 3900, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3900, 3900, 3900, 3900, 3900, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 676, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 0, 0, 0, 0, 0, 557, 0, 0, 0, 0, 559, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 559, 0, 0, 0, 0, 565, 0, 612, 613, 0, 567, 669, 0, 0, 0, 0, 0, 0, 0, 0, 668, 567, 0, 0, 771, 0, 0, 0, 620, 621, 0, 575, 728, 729, 729, 729, 729, 729, 729, 729, 729, 730, 575, 0, 0, 779, 0, 0, 0, 0, 0, 0, 0, 736, 737, 737, 737, 737, 737, 737, 737, 737, 738, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 560, 748, 748, 748, 748, 748, 748, 748, 748, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 559, 744, 745, 745, 745, 745, 745, 745, 745, 745, 746, 559, 0, 0, 0, 0, 0, 514, 515, 0, 0, 567, 752, 753, 753, 753, 753, 753, 753, 753, 753, 754, 567, 0, 0, 0, 0, 0, 522, 523, 524, 0, 575, 669, 0, 0, 0, 0, 0, 0, 0, 0, 668, 575, 0, 0, 0, 0, 0, 530, 531, 532, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 676, 0, 0, 0, 0, 0, 0, 538, 539, 540, 0, 0, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 0, 0, 0, 0, 0, 0, 546, 547, 548, 0, 0, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 0, 0, 0, 0, 0, 0, 0, 555, 556, 0, 0, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 0, 0, 0, 0, 0, 0, 0, 800, 0, 0, 559, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 559, 0, 0, 0, 0, 0, 0, 808, 0, 0, 567, 663, 0, 0, 0, 0, 0, 0, 0, 0, 662, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 12, 12, 12, 12, 12, 12, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 15, 15, 15, 15, 15, 15, 15, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 3, 3, 3, 3, 3, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 223, "indent": 0, "parameters": [[68, -34, -34, 0], 60, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map39_ev1_p1_01\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6-7"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map39_ev1_p1_101\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map39_ev1_p1_102\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8-9"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13-19"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["map_id = 39"]}, {"code": 655, "indent": 0, "parameters": ["key = [map_id, 6, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key] = true"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_21"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22-23"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_24-25"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_26-31"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 241, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 243, "indent": 2, "parameters": []}, {"code": 242, "indent": 2, "parameters": [1]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 245, "indent": 2, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_32-33"]}, {"code": 242, "indent": 2, "parameters": [3]}, {"code": 230, "indent": 2, "parameters": [180]}, {"code": 201, "indent": 2, "parameters": [0, 93, 2, 16, 2, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["ページ２へ"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["ページ２へ"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 1, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 207, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["map_id = 39"]}, {"code": 655, "indent": 0, "parameters": ["key = [map_id, 6, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$game_self_switches[key, true)"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 11, 6, 0]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 223, "indent": 0, "parameters": [[68, -34, -34, 0], 60, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7-10"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12-14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_141"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "indent": null, "parameters": [6]}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 29, "indent": null, "parameters": [4]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "indent": null, "parameters": [6]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "indent": null, "parameters": [4]}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 29, "indent": null, "parameters": [6]}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Fall", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 15, "indent": null, "parameters": [40]}, {"code": 44, "indent": null, "parameters": [{"name": "Water1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "indent": null, "parameters": [6]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Fall", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [40]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Water1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 241, "indent": 0, "parameters": [{"name": "neon", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map39_ev1_p1_2301\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map39_ev1_p1_2302\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_16-19"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20-21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22"]}, {"code": 250, "indent": 0, "parameters": [{"name": "piss_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map39_ev1_p2_301\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_23"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map39_ev1_p2_401\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_24-27"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_28-29"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 201, "indent": 0, "parameters": [0, 17, 8, 16, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, null, null, {"id": 4, "name": "売店のおっさん", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 2}, {"id": 5, "name": "司祭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 6}, {"id": 6, "name": "よっぱらい", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_akunin2", "direction": 4, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 20, "y": 6}]}