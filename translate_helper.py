#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để hỗ trợ dịch game sang tiếng Việt
"""

import csv
import re
import os

def add_vietnamese_column_to_csv(file_path):
    """Thêm cột tiếng Việt vào file CSV"""
    if not os.path.exists(file_path):
        print(f"File không tồn tại: {file_path}")
        return
    
    # Đọc file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    updated_lines = []
    
    for line in lines:
        if line.strip() == '':
            updated_lines.append(line)
            continue
            
        # Đếm số dấu phẩy để xác định số cột
        comma_count = line.count(',')
        
        # Nếu dòng kết thúc bằng dấu phẩy, thêm một cột trống
        if line.endswith(','):
            updated_lines.append(line + ',')
        else:
            # Thêm dấu phẩy và cột trống cho tiếng Việt
            updated_lines.append(line + ',')
    
    # Ghi lại file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(updated_lines))
    
    print(f"Đã thêm cột tiếng Việt vào {file_path}")

def translate_basic_terms():
    """Dictionary các từ cơ bản để dịch"""
    return {
        # UI terms
        "Attack": "Tấn công",
        "Defense": "Phòng thủ", 
        "Magic": "Phép thuật",
        "Item": "Vật phẩm",
        "Skill": "Kỹ năng",
        "Status": "Trạng thái",
        "Level": "Cấp độ",
        "HP": "HP",
        "MP": "MP",
        "Exp": "Kinh nghiệm",
        "Gold": "Vàng",
        
        # Common actions
        "Yes": "Có",
        "No": "Không",
        "OK": "Đồng ý",
        "Cancel": "Hủy",
        "Back": "Quay lại",
        "Next": "Tiếp theo",
        "Save": "Lưu",
        "Load": "Tải",
        "Exit": "Thoát",
        
        # Game terms
        "Name": "Tên",
        "Age": "Tuổi",
        "Job": "Nghề nghiệp",
        "Teacher": "Giáo viên",
        "Student": "Học sinh",
        "Knight": "Hiệp sĩ",
        "Traveler": "Du khách",
        "Writer": "Nhà văn",
        
        # Status
        "None": "Không có",
        "Boyfriend": "Bạn trai",
        "In relationship": "Đang hẹn hò",
        "Widow": "Góa phụ",
        "Teens": "Thiếu niên",
        "Twenties": "Đôi mươi",
        "Forties": "Bốn mươi",
        
        # Items
        "Healing Herb": "Cỏ chữa lành",
        "Poisonous Herb": "Cỏ độc",
        "Antidote Herb": "Cỏ giải độc",
        "Eye Wash": "Thuốc nhỏ mắt",
        "Drops": "Kẹo ngậm",
        "Brain Cleaner": "Thuốc tỉnh táo",
        
        # Skills
        "Continuous Attack": "Tấn công liên tục",
        "Double Attack": "Tấn công đôi",
        "Triple Attack": "Tấn công ba lần",
        "Escape": "Trốn thoát",
        "Watching": "Quan sát",
        "Fire Breath": "Hơi thở lửa",
        "Ice Breath": "Hơi thở băng",
    }

def process_database_file(file_path):
    """Xử lý file database để thêm bản dịch tiếng Việt"""
    if not os.path.exists(file_path):
        print(f"File không tồn tại: {file_path}")
        return
    
    translations = translate_basic_terms()
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    updated_lines = []
    
    for i, line in enumerate(lines):
        if line.strip() == '':
            updated_lines.append(line)
            continue
            
        # Xử lý header
        if i == 0:
            if not line.endswith(',VIE_NAME,DESCRIPTION'):
                line = line.rstrip(',') + ',VIE_NAME,DESCRIPTION'
            updated_lines.append(line)
            continue
        
        # Xử lý các dòng dữ liệu
        parts = line.split(',')
        if len(parts) >= 4:  # Có ít nhất id, free, JPN_NAME, DESCRIPTION
            # Tìm tên tiếng Anh để dịch
            eng_name = ""
            if len(parts) >= 5:
                eng_name = parts[4].strip('"')
            
            # Dịch tên
            vie_name = translations.get(eng_name, eng_name) if eng_name else ""
            
            # Thêm cột tiếng Việt
            if not line.endswith(',,'):
                line = line.rstrip(',') + f',"{vie_name}",'
            
        updated_lines.append(line)
    
    # Ghi lại file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(updated_lines))
    
    print(f"Đã xử lý file database: {file_path}")

if __name__ == "__main__":
    print("Script hỗ trợ dịch game sang tiếng Việt")
    print("1. Xử lý file database")
    print("2. Thêm cột tiếng Việt vào CSV")
    
    # Xử lý các file database
    db_files = [
        "www/csv/DB_Item.csv",
        "www/csv/DB_Skill.csv", 
        "www/csv/DB_Weapon.csv",
        "www/csv/DB_Armor.csv",
        "www/csv/DB_Enemy.csv",
        "www/csv/DB_Actor.csv",
        "www/csv/DB_Class.csv",
        "www/csv/DB_State.csv"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            process_database_file(db_file)
