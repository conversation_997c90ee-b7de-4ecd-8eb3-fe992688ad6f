// Plugin Localization System
(function() {
    'use strict';

    // L<PERSON><PERSON> trữ các bản dịch
    var translations = {
        'en': {}, // Tiếng <PERSON> (default)
        'vi': {}  // Tiếng Việt
    };

    // Hàm lấy bản dịch
    function getTranslation(key, language = 'vi') {
        if (translations[language] && translations[language][key]) {
            return translations[language][key];
        }
        return key; // Trả về key nếu không có bản dịch
    }

    // Hàm khởi tạo bản dịch
    function loadTranslations() {
        // Load các bản dịch từ tệp JSON
        // Sẽ được triển khai sau
    }

    // Thêm các phương thức vào global object
    window.$t = getTranslation;
    window.loadTranslations = loadTranslations;
})();
