let ArrayHelper={CreateArray:function(){let e=[],r=(e,t)=>{for(let l=0;l<arguments[t];l++)t+1<arguments.length?(e.push([]),r(e[l],t+1)):e.push(0)};return r(e,0),e}},FunctionHelper={Empty:function(){}},AlgoHelper={Pathfind2d:function(e=[[]],r,t,l,n){let o=[[r,t]];if(r===l&&t===n)return o;let f=e.length,i=e[0].length,u=ArrayHelper.CreateArray(f,i);if(!(r>=0&&r<i&&t>=0&&t<f&&l>=0&&l<i&&n>=0&&n<f))return console.warn("Warning: start or end point are out of bounce!"),o;let p,a=[[1,0],[0,1],[-1,0],[0,-1]],c=[[r,t,o]],h=!1;for(;c.length>0&&!h;){p=[];for(let r of c){let t=r[0],c=r[1],s=r[2];u[c][t]=1;for(let r of a){let a=[t+r[0],c+r[1]];if(a[0]>=0&&a[0]<i&&a[1]>=0&&a[1]<f&&0===u[a[1]][a[0]]&&e[a[1]][a[0]]){let e=s.slice(0);if(e.push([a[0],a[1]]),a.push(e),p.push(a),a[0]===l&&a[1]===n){h=!0,o=e;break}}}}c=p}return o},PathfindReversed2d:function(e=[[]],r,t,l,n){let o=[[r,t]];if(r===l&&t===n)return o;let f=e.length,i=e[0].length,u=ArrayHelper.CreateArray(f,i);if(!(r>=0&&r<i&&t>=0&&t<f&&l>=0&&l<i&&n>=0&&n<f))return console.warn("Warning: start or end point are out of bounce!"),o;let p,a=[[1,0],[0,1],[-1,0],[0,-1]],c=[[r,t]],h=!1,s=1;for(;c.length>0&&!h;){p=[];for(let r of c){let t=r[0],o=r[1];0===u[o][t]&&(u[o][t]=s);for(let r of a){let a=[t+r[0],o+r[1]];if(a[0]>=0&&a[0]<i&&a[1]>=0&&a[1]<f&&0===u[a[1]][a[0]]&&e[a[1]][a[0]]&&(p.push(a),a[0]===l&&a[1]===n)){h=!0,u[n][l]=s+1;break}}}c=p,s++}if(!h)return[];let H=l,g=n,y=s,A=[[l,n]];for(let e=0;e<s-1;e++)for(let e of a){let r=[H+e[0],g+e[1]];if(r[0]>=0&&r[0]<i&&r[1]>=0&&r[1]<f){let e=u[r[1]][r[0]];if(e<y){y=e,H=r[0],g=r[1],A.push(r);break}}}return o=A}},MiscHelper={},Helper={ArrayHelper:ArrayHelper,FunctionHelper:FunctionHelper,FunctionHelper:FunctionHelper,MiscHelper:MiscHelper};