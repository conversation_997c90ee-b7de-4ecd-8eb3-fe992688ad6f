[null, {"id": 1, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 17, "maxTurns": 1, "message1": "は倒れた！", "message2": "を倒した！", "message3": "", "message4": "は立ち上がった！", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ノックダウン", "note": "ステート１番はＨＰが０になったときに自動的に付加されます。\n<E_name:Down>\n", "priority": 100, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 23, "dataId": 9, "value": 0}]}, {"id": 2, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 18, "maxTurns": 1, "message1": "は毒に冒された！", "message2": "は毒に冒された！", "message3": "", "message4": "の毒が消えた！", "minTurns": 1, "motion": 0, "overlay": 0, "name": "毒", "note": "<E_name:<PERSON><PERSON>>", "priority": 65, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 7, "value": -0.1}]}, {"id": 3, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 19, "maxTurns": 5, "message1": "は暗闇に閉ざされた！", "message2": "を暗闇に閉ざした！", "message3": "", "message4": "の暗闇が消えた！", "minTurns": 3, "motion": 0, "overlay": 0, "name": "暗闇", "note": "<E_name:<PERSON>>", "priority": 70, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 0, "value": -0.6}]}, {"id": 4, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 20, "maxTurns": 5, "message1": "は魔法を封じ込まれた！", "message2": "の魔法を封じ込めた！", "message3": "", "message4": "の魔法封じが解けた！", "minTurns": 3, "motion": 0, "overlay": 0, "name": "沈黙", "note": "<E_name:Silent>", "priority": 75, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 42, "dataId": 2, "value": 0}]}, {"id": 5, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 21, "maxTurns": 4, "message1": "は混乱してしまった！", "message2": "を混乱させた！", "message3": "", "message4": "は我に返った！", "minTurns": 2, "motion": 0, "overlay": 0, "name": "混乱", "note": "<TYPE:MIND>\n<E_name:Confuse>", "priority": 80, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 2, "stepsToRemove": 100, "traits": []}, {"id": 6, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 22, "maxTurns": 5, "message1": "は眠ってしまった！", "message2": "を眠らせた！", "message3": "は眠っている……", "message4": "は目を覚ました！", "minTurns": 3, "motion": 0, "overlay": 0, "name": "睡眠", "note": "<TYPE:MIND>\n<E_name:Sleep>", "priority": 85, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}]}, {"id": 7, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 23, "maxTurns": 6, "message1": "はしびれて動けなくなった！", "message2": "を麻痺させた！", "message3": "は体がしびれて動けない！", "message4": "の麻痺が解けた！", "minTurns": 4, "motion": 0, "overlay": 0, "name": "麻痺", "note": "<E_name:<PERSON><PERSON><PERSON>>", "priority": 90, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}]}, {"id": 8, "autoRemovalTiming": 2, "chanceByDamage": 100, "iconIndex": 24, "maxTurns": 2, "message1": "はバランスを崩した！", "message2": "はバランスを崩した！", "message3": "はバランスを崩している……", "message4": "は体勢を立て直した！", "minTurns": 1, "motion": 0, "overlay": 0, "name": "よろめき", "note": "", "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": []}, {"id": 9, "autoRemovalTiming": 2, "chanceByDamage": 100, "iconIndex": 7, "maxTurns": 2, "message1": "はバランスを崩した！", "message2": "はバランスを崩した！", "message3": "はバランスを崩している……", "message4": "は体勢を立て直した！", "minTurns": 1, "motion": 0, "overlay": 0, "name": "怯み", "note": "", "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.8}, {"code": 21, "dataId": 6, "value": 0.8}]}, {"id": 10, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "拘束中", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 11, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "セックス中", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 12, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 21, "maxTurns": 6, "message1": "はしびれて動けなくなった！", "message2": "を麻痺させた！", "message3": "は体がしびれて動けない！", "message4": "の麻痺が解けた！", "minTurns": 4, "motion": 0, "overlay": 0, "name": "トランス", "note": "", "priority": 90, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}]}, {"id": 13, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 14, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 13, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "盾防御", "note": "盾装備中のみ防御コマンドを実行可能", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 3, "value": 1.2}]}, {"id": 15, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "感度倍増", "note": "<戦闘不能時残留>", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 16, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 114, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "疲労困憊", "note": "<E_name:Tired>", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.1}, {"code": 21, "dataId": 6, "value": 0.1}]}, {"id": 17, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 13, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "プロテクション", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 18, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 11, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "カウンター", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 19, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 15, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "リフレクション", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 20, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 21, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 585, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ターゲット", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 22, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 113, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "祝福", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.2}, {"code": 21, "dataId": 3, "value": 1.2}, {"code": 21, "dataId": 4, "value": 1.2}, {"code": 21, "dataId": 5, "value": 1.2}, {"code": 21, "dataId": 6, "value": 1.2}, {"code": 21, "dataId": 7, "value": 1.2}]}, {"id": 23, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 114, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "呪い", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.8}, {"code": 21, "dataId": 3, "value": 0.8}, {"code": 21, "dataId": 4, "value": 0.8}, {"code": 21, "dataId": 5, "value": 0.8}, {"code": 21, "dataId": 6, "value": 0.8}, {"code": 21, "dataId": 7, "value": 0.8}]}, {"id": 24, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 121, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "透明", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 25, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "興奮", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 26, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 584, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "エイム", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 27, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 28, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 593, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "司祭の雌臭\\I[122]", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 29, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 592, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "主人公の雄臭\\I[122]", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 30, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 594, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "他の女の雌臭", "note": "このステート状態のとき、司祭と合流したら司祭は怒る\n<E_name:Smell of another girl>", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 31, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 594, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "他の男の雄臭", "note": "このステート状態のとき、主人公が違和感を覚える\n<E_name:Smell of another man>", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 32, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 33, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 34, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 35, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 36, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "まんこが敏感", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 37, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "アナルが敏感", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 38, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "喉が敏感", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 39, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 40, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 41, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 577, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "水濡れ", "note": "雷ダメージ1.5倍", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 42, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 577, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "油濡れ", "note": "炎ダメージ1.5倍", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 43, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 44, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 45, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 5, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "二日酔い", "note": "信仰以外の能力が半減", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.5}, {"code": 21, "dataId": 3, "value": 0.5}, {"code": 21, "dataId": 4, "value": 0.5}, {"code": 21, "dataId": 5, "value": 0.5}, {"code": 21, "dataId": 6, "value": 0.5}]}, {"id": 46, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 47, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 48, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 49, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "超射精", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 50, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "魔女の呪い", "note": "司祭で勃起しなくなる呪い\n<E_name:Curse of the Witch>", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 51, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 114, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "日中の吸血鬼", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.8}, {"code": 21, "dataId": 3, "value": 0.8}, {"code": 21, "dataId": 4, "value": 0.8}, {"code": 21, "dataId": 5, "value": 0.8}, {"code": 21, "dataId": 6, "value": 0.8}, {"code": 21, "dataId": 7, "value": 0.8}]}, {"id": 52, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 113, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "夜間の吸血鬼", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 53, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 54, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 55, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 56, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 57, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 58, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 59, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 60, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 61, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 62, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 63, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 64, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 65, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 66, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 67, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 68, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 69, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 70, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-顔", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 71, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-胸", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 72, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-尻", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 73, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-ボディ", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 74, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-足", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 75, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 122, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "ぶっかけ-腋", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 76, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 77, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 78, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 79, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 80, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 81, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 82, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 83, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 84, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 85, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 86, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 87, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 88, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 89, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}, {"id": 90, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "overlay": 0, "name": "", "note": "", "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": []}]