{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "主人公", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ドライアド"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-protag-dryad\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["カットイン"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cutin`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ラミア１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-protag-lamia1\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["舌の表示"]}, {"code": 355, "indent": 0, "parameters": ["stage = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tongue${stage}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ブレス"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-breath`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ラミア２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-protag-lamia2\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["Xrayの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-xray`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["よだれの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-drool`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 1}, {"id": 2, "name": "アビサル・ウッズ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アケパロイ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-<PERSON><PERSON><PERSON>\""]}, {"code": 108, "indent": 0, "parameters": ["3ボディ"]}, {"code": 408, "indent": 0, "parameters": ["10ヘッド"]}, {"code": 408, "indent": 0, "parameters": ["11ヘアー"]}, {"code": 408, "indent": 0, "parameters": ["12フェイス"]}, {"code": 408, "indent": 0, "parameters": ["15ちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["16ア<PERSON><PERSON> skin-pose"]}, {"code": 108, "indent": 0, "parameters": ["20精子"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guys`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ケンちゃん"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-centaurus\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボコの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-boko${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ケンタウロス"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-centaurus`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["メデューサ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-medusa\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base_lower${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth102`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base_upper${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ストーンの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-stone${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["if($gameVariables.value(28) >= 5){"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ph1`"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["if($gameVariables.value(28) >= 3){"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-ah1`"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-protag${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["リザードマン１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-lizardmanSex\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["リザードマンの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-lizardman${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["脚の表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-leg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["リザードマン２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-lizardmanSpawning\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["卵"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-egg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["卵背景"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-eggBG${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 108, "indent": 1, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 1, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinBase${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 1, "parameters": ["顔の表示"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cutinFace${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18);"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer19);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["レッドキャップ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-redcap\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["レッドキャップの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-redcap${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 1}, {"id": 3, "name": "王都周辺", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ゴブリンライダー１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-GoblinRider1\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["BGの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ゴブリンＢＧの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-goblinBG`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cumLower${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["X RAYの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["cum = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}-${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ザーメンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公の手"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["closing eye"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `closing_eye`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["frame"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [98]}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ゴブリンライダー２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-bad_end-GoblinRider2\""]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["BGの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num=$gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公の手"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hand${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["frame"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-frame`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}]}