{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "<PERSON><PERSON>s", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "王都\n暗闇\n公共の場\n拠点", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 30, "data": [6228, 6252, 6252, 6236, 6252, 6252, 6236, 6252, 6252, 6236, 6252, 6252, 6232, 6224, 6228, 6252, 6249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4786, 4786, 6256, 4786, 4786, 6256, 4786, 4786, 6256, 4786, 4786, 6240, 6228, 6262, 1608, 6256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4792, 4792, 6256, 4792, 4792, 6256, 4792, 4792, 6256, 4792, 4792, 6240, 6248, 4790, 1608, 6256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6240, 6248, 4796, 1591, 6256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6240, 6248, 1591, 1591, 6256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6230, 6269, 1591, 6265, 6269, 1591, 6265, 6269, 1591, 6265, 6269, 1591, 6264, 6262, 1591, 1591, 6268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4790, 1591, 4787, 4790, 1591, 4787, 4790, 1591, 4787, 4790, 1591, 4787, 4790, 1591, 1591, 4791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4796, 1591, 4793, 4796, 1591, 4793, 4796, 1591, 4793, 4796, 1591, 4793, 4796, 1591, 1591, 4797, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6230, 6269, 1591, 6259, 6269, 1591, 6259, 6269, 1591, 6259, 6269, 1591, 6258, 6260, 1591, 1591, 6266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4790, 1591, 6256, 4790, 1591, 6256, 4790, 1591, 6256, 4790, 1591, 6240, 6248, 1591, 1591, 6256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 4796, 1591, 6256, 4796, 1591, 6256, 4796, 1591, 6256, 4796, 1591, 6240, 6226, 6244, 6244, 6250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6240, 6224, 6224, 6224, 6248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6248, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6256, 1591, 1591, 6240, 6224, 6224, 6224, 6248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6226, 6244, 6244, 6227, 6244, 6244, 6227, 6244, 6244, 6227, 6244, 6244, 6225, 6224, 6224, 6224, 6248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6252, 6262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4786, 4790, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4792, 4796, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3198, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 554, 575, 0, 554, 575, 0, 554, 575, 0, 554, 575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 562, 0, 0, 562, 0, 0, 562, 0, 0, 562, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 554, 0, 0, 554, 0, 0, 554, 0, 0, 554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 562, 575, 0, 562, 575, 0, 562, 583, 0, 562, 575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 116, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公が監獄に捕まった"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,-100)"]}, {"code": 122, "indent": 0, "parameters": [266, 266, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [116, 116, 1]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [26]}, {"code": 201, "indent": 0, "parameters": [0, 8, 13, 3, 2, 0]}, {"code": 117, "indent": 0, "parameters": [27]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 3, "name": "扉１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 7}, {"id": 4, "name": "扉２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 7}, {"id": 5, "name": "扉３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 7}, {"id": 6, "name": "扉４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 7}, {"id": 7, "name": "扉５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 9}, {"id": 8, "name": "扉６", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 9}, {"id": 9, "name": "扉７", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 9}, {"id": 10, "name": "扉８", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 9}, {"id": 11, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 2}, {"id": 12, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 6}, {"id": 13, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 6}, {"id": 14, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 6}, {"id": 15, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 129, "indent": 2, "parameters": [1, 1, 0]}, {"code": 121, "indent": 2, "parameters": [55, 55, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 201, "indent": 1, "parameters": [0, 146, 3, 12, 0, 0]}, {"code": 117, "indent": 1, "parameters": [27]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 8}, {"id": 16, "name": "衛兵", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People4", "direction": 8, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999}, "directionFix": false, "image": {"tileId": 0, "characterName": "p4_soldier", "direction": 8, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People4", "direction": 8, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 10}, {"id": 17, "name": "移動禁止", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 8}, {"id": 18, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 12}, {"id": 19, "name": "囚人３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 12}, {"id": 20, "name": "囚人２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 5}, {"id": 21, "name": "主人公捕まった時の睡眠アイコン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["収監　主人公"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_spending_time\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 111, "indent": 2, "parameters": [1, 5, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 6, 0, 3, 0]}, {"code": 113, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [19, 19, 1]}, {"code": 122, "indent": 1, "parameters": [266, 266, 1, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 108, "indent": 1, "parameters": ["*******************************"]}, {"code": 408, "indent": 1, "parameters": ["１０日後に自動釈放"]}, {"code": 408, "indent": 1, "parameters": ["*******************************"]}, {"code": 111, "indent": 1, "parameters": [1, 266, 0, 10, 0]}, {"code": 119, "indent": 2, "parameters": ["釈放"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************"]}, {"code": 408, "indent": 1, "parameters": ["釈放されるかの判定"]}, {"code": 408, "indent": 1, "parameters": ["*******************************"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"lucky\""]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 2, "parameters": ["*******************************"]}, {"code": 408, "indent": 2, "parameters": ["成功"]}, {"code": 119, "indent": 2, "parameters": ["釈放"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*******************************"]}, {"code": 408, "indent": 2, "parameters": ["失敗"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_prison_no_release\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["*******************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭が来るかの判定"]}, {"code": 408, "indent": 1, "parameters": ["*******************************"]}, {"code": 111, "indent": 1, "parameters": [1, 261, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 3, "parameters": ["****************************"]}, {"code": 408, "indent": 3, "parameters": ["成功"]}, {"code": 111, "indent": 3, "parameters": [1, 267, 0, 0, 0]}, {"code": 108, "indent": 4, "parameters": ["****************************"]}, {"code": 408, "indent": 4, "parameters": ["まだ司祭が面会に来てない"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 205, "indent": 4, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 4, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 4, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_10-11"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 205, "indent": 4, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 4, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 4, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 122, "indent": 4, "parameters": [267, 267, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [57]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["****************************"]}, {"code": 408, "indent": 4, "parameters": ["既に司祭が面会に来た"]}, {"code": 108, "indent": 4, "parameters": ["****************************"]}, {"code": 408, "indent": 4, "parameters": ["狂気が５０以上"]}, {"code": 111, "indent": 4, "parameters": [1, 184, 0, 50, 1]}, {"code": 221, "indent": 5, "parameters": []}, {"code": 205, "indent": 5, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 5, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 5, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 222, "indent": 5, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_100"]}, {"code": 355, "indent": 5, "parameters": ["var_from_sheet(61, 144, 21, 1, 101, 0)"]}, {"code": 102, "indent": 5, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 5, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_102-104"]}, {"code": 213, "indent": 5, "parameters": [-1, 8, true]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 101, "indent": 5, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 5, "parameters": [""]}, {"code": 221, "indent": 5, "parameters": []}, {"code": 129, "indent": 5, "parameters": [2, 0, 0]}, {"code": 119, "indent": 5, "parameters": ["釈放"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["****************************"]}, {"code": 408, "indent": 4, "parameters": ["看守とスケベして釈放"]}, {"code": 111, "indent": 4, "parameters": [0, 32, 0]}, {"code": 111, "indent": 5, "parameters": [1, 82, 0, 100, 1]}, {"code": 111, "indent": 6, "parameters": [1, 82, 1, 83, 3]}, {"code": 111, "indent": 7, "parameters": [0, 35, 1]}, {"code": 221, "indent": 8, "parameters": []}, {"code": 117, "indent": 8, "parameters": [4]}, {"code": 203, "indent": 8, "parameters": [26, 0, 15, 1, 2]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 222, "indent": 8, "parameters": []}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 8, "parameters": [26, 8, true]}, {"code": 213, "indent": 8, "parameters": [16, 8, true]}, {"code": 213, "indent": 8, "parameters": [26, 8, true]}, {"code": 213, "indent": 8, "parameters": [16, 4, true]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 8, "parameters": [16, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$pixel_animation_blowjob", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$pixel_animation_blowjob", 0]}]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_200"]}, {"code": 213, "indent": 8, "parameters": [-1, 2, true]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_201"]}, {"code": 205, "indent": 8, "parameters": [16, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$pixel_animation_fingerossan", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$pixel_animation_fingerossan", 0]}]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_202"]}, {"code": 213, "indent": 8, "parameters": [-1, 8, true]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_203"]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_204-206"]}, {"code": 205, "indent": 8, "parameters": [16, {"list": [{"code": 41, "indent": null, "parameters": ["People4", 7]}, {"code": 34, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["People4", 7]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 8, "parameters": [16, 4, false]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 40, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 230, "indent": 8, "parameters": [30]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_207"]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 213, "indent": 8, "parameters": [24, 1, true]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_208"]}, {"code": 205, "indent": 8, "parameters": [24, {"list": [{"code": 29, "indent": null, "parameters": [6]}, {"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["Evil", 1]}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 29, "indent": null, "parameters": [6]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["Evil", 1]}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_209"]}, {"code": 108, "indent": 8, "parameters": ["*******************"]}, {"code": 408, "indent": 8, "parameters": ["フェラチオおっさん"]}, {"code": 111, "indent": 8, "parameters": [1, 82, 0, 200, 1]}, {"code": 111, "indent": 9, "parameters": [1, 82, 1, 83, 3]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 213, "indent": 10, "parameters": [26, 4, true]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 10, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 10, "parameters": [24, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$pixel_animation_door", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 10, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 41, "indent": null, "parameters": ["$pixel_animation_door", 0]}]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_210-212"]}, {"code": 117, "indent": 10, "parameters": [95]}, {"code": 250, "indent": 10, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 213, "indent": 10, "parameters": [26, 4, true]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_213-215"]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 10, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 205, "indent": 10, "parameters": [24, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["Evil", 1]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 10, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 41, "indent": null, "parameters": ["Evil", 1]}]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 205, "indent": 8, "parameters": [20, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_230-231"]}, {"code": 108, "indent": 8, "parameters": ["*******************"]}, {"code": 408, "indent": 8, "parameters": ["セックスおっさん"]}, {"code": 111, "indent": 8, "parameters": [1, 82, 0, 200, 1]}, {"code": 111, "indent": 9, "parameters": [1, 82, 1, 83, 3]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 213, "indent": 10, "parameters": [26, 1, true]}, {"code": 213, "indent": 10, "parameters": [26, 4, true]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$pixel_animation_door", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 10, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 41, "indent": null, "parameters": ["$pixel_animation_door", 0]}]}, {"code": 205, "indent": 10, "parameters": [4, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 10, "parameters": [20, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_232"]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_233"]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_234-235"]}, {"code": 117, "indent": 10, "parameters": [95]}, {"code": 250, "indent": 10, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 213, "indent": 10, "parameters": [26, 4, true]}, {"code": 101, "indent": 10, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 10, "parameters": ["_event_data_base_text_236"]}, {"code": 205, "indent": 10, "parameters": [20, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 205, "indent": 10, "parameters": [4, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 205, "indent": 10, "parameters": [26, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 10, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 10, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 108, "indent": 8, "parameters": ["*******************"]}, {"code": 408, "indent": 8, "parameters": ["主人公の場所へ移動"]}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 213, "indent": 8, "parameters": [-1, 1, true]}, {"code": 101, "indent": 8, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 8, "parameters": ["_event_data_base_text_250"]}, {"code": 355, "indent": 8, "parameters": ["var_from_sheet(61, 144, 21, 1, 251, 0)"]}, {"code": 102, "indent": 8, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 8, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 9, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 9, "parameters": ["_event_data_base_text_252"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 404, "indent": 8, "parameters": []}, {"code": 355, "indent": 8, "parameters": ["var_from_sheet(61, 144, 21, 1, 253, 0)"]}, {"code": 102, "indent": 8, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 8, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 9, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 9, "parameters": ["_event_data_base_text_254-255"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 404, "indent": 8, "parameters": []}, {"code": 355, "indent": 8, "parameters": ["var_from_sheet(61, 144, 21, 1, 256, 0)"]}, {"code": 102, "indent": 8, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 8, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 9, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 9, "parameters": ["_event_data_base_text_257-258"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 404, "indent": 8, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 82, 0, 200, 1]}, {"code": 111, "indent": 9, "parameters": [1, 82, 1, 83, 3]}, {"code": 355, "indent": 10, "parameters": ["var_from_sheet(61, 144, 21, 1, 259, 0)"]}, {"code": 102, "indent": 10, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 10, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 11, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 11, "parameters": ["_event_data_base_text_260"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 404, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 108, "indent": 8, "parameters": ["********************"]}, {"code": 408, "indent": 8, "parameters": ["シークレットNTRモードの場合はイベントを隠す"]}, {"code": 221, "indent": 8, "parameters": []}, {"code": 205, "indent": 8, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 8, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 8, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 222, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 122, "indent": 6, "parameters": [402, 402, 1, 0, 2]}, {"code": 122, "indent": 6, "parameters": [404, 404, 1, 0, 2]}, {"code": 122, "indent": 6, "parameters": [569, 569, 1, 0, 2]}, {"code": 122, "indent": 6, "parameters": [567, 567, 1, 0, 1]}, {"code": 122, "indent": 6, "parameters": [567, 567, 1, 0, 1]}, {"code": 101, "indent": 6, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 6, "parameters": ["_event_data_base_text_104"]}, {"code": 221, "indent": 6, "parameters": []}, {"code": 129, "indent": 6, "parameters": [2, 0, 0]}, {"code": 119, "indent": 6, "parameters": ["釈放"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 83, 1, 82, 1]}, {"code": 108, "indent": 5, "parameters": ["****************************"]}, {"code": 408, "indent": 5, "parameters": ["愛情が淫欲より高く１００銀貨持ってる"]}, {"code": 122, "indent": 5, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 111, "indent": 5, "parameters": [1, 11, 0, 100, 1]}, {"code": 221, "indent": 6, "parameters": []}, {"code": 205, "indent": 6, "parameters": [26, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 6, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 6, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 222, "indent": 6, "parameters": []}, {"code": 101, "indent": 6, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 6, "parameters": ["_event_data_base_text_300-302"]}, {"code": 355, "indent": 6, "parameters": ["show_map_log_window"]}, {"code": 125, "indent": 6, "parameters": [1, 0, 100]}, {"code": 101, "indent": 6, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 6, "parameters": [""]}, {"code": 221, "indent": 6, "parameters": []}, {"code": 129, "indent": 6, "parameters": [2, 0, 0]}, {"code": 119, "indent": 6, "parameters": ["釈放"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[80]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["釈放"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 99999, 0]}, {"code": 118, "indent": 1, "parameters": ["釈放"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 221, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 2, "parameters": [16, 0, 2, 8, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 203, "indent": 2, "parameters": [16, 0, 15, 10, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 3, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["収監　司祭"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_spending_time\")"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 144, 21, 2, 20, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 144, 21, 2, 30, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(64, 144, 21, 2, 40, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(65, 144, 21, 2, 50, 0)"]}, {"code": 111, "indent": 0, "parameters": [0, 32, 0]}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 1]}, {"code": 111, "indent": 2, "parameters": [1, 82, 0, 100, 1]}, {"code": 122, "indent": 3, "parameters": [74, 74, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [75, 75, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 82, 0, 150, 1]}, {"code": 122, "indent": 3, "parameters": [76, 76, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63] en(v[74]>=1)", "\\v[64] en(v[75]>=1)", "\\v[65] en(v[76]>=1)", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["翌日へ"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 118, "indent": 1, "parameters": ["翌日へ"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(144, 27, 1)"]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 355, "indent": 2, "parameters": ["console.log(\"セルフスイッチＡがオフ\")"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["console.log(\"セルフスイッチＡがオン\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["衛兵に釈放を乞う"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(144, 27, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] en(v[74]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["衛兵に釈放を乞う（エロ）"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(144, 27, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64] en(v[75]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["暇つぶしに目の前の牢屋の囚人を挑発する"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(144, 27, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "\\v[65] en(v[76]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["グローリーホール"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(144, 27, 5)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "\\v[80]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 0]}, {"code": 119, "indent": 1, "parameters": ["釈放"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["翌日へ"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["釈放"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 99999, 0]}, {"code": 118, "indent": 1, "parameters": ["釈放"]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 123, "indent": 1, "parameters": ["B", 1]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 123, "indent": 1, "parameters": ["D", 1]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 3, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 4}, {"id": 22, "name": "囚人１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 4}, {"id": 23, "name": "囚人６", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor4", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 12}, {"id": 24, "name": "囚人７", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage3", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 12}, {"id": 25, "name": "囚人８", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage3", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 12}, {"id": 26, "name": "面会にきた司祭？", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 8}, {"id": 27, "name": "司祭収監イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["翌日へ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [16, 0, 15, 10, 8]}, {"code": 203, "indent": 0, "parameters": [19, 0, 2, 12, 8]}, {"code": 203, "indent": 0, "parameters": [20, 0, 5, 5, 2]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 0]}, {"code": 111, "indent": 2, "parameters": [1, 6, 0, 3, 0]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 144, 2, 4, 2, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_NextDay\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 121, "indent": 0, "parameters": [19, 19, 1]}, {"code": 122, "indent": 0, "parameters": [266, 266, 1, 0, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["１０日後に自動釈放"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 111, "indent": 0, "parameters": [1, 266, 0, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["key = [144, 21, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["釈放されるかの判定"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"lucky\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 1, "parameters": ["*******************************"]}, {"code": 408, "indent": 1, "parameters": ["成功"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 1, "parameters": ["key = [144, 21, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************"]}, {"code": 408, "indent": 1, "parameters": ["失敗"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_prison_no_release\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公が来るかの判定"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 10, 1]}, {"code": 108, "indent": 1, "parameters": ["****************************"]}, {"code": 408, "indent": 1, "parameters": ["成功"]}, {"code": 203, "indent": 1, "parameters": [26, 0, 15, 1, 2]}, {"code": 205, "indent": 1, "parameters": [26, {"list": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [26, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [26, 8, true]}, {"code": 213, "indent": 1, "parameters": [16, 8, true]}, {"code": 205, "indent": 1, "parameters": [26, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 355, "indent": 1, "parameters": ["key = [144, 21, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["衛兵に釈放を乞う"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_201"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"koushou\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_211"]}, {"code": 355, "indent": 1, "parameters": ["key = [144, 21, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [16, 8, true]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_221"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_222"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["衛兵に釈放を乞う（エロ）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 213, "indent": 0, "parameters": [16, 2, true]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 213, "indent": 0, "parameters": [16, 1, true]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"koushou\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 1, "parameters": ["***********************************************"]}, {"code": 408, "indent": 1, "parameters": ["衛兵とのセックスシーン"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 37, "indent": null}, {"code": 1, "indent": null}, {"code": 36, "indent": null}, {"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 41, "parameters": ["$pixel_animation_door", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["$pixel_animation_door", 0], "indent": null}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [463, 463, 0, 0, 567]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 2, 1, 5]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 2, 1, 5]}, {"code": 122, "indent": 1, "parameters": [467, 467, 0, 0, 50]}, {"code": 122, "indent": 1, "parameters": [468, 468, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [67]}, {"code": 245, "indent": 1, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20-22"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_23-24"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_25-26"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_short1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [67]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 1, "parameters": ["釈放へ"]}, {"code": 355, "indent": 1, "parameters": ["key = [144, 21, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90-91"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["目の前の牢屋の囚人を挑発する"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 205, "indent": 0, "parameters": [25, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-4"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["檻越し　ケツ穴拡げ立ち絵"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["守衛が来る"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["守衛が上を向く"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-34"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["グローリーホール"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 122, "indent": 0, "parameters": [463, 463, 0, 0, 567]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 2, 1, 5]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 2, 1, 5]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 2, 1, 20]}, {"code": 122, "indent": 0, "parameters": [468, 468, 0, 2, 3, 10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"chikaku\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 203, "indent": 1, "parameters": [20, 0, 4, 4, 4]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-34"]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [1, 82, 1, 83, 3]}, {"code": 111, "indent": 3, "parameters": [1, 82, 0, 150, 1]}, {"code": 122, "indent": 4, "parameters": [74, 74, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 144, 27, 5, 50, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 144, 27, 5, 60, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(63, 144, 27, 5, 70, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62] en(v[184]>=50)", "\\v[63] en(v[74]>=1)"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["穴をふさぐ"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 250, "indent": 2, "parameters": [{"name": "Open4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_51"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62] en(v[184]>=50)"]}, {"code": 108, "indent": 2, "parameters": ["穴から覗く目を指で突き刺す"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_61"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Slash1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 170], 30, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_62"]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 315, "indent": 2, "parameters": [0, 2, 0, 0, 5, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63] en(v[74]>=1)"]}, {"code": 108, "indent": 2, "parameters": ["グローリーホール"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_80-81"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェラチオシーン"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 108, "indent": 0, "parameters": ["ちん嗅ぎシーン"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 15, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-107"]}, {"code": 108, "indent": 0, "parameters": ["ちん嗅ぎシーン"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 15, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-158"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_short1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 15, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-161"]}, {"code": 108, "indent": 0, "parameters": ["ゲップシーン"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 1, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, null]}