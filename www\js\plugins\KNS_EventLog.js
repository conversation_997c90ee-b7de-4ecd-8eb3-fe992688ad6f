/*:
 * @plugindesc ver.1.0.3 一部のイベントコマンド実行時マップログを表示します
 * <AUTHOR>
 *
 * @param AddLogSwitchId
 * @text ログ追加判定スイッチ
 * @type switch
 * @default 1
 *
 * @param LOG_KEYS
 * @text ログキー
 *
 * @param command311_a
 * @parent LOG_KEYS
 * @text HP増加(actor, hp)
 * @default HP manage
 *
 * @param command311_r
 * @parent LOG_KEYS
 * @text HP減少(actor, hp)
 * @default HP Remove manage
 *
 * @param command312_a
 * @parent LOG_KEYS
 * @text MP増加(actor, mp)
 * @default MP manage
 *
 * @param command312_r
 * @parent LOG_KEYS
 * @text MP減少(actor, mp)
 * @default MP Remove manage
 *
 * @param command326_a
 * @parent LOG_KEYS
 * @text TP増加(actor, tp)
 * @default TP manage
 *
 * @param command326_r
 * @parent LOG_KEYS
 * @text TP減少(actor, tp)
 * @default TP Remove manage
 *
 *
 * @param command126_a
 * @parent LOG_KEYS
 * @text アイテム増加(item, number)
 * @default Item Add manage
 *
 * @param command126_r
 * @parent LOG_KEYS
 * @text アイテム減少(item, number)
 * @default Item Remove manage
 *
 * @param command127_a
 * @parent LOG_KEYS
 * @text 武器増加(item, number)
 * @default Item Add manage
 *
 * @param command127_r
 * @parent LOG_KEYS
 * @text 武器減少(item, number)
 * @default Item Remove manage
 *
 * @param command128_a
 * @parent LOG_KEYS
 * @text 防具増加(item, number)
 * @default Item Add manage
 *
 * @param command128_r
 * @parent LOG_KEYS
 * @text 防具減少(item, number)
 * @default Item Remove manage
 *
 * @param command313_a
 * @parent LOG_KEYS
 * @text ステート付加(actor, state)
 * @default State Add manage
 *
 * @param command313_r
 * @parent LOG_KEYS
 * @text ステート解除(actor, state)
 * @default State Remove manage
 *
 * @param command314
 * @parent LOG_KEYS
 * @text 全回復()
 * @default All Recover manage
 *
 * @param command315_a
 * @parent LOG_KEYS
 * @text 経験値増加(actor, exp)
 * @default EXP Add manage
 *
 * @param command315_r
 * @parent LOG_KEYS
 * @text 経験値減少(actor, exp)
 * @default EXP Remove manage
 *
 * @param command316_a
 * @parent LOG_KEYS
 * @text レベル増加(actor, level)
 * @default Level Add manage
 *
 * @param command316_r
 * @parent LOG_KEYS
 * @text レベル減少(actor, level)
 * @default Level Remove manage
 *
 * @param command317_a
 * @parent LOG_KEYS
 * @text 能力値増加(actor, point, param)
 * @default Param Add manage
 *
 * @param command317_r
 * @parent LOG_KEYS
 * @text 能力値減少(actor, param)
 * @default Param Remove manage
 *
 * @param command318_a
 * @parent LOG_KEYS
 * @text スキル増加(actor, skill)
 * @default Skill Add manage
 *
 * @param command318_r
 * @parent LOG_KEYS
 * @text スキル減少(actor, skill)
 * @default Skill Remove manage
 *
 * @param command125_a
 * @parent LOG_KEYS
 * @text 所持金増加(gold)
 * @default Gold Add manage
 *
 * @param command125_r
 * @parent LOG_KEYS
 * @text 所持金減少(gold)
 * @default Gold Remove manage
 *
 *
 * @help
 * ■tp_heal
 * tp_heal(1, 10)   // アクター１のTPを10減らします（下限0）
 * tp_heal(0, -100) // パーティ全員のTPを100加算します（上限100）
 *
 * ■イベントコマンドのログ対応
 * 「ログ出力判定スイッチ」がONのときに以下のコマンドが実行されると
 * ログウィンドウに指定のメッセージが追加されます。
 *
 * ログが不要な処理についてはパラメータの該当のキーを
 * 空白にしてください。
 *
 * 項目名の丸括弧は文章を置換する要素で置換部分は
 * 括弧内の順番に沿って%1, %2と入力してください。
 *
 * 【logsheet.csv用のテンプレ】
 * HP manage,%1のHPが%2回復した！,,,,,
 * HP Remove manage,%1のHPが%2減った！,,,,,
 * MP manage,%1のMPが%2回復した！,,,,,
 * MP Remove manage,%1のMPが%2減った！,,,,,
 * TP manage,%1のTPが%2回復した！,,,,,
 * TP Remove manage,%1のTPが%2減った！,,,,,
 * Item Add manage,%1を%2個手に入れた！,,,,,
 * Item Remove manage,%1を%2個失った！,,,,,
 * State Add manage,%1は%2状態になった！,,,,,
 * State Remove manage,%1の%2状態が治った！,,,,,
 * All Recover manage,全回復した！,,,,,
 * EXP Add manage,%1は経験値を%2手に入れた！,,,,,
 * EXP Remove manage,%1は経験値を%2失った！,,,,,
 * Level Add manage,%1はレベルが%2上がった！,,,,,
 * Level Remove manage,%1はレベルが%2下がった！,,,,,
 * Param Add manage,%1の%3が%2上がった！,,,,,
 * Param Remove manage,%1の%3が%2下がった！,,,,,
 * Skill Add manage,%1は%2を覚えた！,,,,,
 * Skill Remove manage,%1は%2を忘れた！,,,,,
 * Gold Add manage,%1銀貨手に入れた！,,,,,
 * Gold Remove manage,%1銀貨手に入れた！,,,,,
 *
 *
 * ver.1.0.1(2023-06-15)
 * - TP増減表示が表示されない不具合を修正しました。
 * - ステート操作時、不要なログが表示される不具合を修正しました。
 * ver.1.0.2(2023-07-01)
 * - 所持金増減処理のログを追加
 * ver.1.0.3(2023-10-09)
 * - DreamX_RandomPrefixesSuffixesに対応し、エンチャントされた
 *   アイテム名がログに表示されるよう対応
 */

const KNS_EventLog = {
	name: "KNS_EventLog",
	param: null,
	getLogString: function(command){
		const key = this.param[command];
		if (key && $LogSheetCSV.Exist(key)){ return $LogSheetCSV.Get(key) || ""; }
		return "";
	},
	pushFormatLog: function(command, args){
		if ($gameSwitches.value(this.param.AddLogSwitchId) === false){ return; }
		let title = this.getLogString(command);
		if (title.length === 0){ return; }
		$LogWindow.Add( title.format(...Array.prototype.slice.call(arguments, 1)) );
	},
	pushFormatLogAR: function(command, add, args){
		this.pushFormatLog(command + (add ? "_a" : "_r"),
			...Array.prototype.slice.call(arguments, 2)
		);
	},
	getItemName: function(data){
		return data ? `\\i[${data.iconIndex}]${data.name}` : "";
	}
};

(function(){
//===================================================================
// new KNS_EventLog
//===================================================================
this.param = PluginManager.parameters(this.name);
this.param.AddLogSwitchId = Math.floor(this.param.AddLogSwitchId) || 0;

//=====================================================
// new KNS_TpHeal
//=====================================================
window.tp_heal = function(actorId, diff){
	if (!$gameParty){ return; }
	diff *= -1;
	const command = "command326_" + (diff > 0 ? "a" : "r");
	(actorId === 0 ? $gameParty.allMembers() : [$gameActors.actor(actorId)]).forEach(function(actor){
		KNS_EventLog.pushFormatLog(command, actor.name(), Math.abs(diff));
		actor.gainTp(diff);
	});
}

//===================================================================
// alias Game_Interpreter
//===================================================================
Game_Interpreter.prototype.knsGetEventLogByGauge = function(command){
	const value = this.operateValue(this._params[2], this._params[3], this._params[4]);
	this.iterateActorEx(this._params[0], this._params[1], function(actor){
		KNS_EventLog.pushFormatLogAR(command, value > 0, actor.name(), Math.abs(value));
	});
}

Game_Interpreter.prototype.knsGetEventLogByItem = function(command, item, value){
	const name = KNS_EventLog.getItemName(item);
	KNS_EventLog.pushFormatLogAR(command, value > 0, name, Math.abs(value));
}

Game_Interpreter.prototype.knsGetEventLogChangeActorByData = function(command, data, bool){
	const name = KNS_EventLog.getItemName(data);
	this.iterateActorEx(this._params[0], this._params[1], function(actor){
		KNS_EventLog.pushFormatLogAR(command, bool, actor.name(), name);
	});
}

Game_Interpreter.prototype.knsGetEventLogChangeActorByNumber = function(command, value, textArg){
	this.iterateActorEx(this._params[0], this._params[1], function(actor){
		KNS_EventLog.pushFormatLogAR(command,
			value > 0, actor.name(), Math.abs(value), textArg || ""
		);
	});
};

// HP/MP/TP
["command311", "command312", "command326"].forEach(function(key){
	const old = Game_Interpreter.prototype[key];
	Game_Interpreter.prototype[key] = function() {
		this.knsGetEventLogByGauge(key);
		return old.apply(this, arguments);
	};
});
// State
(function(){
	const _Game_Interpreter_command313 = Game_Interpreter.prototype.command313;
	Game_Interpreter.prototype.command313 = function() {
		let isAddMode = this._params[2] === 0;
		let stateId = this._params[3];

		const old = this._params;
		this._params = this._params.slice(0);
		this.iterateActorEx(old[0], old[1], function(actor) {
			let lastState = actor.isStateAffected(stateId);
			this._params[0] = 0;
			this._params[1] = actor.actorId();
			_Game_Interpreter_command313.apply(this, arguments);
			let currentState = actor.isStateAffected(stateId);

			if (isAddMode) {
				if (!(!lastState && currentState)){ return; }
			} else {
				if (!(lastState && !currentState)){ return; }
			}
			this.knsGetEventLogChangeActorByData("command313", $dataStates[stateId], isAddMode);
		}.bind(this));
		this._params = old;
		return true;
	};
})();
// Skill
[["command318", "$dataSkills"]].forEach(function(keys){
	const old = Game_Interpreter.prototype[keys[0]];
	Game_Interpreter.prototype[keys[0]] = function() {
		this.knsGetEventLogChangeActorByData(keys[0],
			window[keys[1]][this._params[3]], this._params[2] === 0
		);
		return old.apply(this, arguments);
	};
});
// EXP/Level
["command315", "command316"].forEach(function(key){
	const old = Game_Interpreter.prototype[key];
	Game_Interpreter.prototype[key] = function() {
		this.knsGetEventLogChangeActorByNumber(key, this.operateValue(this._params[2], this._params[3], this._params[4]));
		return old.apply(this, arguments);
	};
});

// Change Parameter
const _Game_Interpreter_command317 = Game_Interpreter.prototype.command317;
Game_Interpreter.prototype.command317 = function() {
	this.knsGetEventLogChangeActorByNumber("command317",
		this.operateValue(this._params[3], this._params[4], this._params[5]),
		TextManager.param(this._params[2]),
	);
	return _Game_Interpreter_command317.apply(this, arguments);
};

// Recover All
const _Game_Interpreter_command314 = Game_Interpreter.prototype.command314;
Game_Interpreter.prototype.command314 = function() {
	KNS_EventLog.pushFormatLog("command314");
	return _Game_Interpreter_command314.apply(this, arguments);
};

// Change Gold
const _Game_Interpreter_command125 = Game_Interpreter.prototype.command125;
Game_Interpreter.prototype.command125 = function() {
	let value = this.operateValue(this._params[0], this._params[1], this._params[2]);
	if (value !== 0){
		KNS_EventLog.pushFormatLogAR("command125", value > 0, Math.abs(value));
	}
	return _Game_Interpreter_command125.apply(this, arguments);
};


// item/weapon/armor
[["command126", "$dataItems"], ["command127", "$dataWeapons"], ["command128", "$dataArmors"]].forEach(function(keys){
	const old = Game_Interpreter.prototype[keys[0]];
	Game_Interpreter.prototype[keys[0]] = function() {
		const item = window[keys[1]][this._params[0]];
		if (!item || item.DXRPSDummyItem) { return; }
	
		const amount = this.operateValue(this._params[1], this._params[2], this._params[3]);
		// must have one of the meta tags and be a weapon/armor
		if (amount > 0 && DreamX.RandomPrefixSuffix.isConfiguredForPrefixSuffix(item)) {
			const items = [];
			for (let i = 0; i < amount; i++) {
				const newItem = DreamX.RandomPrefixSuffix.makeItem(item);
				const index = items.findIndex(function(info){
					return info[0] === newItem;
				});
				if (index === -1){
					items[items.length] = [newItem, 1];
				}else{
					items[index][1]++;
				}
				$gameParty.gainItem(newItem, 1);
			}
			items.forEach(function(info){
				this.knsGetEventLogByItem(keys[0], info[0], info[1]);
			}, this);
			return true;
		} else {
			this.knsGetEventLogByItem(keys[0], item, amount);
			return old.apply(this, arguments);
		}
	};
});

}).call(KNS_EventLog);