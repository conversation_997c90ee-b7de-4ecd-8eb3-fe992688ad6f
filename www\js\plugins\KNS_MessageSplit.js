/*:
 * @plugindesc ver.1.0.1 文章の自動改行を行います。
 * <AUTHOR>
 *
 * @help
 * Window_Base.prototype.drawTextEx関数にウィンドウ端で自動改行を
 * 行う機能を追加した以下の関数を追加しました。
 * Window_Base.prototype.knsDrawTextAutoline(text, x, y)
 *
 * また、メッセージ表示も同様にウィンドウ端で自動改行を行います。
 *
 * 【自動改行について】
 * 数字含む半角アルファベットのみで構成された単語の場合、
 * 単語ごとに改行を行います。
 *
 * ver.1.0.1(2023-05-14)
 * - 自動改行された際、行頭に来るスペースを飛ばすよう変更しました。
 */
const KNS_MessageSplit = {
	name: "KNS_MessageSplit",
	reNotSplit: /[\n\w,.!?、。！？'\"\[\]っぁぃぅぇぉゃゅょッァィゥェォャュョー～「」『』【】・…]/
};

(function(){
	Window_Base.prototype.knsDrawTextAutoline = function(text, x, y, maxWidth=this.contents.width){
		if (text) {
			var textState = { index: 0, x: x, y: y, left: x, maxWidth: maxWidth };
			textState.text = this.convertEscapeCharacters(text);
			textState.height = this.calcTextHeight(textState, false);
			this.resetFontSettings();
			while (textState.index < textState.text.length) {
				this.knsProcessCharacterAutoline(textState);
			}
			return this.calcTextHeight(textState, false) + textState.y;
		} else {
			return this.lineHeight() + y;
		}
	}

	Window_Base.prototype.knsProcessCharacterAutoline = function(textState) {
		switch (textState.text[textState.index]) {
		case '\n':
			this.processNewLine(textState);
			break;
		case '\f':
			this.processNewPage(textState);
			break;
		case '\x1b':
			this.processEscapeCharacter(this.obtainEscapeCode(textState), textState);
			break;
		default:
			this.knsProcessNormalCharacterAutoline(textState);
			break;
		}
	};

	Window_Base.prototype.knsProcessNormalCharacterAutoline = function(textState) {
		let c = textState.text[textState.index++];
		const w = this.textWidth(c);
		if (!KNS_MessageSplit.reNotSplit.test(c)){
			let x = textState.x + w + 48;
			for (let i = textState.index; i < textState.text.length; i++){
				const nc = textState.text[i];
				if (nc){
					x += this.textWidth(nc);
					if (KNS_MessageSplit.reNotSplit.test(nc)){
						if (textState.maxWidth < x){
							this.processNewLine(textState);
							textState.index--;
						}else{
							continue;
						}
					}else if (textState.maxWidth < x){
						this.processNewLine(textState);
						textState.index--;
					}
				}
				break;
			}
		}
		if (!(c === ' ' && textState.x === textState.left)){
			this.contents.drawText(c, textState.x, textState.y, w * 2, textState.height);
			textState.x += w;
		}
	};


	Window_Message.prototype.processNormalCharacter = function(textState) {
		this.knsProcessNormalCharacterAutoline(textState);
	};
})();
