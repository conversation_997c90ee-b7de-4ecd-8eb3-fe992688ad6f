{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "harmonic-peaceful", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 35, "note": "dark_night\n公共の場\n夜固定BGM\n王都\n交通量：小\n路地裏\n拠点\nモブ女生成", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 40, "data": [1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 4403, 4402, 4402, 4402, 4406, 4787, 4786, 4790, 4403, 4402, 4402, 4402, 4406, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 4401, 4400, 4400, 4400, 4404, 4785, 4784, 4788, 4401, 4400, 4400, 4400, 4404, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 4401, 4400, 4400, 4787, 4786, 4785, 4784, 4788, 4786, 4790, 4400, 4400, 4404, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3618, 3585, 3608, 4401, 4400, 4400, 4793, 4792, 4785, 4784, 4788, 4792, 4796, 4400, 4400, 4404, 3600, 3586, 3620, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3608, 4409, 4408, 4408, 1575, 1575, 4793, 4792, 4796, 1575, 1575, 4408, 4408, 4412, 3600, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3608, 4787, 4786, 4790, 1575, 1575, 1575, 1575, 1575, 1575, 1575, 4787, 4786, 4790, 3600, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3608, 4785, 4784, 4788, 1575, 1575, 1575, 1575, 1575, 1575, 1575, 4785, 4784, 4788, 3600, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3608, 4793, 4792, 4796, 1646, 1646, 1646, 1646, 1646, 1646, 1646, 4793, 4792, 4796, 3600, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3586, 3604, 3604, 3604, 3604, 3620, 1547, 1547, 1547, 3618, 3604, 3604, 3604, 3604, 3585, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1547, 1547, 1547, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1547, 1547, 1547, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1547, 1547, 1547, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1547, 1547, 1547, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 3624, 3612, 3612, 3612, 3612, 3612, 3592, 3608, 1547, 1547, 1547, 3600, 3588, 3612, 3612, 3612, 3612, 3612, 3622, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 1547, 1547, 1547, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3666, 3668, 3618, 3585, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3648, 3656, 3624, 3592, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3672, 3642, 3668, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3586, 3620, 3672, 3670, 3600, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3584, 3586, 3604, 3604, 3585, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3588, 3612, 3592, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3674, 3600, 3584, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3664, 3624, 3592, 3584, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3600, 3608, 3649, 3668, 3624, 3592, 3608, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3627, 3613, 3609, 3672, 3662, 3677, 3624, 3614, 3629, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3172, 3172, 3188, 0, 3906, 3892, 3908, 0, 3186, 3172, 3172, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3152, 3152, 3176, 0, 3888, 3872, 3896, 0, 3168, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3180, 3180, 3190, 0, 3888, 3872, 3896, 0, 3192, 3180, 3180, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3888, 3872, 3896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3888, 3872, 3896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3888, 3872, 3896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3900, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 36, 22, 22, 0, 0, 0, 0, 0, 787, 0, 787, 0, 0, 0, 0, 0, 22, 22, 35, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 78, 30, 30, 0, 0, 0, 0, 0, 795, 0, 795, 0, 0, 0, 0, 0, 30, 30, 77, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 78, 38, 38, 0, 0, 0, 0, 787, 0, 0, 0, 787, 0, 0, 0, 0, 38, 38, 77, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 646, 0, 0, 0, 0, 0, 795, 0, 0, 0, 795, 0, 0, 0, 0, 0, 647, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 654, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 655, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 663, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 662, 173, 174, 175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 787, 0, 0, 663, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 662, 181, 182, 183, 0, 0, 502, 0, 0, 0, 503, 0, 0, 795, 0, 0, 663, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 662, 189, 190, 191, 0, 0, 510, 0, 0, 0, 511, 0, 0, 0, 0, 0, 663, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 662, 197, 198, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 663, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 670, 205, 206, 207, 0, 0, 0, 643, 644, 645, 0, 0, 0, 0, 0, 0, 671, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 672, 673, 673, 673, 673, 673, 673, 651, 652, 653, 673, 673, 673, 673, 673, 673, 674, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 15, 680, 681, 681, 681, 681, 681, 681, 659, 660, 661, 681, 681, 681, 681, 681, 681, 682, 13, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 28, 6, 6, 6, 6, 6, 7, 0, 0, 0, 0, 0, 5, 6, 6, 6, 6, 6, 27, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 0, 0, 0, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 502, 0, 0, 0, 503, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 510, 0, 0, 0, 511, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 0, 0, 0, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 128, 129, 130, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 136, 137, 138, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 144, 145, 146, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 36, 23, 0, 0, 152, 153, 154, 21, 35, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 78, 31, 0, 0, 0, 0, 0, 29, 77, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 78, 39, 0, 0, 0, 0, 0, 37, 77, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 0, 0, 0, 0, 0, 0, 0, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2816, 0, 0, 0, 0, 2816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2816, 0, 0, 0, 0, 2816, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2816, 0, 0, 0, 0, 2816, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 250, "variableValid": true, "variableValue": 100}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバスＶ８（引継ぎ）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [8, 0, 14, 21, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 14, 8, 8]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["People1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People1", 0]}]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [8, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 250, "indent": 0, "parameters": [{"name": "vibe_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 213, "indent": 0, "parameters": [9, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_33-35"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 213, "indent": 0, "parameters": [8, 1, true]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-48"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-67"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 355, "indent": 0, "parameters": ["key = [213, 1, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 201, "indent": 0, "parameters": [0, 213, 13, 13, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Gate1", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_door_close\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 4}, {"id": 3, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 201, "indent": 0, "parameters": [0, 8, 26, 14, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 24}, {"id": 4, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 24}, {"id": 5, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 24}, {"id": 6, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 24}, {"id": 7, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 24}, {"id": 8, "name": "キャラ１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 2}, {"id": 9, "name": "キャラ２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 3}, {"id": 10, "name": "キャラ３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 4}, {"id": 11, "name": "キャラ４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 5}, {"id": 12, "name": "キャラ５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 6}, {"id": 13, "name": "歓楽街のサキュバスイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 11"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(250) === 60 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバスイベントV6 (好感度２　友好度６０の時のイベント）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["内容"]}, {"code": 408, "indent": 0, "parameters": ["サキュバスが修道院の前で遊ぶボーイたちを見つめていて……？"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["配置処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 15, 21, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 13, 15, 6]}, {"code": 203, "indent": 0, "parameters": [10, 0, 15, 15, 4]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["People1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People1", 0]}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["People7", 4]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People7", 4]}]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント内容開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 204, "indent": 0, "parameters": [8, 5, 4]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 204, "indent": 0, "parameters": [2, 5, 4]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 13, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 244, 13, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["「まさか～」"]}, {"code": 213, "indent": 1, "parameters": [8, 1, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30"]}, {"code": 205, "indent": 1, "parameters": [8, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-32"]}, {"code": 119, "indent": 1, "parameters": ["セクション２"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["無視する"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 203, "indent": 1, "parameters": [8, 0, 0, 19, 0]}, {"code": 203, "indent": 1, "parameters": [9, 0, 0, 19, 0]}, {"code": 203, "indent": 1, "parameters": [10, 0, 0, 19, 0]}, {"code": 205, "indent": 1, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 1, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 1, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セクション２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["セクション２"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 13, 1, 40, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 244, 13, 1, 50, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["冗談だよ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["サキュバスなら～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 204, "indent": 0, "parameters": [8, 5, 4]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-61"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 203, "indent": 0, "parameters": [9, 0, 0, 19, 0]}, {"code": 203, "indent": 0, "parameters": [10, 0, 0, 19, 0]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 204, "indent": 0, "parameters": [2, 5, 4]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-73"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["酒場へ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [14, 64, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 201, "indent": 0, "parameters": [0, 14, 18, 14, 4, 0]}, {"code": 119, "indent": 0, "parameters": ["末"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 11"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(250) === 80 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバスイベントV7 (好感度２　友好度８０の時のイベント）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["内容"]}, {"code": 408, "indent": 0, "parameters": ["サキュバスが修道院の前でボーイと喋っている……"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["配置処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 201, "indent": 0, "parameters": [0, 244, 14, 22, 8, 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 13, 16, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 13, 15, 2]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["People1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People1", 0]}]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント内容開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 204, "indent": 0, "parameters": [8, 5, 4]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 204, "indent": 0, "parameters": [2, 5, 4]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 203, "indent": 0, "parameters": [9, 0, 0, 19, 0]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [8, 1, true]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 13, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 244, 13, 2, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["「オマエまさか～」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["「何をしていたんだ？」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セクション２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-33"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [8, 8, true]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["数値処理"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 250]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["無視する"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [8, 0, 0, 19, 0]}, {"code": 203, "indent": 0, "parameters": [9, 0, 0, 19, 0]}, {"code": 203, "indent": 0, "parameters": [10, 0, 0, 19, 0]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_release_suc_house\")"]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 11"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(250) === 120 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバスイベントV9 (好感度３　友好度１２０の時のイベント）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["内容"]}, {"code": 408, "indent": 0, "parameters": ["サキュバスがマザーに詰められている……"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["配置処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 243, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 201, "indent": 0, "parameters": [0, 244, 14, 22, 8, 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 14, 15, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 15, 13, 2]}, {"code": 203, "indent": 0, "parameters": [10, 0, 14, 13, 2]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Monster2", 3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["People1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People1", 0]}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["People2", 1]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People2", 1]}]}, {"code": 201, "indent": 0, "parameters": [0, 244, 14, 21, 8, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 241, "indent": 0, "parameters": [{"name": "DBM_Underwater_Temple", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント内容開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 204, "indent": 0, "parameters": [8, 5, 4]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 250, "indent": 0, "parameters": [{"name": "vibe_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-8"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9-12"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["サキュバスたちの会話終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [9, 0, 0, 19, 0]}, {"code": 203, "indent": 0, "parameters": [10, 0, 0, 19, 0]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [8, 1, true]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-27"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 250]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 40]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [8, 0, 0, 19, 0]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 244, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 515, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 21}, {"id": 14, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 15}, {"id": 15, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 15}, {"id": 16, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 7}, {"id": 17, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 7}, {"id": 18, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 3}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 159, "characterName": "", "direction": 6, "pattern": 0, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["バグでオフになってなかったセルフスイッチをオフにする"]}, {"code": 408, "indent": 0, "parameters": ["(244,13)"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["key = [244, 13, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 22}, {"id": 20, "name": "マザー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People2", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(250) === 200 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス１１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 20, 1, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 244, 20, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 108, "indent": 1, "parameters": ["交渉判定へ"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["交渉判定"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"koushou\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 108, "indent": 0, "parameters": ["判定結果"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 108, "indent": 1, "parameters": ["判定成功"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["判定失敗"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 250]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 213, 14, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 250, "variableValid": true, "variableValue": 210}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 5}, {"id": 21, "name": "サキュバス（売春婦リタイア後）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 250, "variableValid": false, "variableValue": 999}, "directionFix": false, "image": {"tileId": 0, "characterName": "succubus", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(250) === 999 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　アフターストーリー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 21, 1, 10, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 895, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["※サキュバスが辞めるまでにヤリゾーにサキュバスの話をしていた"]}, {"code": 111, "indent": 1, "parameters": [0, 503, 0]}, {"code": 119, "indent": 2, "parameters": ["ヤリゾーに話してない"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-33"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["※サキュバスが辞めるまでにヤリゾーにサキュバスの話をしていない"]}, {"code": 118, "indent": 1, "parameters": ["ヤリゾーに話してない"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_22"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーシーン"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 15, 16, 8]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 15, 13, 2]}, {"code": 201, "indent": 0, "parameters": [0, 244, 15, 13, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-dungeon3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-44"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["次のシーンへ　（売春宿の回想）"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["key = [242, 106, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 201, "indent": 0, "parameters": [0, 242, 65, 8, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 119, "indent": 0, "parameters": ["末"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 250, "variableValid": false, "variableValue": 999}, "directionFix": false, "image": {"tileId": 0, "characterName": "succubus", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　ＮＲ１（後編）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 15, 14, 8]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 15, 13, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-82"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "succubus", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(895) === 2 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR2"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["口元が汚れてる"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 201, "indent": 0, "parameters": [0, 244, 21, 4, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 21, 3, 2]}, {"code": 203, "indent": 0, "parameters": [8, 0, 21, 4, 8]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [8, 8, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 9)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-28"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーンセックス　 11番騎乗位　ちんぽ先っぽしかいれてない"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-39"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ中間まで入れる"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-56"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ根本まで入れる"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-62"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 125, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-74"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 125, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-84"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 125, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-101"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ中間まで　抜けたところめっちゃ"]}, {"code": 408, "indent": 0, "parameters": ["白泡になってる。"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-112"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ先っぽ"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-121"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ先っぽケツよこに振って先っぽ愛"]}, {"code": 408, "indent": 0, "parameters": ["撫"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130-132"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　サキュバスの肩に手を置く"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-141"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックスシーン 11番騎乗位　ちんぽ根本まで入れる　白目剥く"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-151"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 125, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-161"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※死～ん……"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_170-173"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-204"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(895) === 3 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR3"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13-14"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン　１２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 11)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-30"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 11)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-47"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 11)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-58"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["森からサキュバス出てくる"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 201, "indent": 0, "parameters": [0, 244, 22, 6, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 21, 6, 8]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 21, 3, 2]}, {"code": 223, "indent": 0, "parameters": [[68, -34, -34, 0], 60, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-111"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 4]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(895) === 4 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR4"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウトイン　リフトアップセックス"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 12)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-12"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["街のシーン"]}, {"code": 231, "indent": 0, "parameters": [1, "BG_city_market1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-23"]}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-dungeon3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_24-27"]}, {"code": 108, "indent": 0, "parameters": ["乳揉み"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-48"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※フェードアウトイン　セックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 12)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-73"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 12)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-86"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["修道院シーン　夕方"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 223, "indent": 0, "parameters": [[68, -34, -34, 0], 60, true]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 14, 10, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-92"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 895, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(895) === 6 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR6（ＮＴＲルート最終イベント）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["サキュバス来る　腹ボテ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["succubus", 2]}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 14, 10, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-35"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 21, 7, 60, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_61-63"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 244, 21, 7, 70, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_71-72"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 99]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 503, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 895, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "succubus", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 && $gameSwitches.value(503) === true &&"]}, {"code": 408, "indent": 0, "parameters": ["             Math.min($gameVariables.value(895),6) === 6 && $gameVariables.value(250) === 999 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR1後にヤリゾーが死んでる場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 515, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 8}, {"id": 22, "name": "サキュバスＮＲ５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(895) === 5 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サキュバス　NR5"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["CG　窓から顔乗り出してる"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 13)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 45}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 13)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["屋内でガニ股でセックスされてるシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 255]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 14)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※左に窓から乗り出してる画　右にガニ股の画像"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [1620, 1620, 0, 0, 120]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 14)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精　サキュバス　歯食いしばりアヘ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["CG　窓から顔乗り出してる"]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 13)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 45}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 45}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※屋内シーン　射精完了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 10, 14)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [895, 895, 0, 0, 6]}, {"code": 117, "indent": 1, "parameters": [946]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 895, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 7}, {"id": 23, "name": "クエスト用ＮＰＣ『王都に迫る悪魔』承認１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1012, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(1012) >= 2 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(5) == 0"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"koushou\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [1012, 1012, 1, 0, 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(8, 100, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(8, 100, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 13}, null, null]}