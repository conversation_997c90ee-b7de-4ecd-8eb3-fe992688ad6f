/*:
 * @plugindesc ver.1.0.2 保存されたログを一覧するシーンを作成します。
 * <AUTHOR>
 * 
 * @param LogTypeVarId
 * @text ログタイプ指定変数
 * @type variable
 * @default 1
 * 
 * @param BlockVarId
 * @text ログブロック指定変数
 * @type variable
 * @default 2
 * 
 * @param BlockCategorySizeId
 * @text [v1.0.1]ログカテゴリー要素数指定変数
 * @type variable
 * @default 3
 * 
 * @param BlockCategoryMaxCols
 * @text [v1.0.1]ログカテゴリーカラム数
 * @type number
 * @default 6
 * 
 * @param BlockCategoryMaxItems
 * @text [v1.0.2]ログカテゴリー最大項目数
 * @type variable
 * @default 6
 * 
 * @param BlockCategoryTemplate
 * @text [v1.0.2]ログカテゴリー連番カラム
 * @default Day %1
 * 
 * @param BlockCategoryFontSize
 * @text [v1.0.1]ログカテゴリーフォントサイズ
 * @type number
 * @default 28
 * 
 * @param BlockListItemHeight
 * @text [v1.0.1]ログリスト一アイテムの高さ
 * @type number
 * @default 72
 * 
 * @param LogCallCommonEvent
 * @text [v1.0.1]ログ呼び出しコモンイベント
 * @type common_event
 * @default 989
 * 
 * @param LogCallCommonVarId
 * @text [v1.0.1]コモンイベント分岐変数ID
 * @type variable
 * @default 4
 * 
 * @param BlockCategories
 * @text ログブロック（ID/表示名）
 * @type struct<CategoryBlock>[]
 * @default ["{\"id\":\"1\",\"name\":\"1日目\"}","{\"id\":\"2\",\"name\":\"2日目\"}"]
 * 
 * @param LogCategoryRectangle
 * @text ログカテゴリーウィンドウサイズ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"0","width":"1280","height":"128"}
 * 
 * @param LogListRectangle
 * @text ログリストウィンドウサイズ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"128","width":"1280","height":"832"}
 * 
 * @help
 * 【プラグインコマンド】
 * KNS_LogListScene call
 * 　ログリストシーンを呼び出します。
 * 
 * 【プラグインパラメータ】
 * ・「ログタイプ指定変数」はset_mlogの実行内容を変数の値によって
 * 　以下のように変化させます
 * 　0: マップのログウィンドウに表示する
 * 　1: ログリストシーンのみに記録する
 * 　2: 0, 1両方行う
 * 
 * ・「ログブロック指定変数」はset_mlogが実行された際、ログリストの
 * 　どの「ログブロック」に保存するかを指定する変数です。
 * 　変数の値は「ログブロック」のIDと一致します。
 * 
 * 【コモンイベント制御文字】
 * ・ログ中の文章を以下の制御文字で囲むことで
 * 　パラメータで指定した変数にタグ内の値を格納し
 * 　パラメータで指定したコモンイベントを呼ぶことができます
 * \\CA[値]文字列\\CA[0]
 * 
 * 例）
 * \\CA[32]このイベント\\CA[0]を見る
 * 
 * 【更新履歴】
 * ver.1.0.0(2023-09-11)
 * - デモ
 * ver.1.0.0(2023-09-13)
 * - コモンイベント制御文字の仕様に誤りがあったため変更
 * - ブロックカテゴリーウィンドウにカラム数指定を追加
 * - ブロックカテゴリーウィンドウのカラム数を変数で指定できるよう対応
 * - ブロックリストウィンドウにの行数を選択できるよう対応
 * - ウィンドウ非アクティブ時に半透明で表示するよう対応
 * ver.1.0.2(2023-09-15)
 * - ログカテゴリーにフォントサイズ指定を追加
 * - ログカテゴリーに連番項目を追加。
 * 　カテゴリーよりカテゴリー最大数が多いとき変数値1から項目を追加する
 */
/*~struct~Rectangle:
 * @param x
 * @text X
 * @type number
 * @min -65536
 * @max 65535
 * @default 0
 * 
 * @param y
 * @text Y
 * @type number
 * @min -65536
 * @max 65535
 * @default 0
 * 
 * @param width
 * @text 幅
 * @type number
 * @default 0
 * 
 * @param height
 * @text 高さ
 * @type number
 * @default 0
 */
/*~struct~CategoryBlock:
 * @param id
 * @text ログブロック番号
 * @type number
 * @default 1
 * 
 * @param name
 * @text ログブロック名
 * @default 1日目
 */

const KNS_LogListScene = {
    name: "KNS_LogListScene",
    param: null,
    reLogText: /^_log_database_text_(.+)$/,
    parseRectangle: function(obj, key){
        const rect = JsonEx.parse(obj[key] || "{}");
        rect.x = Math.floor(rect.x);
        rect.y = Math.floor(rect.y);
        rect.width  = Math.floor(rect.width);
        rect.height = Math.floor(rect.height);
        return obj[key] = rect;
    }
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.LogTypeVarId = Math.floor(this.param.LogTypeVarId);
    this.param.BlockVarId = Math.floor(this.param.BlockVarId);
    this.param.BlockCategorySizeId = Math.floor(this.param.BlockCategorySizeId);
    this.param.BlockCategoryMaxCols = Math.floor(this.param.BlockCategoryMaxCols);
    this.param.BlockListItemHeight = Math.floor(this.param.BlockListItemHeight);
    this.param.LogCallCommonEvent = Math.floor(this.param.LogCallCommonEvent);
    this.param.LogCallCommonVarId = Math.floor(this.param.LogCallCommonVarId);
    this.param.BlockCategoryFontSize = Math.floor(this.param.BlockCategoryFontSize);
    this.param.BlockCategoryMaxItems = Math.floor(this.param.BlockCategoryMaxItems);

    this.param.BlockCategories = JsonEx.parse(this.param.BlockCategories).map(function(json){
        const obj = JsonEx.parse(json);
        obj.id = Math.floor(obj.id);
        obj.name = String(obj.name);
        return obj;
    });
    this.parseRectangle(this.param, "LogCategoryRectangle");
    this.parseRectangle(this.param, "LogListRectangle");

    //============================================================
    // alias window
    //============================================================
    const _window_set_mlog = window.set_mlog;
    window.set_mlog = function(text){
        const type = $gameVariables.value(KNS_LogListScene.param.LogTypeVarId);
        if (type === 0 || type === 2){
            _window_set_mlog.apply(this, arguments);
        }
        if (type === 1 || type === 2){
            $gameSystem.knsAddLogList($gameVariables.value(KNS_LogListScene.param.BlockVarId), text);
        }
    }

    //============================================================
    // alias Game_System
    //============================================================
    Game_System.prototype.knsAddLogList = function(i, text){
        if (KNS_LogListScene.param.BlockCategories.find(function(category){
            return category.id === i;
        })){
            const args = [text, {}];
            let tempText;
            if (KNS_LogListScene.reLogText.test(text) && $LogSheetCSV.Exist(RegExp.$1)){
                tempText = $LogSheetCSV.Get(RegExp.$1);
            }else{
                tempText = text;
            }
            tempText.replace(/\\v\[(\d+)\]/g, function(_, n){
                let id = Math.floor(n);
                args[1][id] = $gameVariables.value(id);
            });
            this.knsGetLogList(i).push(args);
        }
    }

    Game_System.prototype.knsGetLogList = function(i){
        if (!this._knsLogList){ this._knsLogList = {}; }
        if (!this._knsLogList[i]){ this._knsLogList[i] = []; }
        return this._knsLogList[i];
    }

    //============================================================
    // alias Game_Interpreter
    //============================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_LogListScene.name){ return; }
        switch ((args[0] || "").toLowerCase()){
            case 'call':{
                SceneManager.push(Scene_KnsLogScene);
                break;
            }
        }
    };

    //============================================================
    // alias Window_Base
    //============================================================
    const _Window_Base_drawTextEx = Window_Base.prototype.drawTextEx;
    Window_Base.prototype.drawTextEx = function(text, x, y){
        this._knsCeHyperLinkId = 0;
        this._knsCeHyperLinkLastColor = null;
        return _Window_Base_drawTextEx.apply(this, arguments);
    }

    const _Window_Base_processEscapeCharacter = Window_Base.prototype.processEscapeCharacter;
    Window_Base.prototype.processEscapeCharacter = function(code, textState) {
        if (code === 'CA') {
            const id = this.obtainEscapeParam(textState);
            if (id === 0){
                this.changeTextColor(this._knsCeHyperLinkLastColor);
                this._knsCeHyperLinkLastColor = null;
            }else{
                this._knsCeHyperLinkId = id;
                this._knsCeHyperLinkLastColor = this.contents.textColor;
                this.changeTextColor(this.systemColor());
            }
        }else{
            _Window_Base_processEscapeCharacter.apply(this, arguments);
        }
    };

    const _Window_Base_processNormalCharacter = Window_Base.prototype.processNormalCharacter;
    Window_Base.prototype.processNormalCharacter = function(textState) {
        if (this._knsCeHyperLinkLastColor){
            const c = textState.text[textState.index];
            const w = this.textWidth(c);
            this.contents.fillRect(textState.x, textState.y + textState.height - 4, w, 1, this.contents.textColor);
        }
        _Window_Base_processNormalCharacter.apply(this, arguments);
    };

    const _Window_Base_knsDrawTextAutoline = Window_Base.prototype.knsDrawTextAutoline;
    Window_Base.prototype.knsDrawTextAutoline = function(text, x, y){
        this._knsCeHyperLinkId = 0;
        this._knsCeHyperLinkLastColor = null;
        return _Window_Base_knsDrawTextAutoline.apply(this, arguments);
    }

    const _Window_Base_knsProcessNormalCharacterAutoline = Window_Base.prototype.knsProcessNormalCharacterAutoline;
    Window_Base.prototype.knsProcessNormalCharacterAutoline = function(textState) {
        _Window_Base_knsProcessNormalCharacterAutoline.apply(this, arguments);
        if (this._knsCeHyperLinkLastColor){
            const c = textState.text[textState.index - 1];
            const w = this.textWidth(c);
            this.contents.fillRect(textState.x - w, textState.y + textState.height - 4, w, 1, this.contents.textColor);
        }
	};
}).call(KNS_LogListScene);

//============================================================
// new Window_KnsLogCategory
//============================================================
class Window_KnsLogCategory extends Window_HorzCommand{
    initialize(rect){
        this._knsRect = rect;
        super.initialize(rect.x, rect.y);
        this._knsLastListIndex = {};
        this.select(0);
    }
    windowWidth(){ return this._knsRect.width; }
    windowHeight(){ return this._knsRect.height; }
    maxCols(){ return KNS_LogListScene.param.BlockCategoryMaxCols; }
    maxItems(){ return $gameVariables.value(KNS_LogListScene.param.BlockCategoryMaxItems); }
    itemTextAlign(){ return 'center'; }
    standardFontSize(){ return KNS_LogListScene.param.BlockCategoryFontSize; }
    lineHeight(){ return KNS_LogListScene.param.BlockCategoryFontSize; }
    makeCommandList(){
        const max = this.maxItems();
        KNS_LogListScene.param.BlockCategories.slice(0, max).forEach(function(block){
            this.addCommand(block.name, 'ok', true, block.id);
        }, this);
        let j = 1;
        for (let i = KNS_LogListScene.param.BlockCategories.length; i < max; i++){
            this.addCommand(KNS_LogListScene.param.BlockCategoryTemplate.format(j), 'ok', true, j);
            j++;
        }
        
    }
    updateHelp(){
        const item = this._list[this.index()];
        if (!item){ return; }
        this._helpWindow.knsSetCategory(item.ext || 0);
        if (this._knsLastListIndex[item.ext] === undefined){
            this._helpWindow.deselect();   
        }else{
            this._helpWindow.select(this._knsLastListIndex[item.ext]);
        }
    }
    knsSaveLastIndex(){
        const item = this._list[this.index()];
        if (!item){ return; }
        this._knsLastListIndex[item.ext] = this._helpWindow.index();
    }
    activate(){
        super.activate();
        this.contentsOpacity = 255;
    }
    deactivate(){
        super.deactivate();
        this.contentsOpacity = 128;
    }
};

//============================================================
// new Window_KnsLogList
//============================================================
class Window_KnsLogList extends Window_Selectable{
    initialize(rect){
        super.initialize(rect.x, rect.y, rect.width, rect.height);
        this._knsCategoryId = -1;
        this._list = [];
    }
    knsSetCategory(id){
        if (this._knsCategoryId !== id){
            this._knsCategoryId = id;
            this.refresh();
        }
    }
    refresh(){
        this._list = $gameSystem.knsGetLogList(this._knsCategoryId);
        this._knsCommonId = {};
        super.refresh();
    }
    maxItems(){ return this._list ? this._list.length : 0; }
    itemHeight(){ return KNS_LogListScene.param.BlockListItemHeight; }
    drawItem(index){
        const item = this._list[index];
        if (item){
            const rect = this.itemRect(index);
            let text = item[0];
            if (KNS_LogListScene.reLogText.test(text) && $LogSheetCSV.Exist(RegExp.$1)){
                text = $LogSheetCSV.Get(RegExp.$1).replace(/\\{2}/g, "\\");
            }
            text = text.replace(/\\v\[(\d+)\]/g, function(_, n){
                return item[1][n] || "";
            });
            this.knsDrawTextAutoline(text, rect.x, rect.y);
            this._knsCommonId[index] = this._knsCeHyperLinkId;
        }
    }
    knsGetCommonEventId(){
        return this._knsCommonId[this.index()] || 0;
    }
    activate(){
        super.activate();
        this.contentsOpacity = 255;
    }
    deactivate(){
        super.deactivate();
        this.contentsOpacity = 128;
    }
};

//============================================================
// new Scene_KnsLogScene
//============================================================
class Scene_KnsLogScene extends Scene_MenuBase{
    create(){
        super.create();
        this.knsCreateLogCategoryWindow();
        this.knsCreateLogListWindow();
    }
    knsCreateLogCategoryWindow(){
        this._knsLogCategoryWindow = new Window_KnsLogCategory(KNS_LogListScene.param.LogCategoryRectangle);
        this._knsLogCategoryWindow.setHandler("ok", this.knsOnCategoryOk.bind(this));
        this._knsLogCategoryWindow.setHandler("cancel", this.popScene.bind(this));
        this.addWindow(this._knsLogCategoryWindow);
    }
    knsOnCategoryOk(){
        if (this._knsLogListWindow.index() === -1){
            this._knsLogListWindow.select(0);
        }
        this._knsLogListWindow.activate();
    }
    knsCreateLogListWindow(){
        this._knsLogListWindow = new Window_KnsLogList(KNS_LogListScene.param.LogListRectangle);
        this._knsLogListWindow.setHandler("ok", this.knsOnListOk.bind(this));
        this._knsLogListWindow.setHandler("cancel", this.knsOnListCancel.bind(this));
        this.addWindow(this._knsLogListWindow);
        this._knsLogCategoryWindow.setHelpWindow(this._knsLogListWindow);
    }
    knsOnListOk(){
        const commonId = this._knsLogListWindow.knsGetCommonEventId();
        if (commonId === 0){
            this._knsLogListWindow.activate();
        }else{
            $gameVariables.setValue(KNS_LogListScene.param.LogCallCommonVarId, commonId);
            $gameTemp.reserveCommonEvent(KNS_LogListScene.param.LogCallCommonEvent);
            SceneManager.goto(Scene_Map);
        }
    }
    knsOnListCancel(){
        this._knsLogCategoryWindow.knsSaveLastIndex();
        this._knsLogCategoryWindow.activate();
    }
};
