/*:
 * @plugindesc Adds an option to hide battle graphics and toggles Switch 197.
 * <AUTHOR>
 *
 * @help
 * This plugin adds a "Hide Battle Graphic" option in the Options menu. 
 * When this option is enabled, Switch 197 will be turned ON.
 * When this option is disabled, Switch 197 will be turned OFF.
 */

var Imported = Imported || {};
Imported.HideBattleGraphicOption = true;

var HideBattleGraphicOption = {};

(function() {
    // Define ConfigManager property
    Object.defineProperty(ConfigManager, 'hideBattleGraphic', {
        get: function() {
            return this._hideBattleGraphic;
        },
        set: function(value) {
            this._hideBattleGraphic = value;
            if ($gameSwitches) {  // Check if $gameSwitches is initialized
                $gameSwitches.setValue(197, this._hideBattleGraphic);
            }
        },
        configurable: true
    });

    // Initialize the property
    var _ConfigManager_makeData = ConfigManager.makeData;
    ConfigManager.makeData = function() {
        var config = _ConfigManager_makeData.call(this);
        config.hideBattleGraphic = this.hideBattleGraphic || false;
        return config;
    };

    var _ConfigManager_applyData = ConfigManager.applyData;
    ConfigManager.applyData = function(config) {
        _ConfigManager_applyData.call(this, config);
        this.hideBattleGraphic = this.readFlag(config, 'hideBattleGraphic', false);
        if ($gameSwitches) {  // Check again here to ensure safety
            $gameSwitches.setValue(197, this.hideBattleGraphic);
        }
    };

    // Initialize the _hideBattleGraphic property
    ConfigManager._hideBattleGraphic = false;

    // Add to the options window
    var _Window_Options_addGeneralOptions = Window_Options.prototype.addGeneralOptions;
    Window_Options.prototype.addGeneralOptions = function() {
        _Window_Options_addGeneralOptions.call(this);
        this.addCommand('Hide Battle Graphic', 'hideBattleGraphic');
    };

})();
