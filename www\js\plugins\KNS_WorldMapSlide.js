/*:
 * @plugindesc KNS_SlideShowを使用しワールドマップにスライドを表示します
 * <AUTHOR>
 * 
 * @param WorldMapSwitch
 * @text スライド表示フラグ
 * @type switch
 * @default 240
 * 
 * @param optionDisplayPosition
 * @text 選択肢表示位置
 * @type select
 * @default right
 * 
 * @option left
 * @option right
 * 
 * @param TitleHeight
 * @text マップ名行数
 * @type number
 * @default 1
 * 
 * @param DescHeight
 * @text 説明文行数
 * @type number
 * @default 5
 * 
 * @help
 * ※このプラグインはKNS_SlideShow以下に設置してください。
 * 
 * 指定のスイッチがONのとき、足元のイベントのメモ欄記述に
 * 対応した文章をスライド形式で表示します。
 * 非表示にする場合は「KNS_SlideShow remove」で表示を
 * 消すことができます。
 * 
 * ■logsheet指定
 * world_map/{ID}/name, world_map/{ID}/descのキーを追加し
 * マップ名、説明文を指定する
 * 
 * 例）
 * world_map/001/name,"テスト001 title",,,,,
 * world_map/001/desc,"テスト001 desc",,,,,
 * world_map/002/name,"テスト002 title",,,,,
 * world_map/002/desc,"テスト002 desc",,,,,
 * 
 * ■イベントメモ欄記述
 * 上記のキーで指定したIDと対応するイベントのメモ欄に以下の記述をする
 * <KNS_WorldMapSlide: {ID}>
 * 
 * 例）
 * <KNS_WorldMapSlide: 001>
 * 
 * ■プラグインコマンド
 * KNS_WorldMapSlide show
 * 　足元のイベントにメモ欄記述があればスライドとして表示する
 * （イベントなどで非表示にされた後の復帰用）
 */

const KNS_WorldMapSlide = {
    name: "KNS_WorldMapSlide",
    param: null
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.WorldMapSwitch = Number(this.param.WorldMapSwitch);
    this.param.TitleHeight = Number(this.param.TitleHeight);
    this.param.DescHeight = Number(this.param.DescHeight);
    this.param.optionDisplayPosition = String(this.param.optionDisplayPosition);

    //=======================================================
    // alias Game_Interpreter
    //=======================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_WorldMapSlide.name){ return; }
        switch (args[0].toLowerCase()){
            case 'show':{ $gameParty.knsCheckWorldSlideShow(); break; }
        }
    }

    //==========================================
    // alias Game_Party
    //==========================================
    const _Game_Party_onPlayerWalk = Game_Party.prototype.onPlayerWalk;
    Game_Party.prototype.onPlayerWalk = function(triggers) {
        if ($gameSwitches.value(KNS_WorldMapSlide.param.WorldMapSwitch)){
            this.knsCheckWorldSlideShow();
        }
        _Game_Party_onPlayerWalk.apply(this, arguments);
    };

    Game_Party.prototype.knsCheckWorldSlideShow = function(){
        const ev = $gameMap.eventsXy($gamePlayer.x, $gamePlayer.y).find(function(ev){
            return ev.event().meta[KNS_WorldMapSlide.name];
        });
        const list = $gameSystem.knsGetSlideShowList();
        if (list.length > 0){ list.length = 0; }
        if (ev){
            let num = Number(ev.event().meta[KNS_WorldMapSlide.name]);
            let sn = ("000" + num).slice(-3);
            [
                ["name", KNS_WorldMapSlide.param.TitleHeight],
                ["desc", KNS_WorldMapSlide.param.DescHeight]
            ].forEach(function(info){
                let key = `world_map/${sn}/${info[0]}`;
                if ($LogSheetCSV.Exist(key)){
                    $gameSystem.knsReserveSlideShow(
                        $LogSheetCSV.Get(key) || "",
                        KNS_WorldMapSlide.param.optionDisplayPosition,
                        -1,
                        info[1]
                    );
                }else{
                    console.error(`[${KNS_WorldMapSlide.name}]存在しない説明文（${key}）`);
                }
            }, this);
        }
    }
}).call(KNS_WorldMapSlide);