{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "PerituneMaterial_Flow", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 60, "note": "dark_night\nBGM固定\n王都\n浴場\n拠点\n公共の場", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 35, "data": [5940, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5948, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 5968, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 5968, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 2370, 2356, 2356, 2356, 2356, 2356, 2356, 2372, 5968, 2370, 2356, 2356, 2356, 2356, 2356, 2356, 2372, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 5968, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 2376, 2364, 2364, 2364, 2364, 2364, 2364, 2374, 5968, 2376, 2364, 2364, 2364, 2364, 2364, 2364, 2374, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5968, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5968, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5970, 5939, 5972, 1598, 1598, 1598, 1598, 1598, 1598, 1598, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5938, 5956, 5956, 5972, 0, 5970, 5956, 5956, 5937, 5936, 5938, 5956, 5956, 5972, 0, 5970, 5956, 5956, 5962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5940, 5964, 5964, 5974, 0, 5976, 5964, 5964, 5944, 5936, 5940, 5964, 5964, 5974, 0, 5976, 5964, 5964, 5961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6514, 6514, 6518, 3290, 6515, 6514, 6514, 5976, 5948, 5974, 6514, 6514, 6518, 3290, 6515, 6514, 6514, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6520, 6520, 6524, 3280, 6521, 6520, 6520, 6515, 5968, 6518, 6520, 6520, 6524, 3280, 6521, 6520, 6520, 5953, 5956, 5956, 5956, 5956, 5956, 5956, 5972, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 3282, 3268, 3268, 3251, 3268, 3268, 3284, 6521, 5968, 6524, 3282, 3268, 3268, 3251, 3268, 3268, 3284, 5952, 5940, 5964, 5964, 5964, 5964, 5964, 5961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 3264, 3248, 3248, 3248, 3248, 3248, 3250, 3284, 5968, 3282, 3249, 3248, 3248, 3248, 3248, 3248, 3272, 5952, 5960, 6514, 6514, 6514, 6514, 6514, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 3288, 3276, 3276, 3260, 3276, 3276, 3276, 3286, 5968, 3288, 3276, 3276, 3276, 3260, 3276, 3276, 3286, 5952, 5960, 6520, 6520, 6520, 6520, 6520, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5938, 5956, 5956, 5972, 3280, 5970, 5956, 5956, 5956, 5939, 5956, 5956, 5956, 5972, 3280, 5970, 5956, 5956, 5937, 5960, 0, 0, 0, 0, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5940, 5964, 5964, 5974, 3280, 5976, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5974, 3280, 5976, 5964, 5964, 5944, 5960, 0, 0, 0, 0, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6514, 6514, 6518, 3292, 6515, 6514, 6514, 6514, 6514, 6514, 6514, 6514, 6518, 3292, 6515, 6514, 6514, 5952, 5960, 0, 0, 0, 0, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 6520, 6520, 6524, 0, 6521, 6520, 6520, 6520, 6520, 6520, 6520, 6520, 6524, 0, 6521, 6520, 6520, 5976, 5974, 0, 5979, 5969, 5981, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6515, 6518, 0, 6515, 6514, 6518, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6521, 6524, 0, 6521, 6520, 6524, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5970, 5956, 5956, 5956, 5956, 5956, 5956, 5962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5952, 5936, 5936, 5936, 5936, 5936, 5936, 5960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5966, 5969, 5969, 5969, 5969, 5969, 5969, 5981, 0, 0, 0, 5979, 5969, 5969, 5969, 5969, 5969, 5969, 5965, 5964, 5964, 5964, 5964, 5964, 5964, 5974, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3436, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3436, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3410, 3420, 3420, 3420, 3417, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 0, 0, 0, 0, 0, 3424, 0, 0, 0, 3424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3395, 3412, 3428, 3962, 3426, 3412, 3428, 3962, 3426, 3412, 3395, 3412, 3412, 3428, 0, 0, 3424, 0, 0, 0, 3424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3416, 3952, 3432, 3420, 3430, 3952, 3408, 3392, 3392, 3392, 3392, 3416, 0, 0, 3424, 0, 0, 0, 3424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3416, 3961, 3953, 3953, 3953, 3959, 3408, 3392, 3392, 3392, 3392, 3398, 3425, 3425, 3423, 3425, 3425, 3425, 3431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3392, 3394, 3428, 3090, 3076, 3092, 3426, 3393, 3392, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3420, 3420, 3420, 3420, 3430, 3072, 3056, 3080, 3432, 3420, 3420, 3420, 3420, 3420, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 317, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 325, 326, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 309, 310, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 370, 0, 371, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 250, 378, 0, 379, 243, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 251, 0, 0, 0, 251, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 13, 0, 0, 0, 13, 13, 238, 0, 238, 13, 13, 0, 0, 0, 13, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 246, 0, 246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 848, 372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 380, 848, 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 197, 0, 184, 184, 184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 192, 192, 192, 0, 0, 0, 0, 0, 0, 372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 128, 372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 380, 136, 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 362, 0, 362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$yaba12 = 0"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 3, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 3, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 25}, {"id": 3, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 121, "indent": 0, "parameters": [110, 110, 1]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 121, "indent": 1, "parameters": [8, 8, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 8, 3, 24, 0, 0]}, {"code": 134, "indent": 0, "parameters": [1]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [1, 10, 0, 1, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 2, 1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 25}, {"id": 4, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 3, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 3, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 25}, {"id": 5, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map16_5_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [0, 110, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_map16_mada\")"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map16_5_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 19}, {"id": 6, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [0, 110, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_map16_mada\")"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map16_6_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map16_6_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 19}, {"id": 7, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 121, "indent": 1, "parameters": [45, 45, 0]}, {"code": 135, "indent": 1, "parameters": [0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 121, "indent": 1, "parameters": [45, 45, 1]}, {"code": 135, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 10}, {"id": 8, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 135, "indent": 1, "parameters": [0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 135, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 10}, {"id": 9, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 20, "y": 21}, {"id": 10, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 19, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 12, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 17, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [3]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 21}, {"id": 11, "name": "店主", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [1, 1107, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 1107, 0, 99, 4]}, {"code": 122, "indent": 2, "parameters": [74, 74, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(63, \"about_quest\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bath_pri_kabe = 0"]}, {"code": 122, "indent": 0, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 111, "indent": 1, "parameters": [1, 10, 0, 2, 1]}, {"code": 122, "indent": 2, "parameters": [13, 13, 3, 0, 2]}, {"code": 111, "indent": 2, "parameters": [1, 11, 1, 13, 4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_10"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 2, "parameters": [["\\n[1]", "\\n[2]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\n[1]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_3"]}, {"code": 108, "indent": 3, "parameters": ["覗き穴イベント用"]}, {"code": 111, "indent": 3, "parameters": [1, 5, 0, 1, 1]}, {"code": 111, "indent": 4, "parameters": [0, 31, 0]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 11, 3)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 221, "indent": 3, "parameters": []}, {"code": 129, "indent": 3, "parameters": [2, 1, 0]}, {"code": 117, "indent": 3, "parameters": [57]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\n[2]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_3"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 129, "indent": 3, "parameters": [1, 1, 0]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [111, 111, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 11, 1, 13, 4]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_10"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [111, 111, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 125, "indent": 1, "parameters": [1, 1, 13]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 121, "indent": 1, "parameters": [110, 110, 0]}, {"code": 134, "indent": 1, "parameters": [0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[63] if(v[74]>=1)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[63] if(v[74]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["クエスト　浴場改良"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 11, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 110, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["****************************"]}, {"code": 408, "indent": 0, "parameters": ["* 浴場覗き穴イベント時"]}, {"code": 408, "indent": 0, "parameters": ["****************************"]}, {"code": 111, "indent": 0, "parameters": [0, 31, 0]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["* 覗き穴イベント１"]}, {"code": 111, "indent": 1, "parameters": [1, 703, 0, 1, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map16_ev11_p3_101\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map16_ev11_p3_102\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_2-3"]}, {"code": 205, "indent": 3, "parameters": [14, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 3, "parameters": [17, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 355, "indent": 3, "parameters": ["$bath_pri_kabe = 1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["* 覗き穴イベント２"]}, {"code": 408, "indent": 1, "parameters": ["ガニ股ダンス習得済の場合発生"]}, {"code": 111, "indent": 1, "parameters": [1, 703, 0, 2, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map16_ev11_p3_201\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map16_ev11_p3_102\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_11-14"]}, {"code": 205, "indent": 3, "parameters": [14, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 3, "parameters": [17, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 505, "indent": 3, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 3, "parameters": [32, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 3, "parameters": [33, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 355, "indent": 3, "parameters": ["$bath_pri_kabe = 1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエスト　『浴場改良』"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 1107, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["クエスト受注後最初に話しかけたとき"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-3"]}, {"code": 122, "indent": 1, "parameters": [1107, 1107, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["アイテム集まったかチェック"]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 3, 0, 350, 0]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 10, 1]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 3, 0, 351, 0]}, {"code": 111, "indent": 2, "parameters": [1, 54, 0, 10, 1]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 3, 0, 2, 0]}, {"code": 111, "indent": 3, "parameters": [1, 54, 0, 5, 1]}, {"code": 108, "indent": 4, "parameters": ["集まってたらクリア"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_10"]}, {"code": 122, "indent": 4, "parameters": [1107, 1107, 0, 0, 98]}, {"code": 117, "indent": 4, "parameters": [46]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["集まってない場合は再度集めるアイテムを教えてくれる。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 21}, {"id": 12, "name": "司祭側入浴", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 212, "indent": 1, "parameters": [-1, 69, true]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [1, 1107, 0, 99, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 50]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["x = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": ["tp_heal(2,x)"]}, {"code": 111, "indent": 1, "parameters": [0, 111, 0]}, {"code": 355, "indent": 2, "parameters": ["x = $gameVariables.value(54)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(1,x)"]}, {"code": 111, "indent": 2, "parameters": [4, 1, 6, 30]}, {"code": 313, "indent": 3, "parameters": [0, 1, 1, 30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["******************"]}, {"code": 408, "indent": 1, "parameters": ["ぶっかけ洗い流し"]}, {"code": 408, "indent": 1, "parameters": ["******************"]}, {"code": 122, "indent": 1, "parameters": [1701, 2600, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 1, "parameters": [90, 90, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 71]}, {"code": 122, "indent": 1, "parameters": [91, 91, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 72]}, {"code": 122, "indent": 1, "parameters": [92, 92, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 73]}, {"code": 122, "indent": 1, "parameters": [93, 93, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 74]}, {"code": 122, "indent": 1, "parameters": [94, 94, 0, 0, 0]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 75]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 31]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 31]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["風呂から上がる処理"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 5}, {"id": 13, "name": "主人公", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protag-naked", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 3}, {"id": 14, "name": "ヒロイン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine_naked2", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 15, "name": "男側入浴", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["入浴開始"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 212, "indent": 1, "parameters": [-1, 69, true]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [1, 1107, 0, 99, 0]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 50]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["x = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": ["tp_heal(1,x)"]}, {"code": 111, "indent": 1, "parameters": [0, 111, 0]}, {"code": 355, "indent": 2, "parameters": ["x = $gameVariables.value(54)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(2,x)"]}, {"code": 108, "indent": 2, "parameters": ["******************"]}, {"code": 408, "indent": 2, "parameters": ["ぶっかけ洗い流し"]}, {"code": 408, "indent": 2, "parameters": ["******************"]}, {"code": 122, "indent": 2, "parameters": [1701, 2600, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 2, "parameters": [90, 90, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 71]}, {"code": 122, "indent": 2, "parameters": [91, 91, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 72]}, {"code": 122, "indent": 2, "parameters": [92, 92, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 73]}, {"code": 122, "indent": 2, "parameters": [93, 93, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 74]}, {"code": 122, "indent": 2, "parameters": [94, 94, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 75]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 0]}, {"code": 111, "indent": 2, "parameters": [4, 2, 6, 31]}, {"code": 313, "indent": 3, "parameters": [0, 2, 1, 31]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 1, 6, 30]}, {"code": 313, "indent": 2, "parameters": [0, 1, 1, 30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["覗きイベント(ヤリゾー / 昼）"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 1]}, {"code": 111, "indent": 2, "parameters": [0, 503, 1]}, {"code": 111, "indent": 3, "parameters": [0, 643, 0]}, {"code": 111, "indent": 4, "parameters": [0, 32, 0]}, {"code": 111, "indent": 5, "parameters": [0, 111, 0]}, {"code": 111, "indent": 6, "parameters": [1, 881, 0, 0, 0]}, {"code": 355, "indent": 7, "parameters": ["MapEvent.call(0, 39,1)"]}, {"code": 119, "indent": 7, "parameters": ["風呂から上がる"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["覗きイベント(二人組 / 夜）"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 1, 1]}, {"code": 111, "indent": 2, "parameters": [1, 703, 0, 0, 0]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 15, 2)"]}, {"code": 119, "indent": 3, "parameters": ["風呂から上がる"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 703, 0, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 15, 3)"]}, {"code": 119, "indent": 3, "parameters": ["風呂から上がる"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 703, 0, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 15, 4)"]}, {"code": 119, "indent": 3, "parameters": ["風呂から上がる"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["風呂から上がる"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["風呂から上がる処理"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["覗きイベント１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["（データベース用　呼び出しは　別ページから行う）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [32, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, -1]}, {"code": 14, "indent": null, "parameters": [0, 1]}, {"code": 38, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "indent": null, "parameters": [0, -1]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "indent": null, "parameters": [0, 1]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101"]}, {"code": 205, "indent": 0, "parameters": [33, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_102"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map16_ev15_101\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map16_ev15_102\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_103-104"]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"koushou\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_110-111"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Blow1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [32, {"list": [{"code": 29, "indent": null, "parameters": [5]}, {"code": 37, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 32, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 31, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 32, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 31, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_112"]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 2, "parameters": [33, 8, true]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, -1]}, {"code": 38, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, 1]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, -1]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, 1]}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_113"]}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_114"]}, {"code": 111, "indent": 2, "parameters": [0, 5, 1]}, {"code": 122, "indent": 3, "parameters": [703, 703, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 117, "indent": 3, "parameters": [97]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_199"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["覗きイベント２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["（データベース用　呼び出しは　別ページから行う）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 111, "indent": 0, "parameters": [12, "$bath_pri_kabe == 1"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, -1]}, {"code": 14, "indent": null, "parameters": [0, 1]}, {"code": 38, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "indent": null, "parameters": [0, -1]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "indent": null, "parameters": [0, 1]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 213, "indent": 1, "parameters": [33, 1, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2-4"]}, {"code": 111, "indent": 1, "parameters": [1, 35, 0, 1, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_5-6"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_7-8"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_9"]}, {"code": 111, "indent": 1, "parameters": [1, 36, 0, 1, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10-11"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_12-13"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_14-16"]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 29, "indent": null, "parameters": [5]}, {"code": 37, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 32, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 31, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 32, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 31, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_17-21"]}, {"code": 213, "indent": 1, "parameters": [-1, 4, true]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["$bath_pri_kabe = 2"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [97]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["if (typeof $bath_pri_kabe === \"undefined\"){"]}, {"code": 655, "indent": 0, "parameters": ["$bath_pri_kabe = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["if (typeof $bath_pri === \"undefined\"){"]}, {"code": 655, "indent": 0, "parameters": ["$bath_pri = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["覗きイベント３ (穴に向かってガニ股ダンス）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["（データベース用　呼び出しは　別ページから行う）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 111, "indent": 0, "parameters": [12, "$bath_pri_kabe == 1"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 231, "indent": 1, "parameters": [89, "BG_nozokiana", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 203, "indent": 1, "parameters": [32, 0, 14, 8, 8]}, {"code": 203, "indent": 1, "parameters": [33, 0, 14, 9, 8]}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 250, "indent": 1, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["*********************************"]}, {"code": 408, "indent": 2, "parameters": ["見せる場合"]}, {"code": 408, "indent": 2, "parameters": ["*********************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_6"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [32, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [32, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, -1]}, {"code": 14, "indent": null, "parameters": [0, 1]}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, -1]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, 1]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 213, "indent": 2, "parameters": [32, 1, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10-15"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Blow1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [32, {"list": [{"code": 29, "indent": null, "parameters": [5]}, {"code": 37, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 32, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 31, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "indent": null, "parameters": [5]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 32, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 31, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 2, "parameters": [33, 1, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_16"]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 14, "indent": null, "parameters": [0, -1]}, {"code": 14, "indent": null, "parameters": [0, 1]}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, -1]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "indent": null, "parameters": [0, 1]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_17-20"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21"]}, {"code": 111, "indent": 2, "parameters": [0, 5, 1]}, {"code": 355, "indent": 3, "parameters": ["$bath_pri_kabe = 3"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*********************************"]}, {"code": 408, "indent": 2, "parameters": ["見せる場合ここまで"]}, {"code": 408, "indent": 2, "parameters": ["*********************************"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["*********************************"]}, {"code": 408, "indent": 2, "parameters": ["見せない場合"]}, {"code": 408, "indent": 2, "parameters": ["*********************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_7"]}, {"code": 204, "indent": 2, "parameters": [4, 2, 4]}, {"code": 230, "indent": 2, "parameters": [20]}, {"code": 213, "indent": 2, "parameters": [17, 1, true]}, {"code": 205, "indent": 2, "parameters": [17, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 204, "indent": 2, "parameters": [6, 2, 4]}, {"code": 230, "indent": 2, "parameters": [20]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_6"]}, {"code": 205, "indent": 2, "parameters": [32, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [33, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 213, "indent": 2, "parameters": [32, 8, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_8"]}, {"code": 111, "indent": 2, "parameters": [0, 5, 1]}, {"code": 355, "indent": 3, "parameters": ["$bath_pri_kabe = 2"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*********************************"]}, {"code": 408, "indent": 2, "parameters": ["見せない場合ここまで"]}, {"code": 408, "indent": 2, "parameters": ["*********************************"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 201, "indent": 1, "parameters": [0, 16, 14, 3, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 111, "indent": 2, "parameters": [12, "$bath_pri == 3"]}, {"code": 122, "indent": 3, "parameters": [703, 703, 0, 0, 3]}, {"code": 108, "indent": 3, "parameters": ["２にすることで合流時にイベントを発生させる"]}, {"code": 408, "indent": 3, "parameters": ["２にすることでリセットされるまで再発生させない"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [97]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 5}, {"id": 16, "name": "相方を待つ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["if(typeof $bath_pri_kabe == \"undefined\"){"]}, {"code": 655, "indent": 0, "parameters": ["  $bath_pri_kabe = 0"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 134, "indent": 1, "parameters": [1]}, {"code": 121, "indent": 1, "parameters": [111, 111, 1]}, {"code": 121, "indent": 1, "parameters": [110, 110, 1]}, {"code": 129, "indent": 1, "parameters": [1, 1, 0]}, {"code": 129, "indent": 1, "parameters": [2, 1, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, 0]}, {"code": 129, "indent": 1, "parameters": [2, 0, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 111, "indent": 1, "parameters": [1, 703, 0, 1, 0]}, {"code": 111, "indent": 2, "parameters": [12, "$bath_pri_kabe == 2"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 16, 2)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 703, 0, 2, 0]}, {"code": 111, "indent": 2, "parameters": [12, "$bath_pri_kabe >= 2"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 16, 3)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************"]}, {"code": 408, "indent": 0, "parameters": ["* 覗き穴イベント２の後"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 355, "indent": 0, "parameters": ["$bath_pri_kabe = 0"]}, {"code": 122, "indent": 0, "parameters": [703, 703, 0, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************"]}, {"code": 408, "indent": 0, "parameters": ["* 覗き穴イベント３の後"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 111, "indent": 0, "parameters": [12, "$bath_pri_kabe == 3"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 203, "indent": 1, "parameters": [32, 0, 15, 20, 4]}, {"code": 203, "indent": 1, "parameters": [33, 0, 15, 21, 4]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2-3"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [32, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [33, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map16_ev16_p3_101\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_map16_ev16_p3_102\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4-5"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_6-8"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 85]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$bath_pri_kabe = 0"]}, {"code": 122, "indent": 0, "parameters": [703, 703, 0, 0, 3]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 20}, {"id": 17, "name": "女湯の女客　（壁際）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 3}, {"id": 18, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 18}, {"id": 19, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 18}, {"id": 20, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 18}, {"id": 21, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 22, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 1}, {"id": 23, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 1}, {"id": 24, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 1}, {"id": 25, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 11}, {"id": 26, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 11}, {"id": 27, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 22, "y": 20}, {"id": 28, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 11}, {"id": 29, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 11}, {"id": 30, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 26}, {"id": 31, "name": "男湯側おっさんA", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [120], "indent": null}, {"code": 1, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 7}, {"id": 32, "name": "男湯側おっさんB", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": true, "switch2Id": 32, "switch2Valid": true, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameSwitches.value(643) === true && $gameSwitches.value(503) === false &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameSwitches.value(111) === true && $gameSwitches.value(32) === true &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(881) === 0\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["出現条件($game_switches[643] == true && $game_switches[503] == "]}, {"code": 408, "indent": 0, "parameters": ["false && $gameVariables.value(881) == 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male4", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 3}, {"id": 33, "name": "男湯側おっさんC", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male5", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 4}, {"id": 34, "name": "女湯の女客", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem3", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [180]}, {"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [180]}, {"code": 3, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 6}, {"id": 35, "name": "湯女", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem2", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 4"], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [93]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 122, "indent": 0, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 4, "$gameActors.actor(1).tp"]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 100, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_fatigue100\")"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 11, 0, 100, 1]}, {"code": 125, "indent": 2, "parameters": [1, 0, 100]}, {"code": 250, "indent": 2, "parameters": [{"name": "Coin", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 35, 2)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_need_money\")"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["湯女イベント１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 108, "indent": 0, "parameters": ["画像準備"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_999"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 108, "indent": 0, "parameters": ["手に汚れがびっしりついてる差分"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8-12"]}, {"code": 108, "indent": 0, "parameters": ["※チン皮に指入れる"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13-15"]}, {"code": 108, "indent": 0, "parameters": ["※困り眉に目をほそめ　ニヤつき口"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16-25"]}, {"code": 108, "indent": 0, "parameters": ["※フラッシュ　舌がアナルに触れる"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_26-33"]}, {"code": 108, "indent": 0, "parameters": ["※カットイン　ケツ穴めっちゃ舐め倒してるやつ"]}, {"code": 245, "indent": 0, "parameters": [{"name": "stroke_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_34-39"]}, {"code": 108, "indent": 0, "parameters": ["※エロ耐久判定"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 1, "parameters": ["★成功"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-103"]}, {"code": 108, "indent": 1, "parameters": ["※フラッシュ　ひょっとこヅラアナル舐め"]}, {"code": 245, "indent": 1, "parameters": [{"name": "stroke_slow_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_104-119"]}, {"code": 108, "indent": 1, "parameters": ["※フラッシュ　超大量射精"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_120"]}, {"code": 108, "indent": 1, "parameters": ["※湯女、口を離す。　口のまわりにはカス"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_121-124"]}, {"code": 108, "indent": 1, "parameters": ["※フラッシュ　再び　ひょっとこアナル舐め"]}, {"code": 245, "indent": 1, "parameters": [{"name": "stroke_fast_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_125-133"]}, {"code": 108, "indent": 1, "parameters": ["※射精ストップ、全身汁まみれ差分"]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 4]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_134-139"]}, {"code": 108, "indent": 1, "parameters": ["※潮吹き　フラッシュ"]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 231, "indent": 1, "parameters": [16, "event-bath_lady-rimjob-squirt", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_140-145"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 252]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["★失敗"]}, {"code": 108, "indent": 1, "parameters": ["※判定失敗　射精フラッシュ　並射精"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40-43"]}, {"code": 108, "indent": 1, "parameters": ["※差分　射精後"]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_44-54"]}, {"code": 108, "indent": 1, "parameters": ["※潮吹き"]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 12, 1)"]}, {"code": 231, "indent": 1, "parameters": [16, "event-bath_lady-rimjob-squirt", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_55-57"]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_58-62"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-100)"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 252]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 117, "indent": 1, "parameters": [955]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 232, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 6}, {"id": 36, "name": "風呂場のアイリス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 903, "variableValid": false, "variableValue": 99}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs_naked", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス　最悪だったクエスト"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["!!!ストーリー一時撤去のためv903 >= 99の条件は一時撤去"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 201, "indent": 0, "parameters": [0, 16, 3, 3, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 4, 3, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-9"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 148, 11, 7, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs_naked", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 4, 3, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-8"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [1002, 1002, 0]}, {"code": 201, "indent": 0, "parameters": [0, 16, 4, 6, 2, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1002, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 682, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 6}, {"id": 37, "name": "陰毛・腋毛", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"pub_hair\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"armpit_hair\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"on\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"off\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [35, 35, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 2, "parameters": [35, 35, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"on\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"off\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [36, 36, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 2, "parameters": [36, 36, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 6}, {"id": 38, "name": "ミルク購入", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 8, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 302, "indent": 0, "parameters": [0, 180, 0, 0, true]}, {"code": 605, "indent": 0, "parameters": [0, 181, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 182, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 183, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 6, "y": 20}, {"id": 39, "name": "ヤリゾー覗きイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー覗きイベント（１）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [32, 1, true]}, {"code": 205, "indent": 0, "parameters": [32, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 16, 39, 1, 10, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11"]}, {"code": 205, "indent": 0, "parameters": [32, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公ソロの場合はここで終了"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 111, 1]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭も風呂場にいる場合"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィック準備"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [113]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 16, 39, 1, 40, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-46"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 16, 39, 1, 50, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 16, 39, 1, 60, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_61"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [881, 881, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 0}, {"id": 40, "name": "ヤリゾー", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 32, "switch2Valid": true, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameSwitches.value(643) === true && $gameSwitches.value(503) === false &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameSwitches.value(32) === true && $gameVariables.value(882) === 0 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭の服に精子ぶっかけるイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 355, "indent": 0, "parameters": ["$yaba12 = 1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 24}, {"id": 41, "name": "ヤリゾー風呂場イベのやつ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロのとき入浴前にヤリゾーに話しかけていると"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーが司祭の服に精子をかけるイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["if(typeof $yaba12 === \"undefined\"){"]}, {"code": 655, "indent": 0, "parameters": ["$yaba12 = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [12, "$yaba12 == 1"]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 203, "indent": 1, "parameters": [40, 0, 4, 16, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 8, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 8, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾー登場"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20-22"]}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭の服のところへ"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 205, "indent": 1, "parameters": [40, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 8, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 8, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["臭い嗅ぐ"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40-48"]}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["しゃせい"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾー退出して司祭出てくる"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 203, "indent": 1, "parameters": [40, 0, 1, 24, 6]}, {"code": 205, "indent": 1, "parameters": [40, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_60-62"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 1, "parameters": ["************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了"]}, {"code": 408, "indent": 1, "parameters": ["*************************************************************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$yaba12 = 0"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [882, 882, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [97]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 15}, {"id": 42, "name": "PMイベント-浴場", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 543, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 225, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 42, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 543, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 8, 3, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1646, 1646, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [32, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [33, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["本編"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 16, 42, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 16, 42, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["覗く"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 28)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-14"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["覗かない"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セクション２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [0, 0, 5, 23, 6]}, {"code": 201, "indent": 0, "parameters": [0, 16, 6, 23, 4, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 16, 42, 2, 60, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 16, 42, 2, 70, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 16, 42, 2, 80, 0)"]}, {"code": 111, "indent": 0, "parameters": [1, 11, 0, 2, 1]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_51-52"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[73]>=1)", "\\v[63] if(v[74]>=1)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["良いお湯だった"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_61"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[73]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["アイス食べる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_71"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 125, "indent": 1, "parameters": [1, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 10]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 4)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_72-73"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] if(v[74]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["お風呂場で一体何をしていたんだ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81-82"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-50)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["ページ１に終了処理置いた場合、場所移動かけると正常に動作しないの"]}, {"code": 408, "indent": 0, "parameters": ["で各ページに置いてる"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 115, "volume": 100}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [202, 12, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [1646, 1646, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [543, 543, 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 201, "indent": 0, "parameters": [0, 10, 10, 17, 2, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 34, "y": 0}, null, null, null, null]}