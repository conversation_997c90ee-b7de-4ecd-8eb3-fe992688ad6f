{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 30, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "キャンプ - 会話(司祭と二人きり）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["* キャンプ通常会話（司祭と二人きり）"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 118, "indent": 0, "parameters": ["ダイス"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 0, "parameters": ["id = $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(61, 1, id)"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント１"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント２"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント３"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map61_ev1_p4_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map61_ev1_p4_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map61_ev1_p4_2a\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_map61_ev1_p4_2b\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map61_ev1_p4_3a\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_5"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_6"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], 0, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_7"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_8"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_9"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_12"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "キャンプ - 会話(ヤリゾーもいる）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["* キャンプ会話　- ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": ["*******************************"]}, {"code": 118, "indent": 0, "parameters": ["ダイス"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 0, "parameters": ["id = $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(61, 2, id)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント１"]}, {"code": 108, "indent": 0, "parameters": ["・マップイベント呼び出しを用いて呼び出したイベントに連番メッセー"]}, {"code": 408, "indent": 0, "parameters": ["ジ表示がある場合、"]}, {"code": 408, "indent": 0, "parameters": ["そのイベントを呼び出した回数だけメッセージがループで表示される。"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["そのため一旦各個表示にしている。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント２"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 111, "indent": 0, "parameters": [1, 82, 1, 98, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["イベント３"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* ヤリゾーとセックスしてる場合追加でイベント"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 1, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["* ヤリゾーに完全に寝取られてるかどうかでセリフが変わる。"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 10, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_7"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_5"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_6"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "ポーションメーカー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* キャンプ会話　- ポーションメーカー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["ダイス"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 4]}, {"code": 355, "indent": 0, "parameters": ["id = self.event_id"]}, {"code": 655, "indent": 0, "parameters": ["page = $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(61, id, page)"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* 司祭がイチャイチャを見せつける"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 3, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 3, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-13"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* 会話２　なんで錬金術師に"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* 会話３"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 3, 4, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 3, 4, 30, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[183]>=100)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[183]>=100)"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-33"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* 会話４"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 3, 5, 10, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "会話", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "会話", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "会話", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["* キャンプ会話　- 吸血姫"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["ダイス"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 355, "indent": 0, "parameters": ["id = self.event_id"]}, {"code": 655, "indent": 0, "parameters": ["page = $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(61, id, page)"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 254]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["会話１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 6, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 6, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 245, "indent": 1, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [69]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-13"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["会話2"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 6, 3, 10, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 1}, {"id": 7, "name": "会話", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "会話", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 9, "name": "イチャイチャ-<PERSON><PERSON><PERSON>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$prohero_sleep", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["未実装"]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["Not yet implemented"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 10, "name": "イチャイチャ-Fore play", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$prohero_sleep", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 118, "indent": 0, "parameters": ["選択"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"please_him\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"please_her\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] if(v[1]==99999)", "\\v[80]"], 2, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["********************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭が奉仕"]}, {"code": 408, "indent": 1, "parameters": ["********************************************"]}, {"code": 118, "indent": 1, "parameters": ["司祭が奉仕"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"intimacy_handjob\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 2, "parameters": ["id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(61, id, 2)"]}, {"code": 122, "indent": 2, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[80]"]}, {"code": 119, "indent": 2, "parameters": ["選択"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] if(v[1]==99999)"]}, {"code": 108, "indent": 1, "parameters": ["********************************************"]}, {"code": 408, "indent": 1, "parameters": ["主人公が奉仕"]}, {"code": 408, "indent": 1, "parameters": ["********************************************"]}, {"code": 118, "indent": 1, "parameters": ["主人公が奉仕"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["未実装"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["* 手コキ"]}, {"code": 408, "indent": 0, "parameters": ["***************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [160]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-21"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22-26"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}_penis1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(44),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(45),$s_w,$s_h,255,0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_27-29"]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"intimacy_handjob_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"intimacy_handjob_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["$no = `event-handjob1-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(44),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(45),$s_w,$s_h,255,0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-105"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-203"]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [221]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_204"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["$no = `event-handjob1-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(44),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(45),$s_w,$s_h,255,0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_205-206"]}, {"code": 117, "indent": 1, "parameters": [212]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_207-211"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,-10)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 11, "name": "イチャイチャ-Sex", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$prohero_sleep", "direction": 4, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"sex_tetsunagikkusu\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"sex_ketsuhojikkusu\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, id, 2)"]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 355, "indent": 1, "parameters": ["id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, id, 3)"]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************"]}, {"code": 408, "indent": 0, "parameters": ["* 手つなぎックス"]}, {"code": 408, "indent": 0, "parameters": ["**********************"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"sex_tetsunagikkusu_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_end\")"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-16"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17-33"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 231, "indent": 0, "parameters": [9, "event-tetsunagikkusu-semen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_34-37"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_38-41"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] if(s[33])", "\\v[62]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] if(s[33])"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "piss_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 231, "indent": 1, "parameters": [8, "event-tetsunagikkusu-piss", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_42-49"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-51"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_52-53"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_54"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ケツほじセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [184]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 11, 3, 30, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 11, 3, 40, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-16"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-32"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 120, "volume": 80}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [184]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41-42"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツ穴とまんこから潮噴き"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-53"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スパンキング"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 120, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [184]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-62"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スパンキング"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 120, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-71"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スパンキング"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 120, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-83"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スパンキング"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow8", "pan": 0, "pitch": 120, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-94"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [184]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 12, "name": "イチャイチャ（従者ありの時）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカーの目の前でセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["*************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 243, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 24)"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 244, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 3}, {"id": 13, "name": "見張りが居ない時のイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$prohero_sleep", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["見張りを立てていない時 - 司祭が主人公を逆レイプ（Horny100)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [130]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$prot_sleep", 0]}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["逆レ〇プシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 108, "indent": 0, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 108, "indent": 0, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ベロチューカットイン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90"]}, {"code": 108, "indent": 0, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110"]}, {"code": 108, "indent": 0, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120"]}, {"code": 108, "indent": 0, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["排卵カットイン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-142"]}, {"code": 122, "indent": 0, "parameters": [52, 52, 0, 0, 4]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["腰アゲ"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [186]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["腰サゲ"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [186]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [52, 52, 2, 0, 1]}, {"code": 111, "indent": 1, "parameters": [1, 52, 0, 0, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-153"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精アクメ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [186]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-166"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 13, 1, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 13, 1, 300, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["起きる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-202"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["カットイン　プロテクション破壊　卵子に精子群がる"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-213"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["Zzz..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_310"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 5}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 3}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 3}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 3}, {"id": 17, "name": "キャンプ-寝取らせイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["キャンプ　寝取らせ　ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 17, 1, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 17, 1, 11, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 61, 17, 1, 12, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[204]>=3)", "\\v[62] en(v[204]>=3)", "\\v[63] en(v[204]>=4)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[204]>=3)"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 17, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[204]>=3)"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 17, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] en(v[204]>=4)"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 17, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["腕つかみ立ちバック"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 12]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map61_ev17_p1_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map61_ev17_p1_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5-14"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_999"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15-23"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_24-30"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 355, "indent": 0, "parameters": ["$womb = 1"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-51"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_52-61"]}, {"code": 355, "indent": 0, "parameters": ["$womb = 2"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_62-67"]}, {"code": 355, "indent": 0, "parameters": ["$womb = 3"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_68-76"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_77"]}, {"code": 355, "indent": 0, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 0"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_78"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_79-80"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 12]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_81-82"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map61_ev17_p1_2a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map61_ev17_p1_2b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_83-85"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 16]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_86"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_87"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [138, 138, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ケツ肉ぶいんドギー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["序幕"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 17, 3, 40, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 17, 3, 50, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-20"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_21-31"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["アクメしろ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_42-43"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["アクメするな"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51-57"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_60-64"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー消える"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["精子逆噴射"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終幕"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [195]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 17, 3, 130, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_131-132"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["精子逆噴射"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [138, 138, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["目隠しフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["イベントＩＤ４５に移動"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 8)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4-14"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 129, 0]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 8)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-39"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーをしゃぶる"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 8)"]}, {"code": 111, "indent": 0, "parameters": [0, 129, 1]}, {"code": 108, "indent": 1, "parameters": ["主人公のエロレベルが低い"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-106"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 8)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110-114"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_115-117"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["主人公のエロレベルが高い"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-206"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 8)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-211"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-222"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ステータス処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 111, "indent": 1, "parameters": [0, 129, 0]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 111, "indent": 1, "parameters": [0, 129, 0]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [138, 138, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 22}, {"id": 18, "name": "[見張り開始時イベント]主人公", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 108, "indent": 1, "parameters": ["絆レベル１のときは強制的にヤリゾーがガードに。"]}, {"code": 408, "indent": 1, "parameters": ["コモンから直接呼び出してるからここでの記述は無し"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 18, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが絆レベル2以下の場合は司祭はヤリゾーがテント内で一緒に寝"]}, {"code": 408, "indent": 1, "parameters": ["ることを拒否する。"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 18, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが絆レベル3のときはテント内で寝ることを承諾。"]}, {"code": 408, "indent": 1, "parameters": ["（奥さん、肩こってるでしょう。寝る前にマッサージさせていただきや"]}, {"code": 408, "indent": 1, "parameters": ["すよ　……わかりました　って感じ）"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 3, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 18, 4)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが絆レベル４の時はテント内で寝るように誘う。"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 4, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 18, 5)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが絆レベル５のときは主人公を強制的にガードにして"]}, {"code": 408, "indent": 1, "parameters": ["二人で仲良くテント内へといく"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 5, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 18, 6)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["▽封印▽"]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 99999999, 0]}, {"code": 355, "indent": 2, "parameters": ["x = $gameVariables.value(204)+1"]}, {"code": 655, "indent": 2, "parameters": ["x = 2 if x == 1"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(61, 18, x)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["△封印△"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 絆レベル１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [137, 137, 1]}, {"code": 122, "indent": 0, "parameters": [282, 282, 0, 1, 8]}, {"code": 122, "indent": 0, "parameters": [284, 284, 0, 0, 10]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 絆レベル２"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-8"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [137, 137, 1]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 絆レベル３"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 690, 1]}, {"code": 108, "indent": 1, "parameters": ["1回目"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 18, 4, 10, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_11-14"]}, {"code": 121, "indent": 2, "parameters": [690, 690, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["2回目以降"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-101"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [137, 137, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 絆レベル４"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [137, 137, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー 絆レベル５"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 117, "indent": 1, "parameters": [61]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [137, 137, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 5}, {"id": 19, "name": "[見張り開始時イベント]司祭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 19, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 19, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーが居る場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ＰＭが居る場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 19, 3, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 19, 3, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 5}, {"id": 20, "name": "[見張り開始時イベント]従者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 5}, {"id": 21, "name": "主人公見張り時-ヤリゾーイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 137, 0]}, {"code": 111, "indent": 2, "parameters": [0, 32, 0]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["イベントテストモードの処理"]}, {"code": 111, "indent": 3, "parameters": [0, 36, 0]}, {"code": 117, "indent": 4, "parameters": [993]}, {"code": 102, "indent": 4, "parameters": [["Tent event(tDT event)", "Normal"], -1, 0, 2, 0]}, {"code": 402, "indent": 4, "parameters": [0, "Tent event(tDT event)"]}, {"code": 119, "indent": 5, "parameters": ["テントから物音"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "Normal"]}, {"code": 119, "indent": 5, "parameters": ["通常イベント"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["イベントテストモードの処理ここまで"]}, {"code": 408, "indent": 3, "parameters": ["************************************"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["本処理"]}, {"code": 408, "indent": 3, "parameters": ["*************************************"]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 10, 0]}, {"code": 108, "indent": 4, "parameters": ["テントから物音イベント"]}, {"code": 118, "indent": 4, "parameters": ["テントから物音"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 21, 2)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["通常キャンプ内イベント"]}, {"code": 408, "indent": 4, "parameters": ["（発生条件は後で要調整）"]}, {"code": 118, "indent": 4, "parameters": ["通常イベント"]}, {"code": 355, "indent": 4, "parameters": ["console.log(\"主人公見張り時通常イベント\")"]}, {"code": 108, "indent": 4, "parameters": ["**************************************************"]}, {"code": 408, "indent": 4, "parameters": ["Lvl2"]}, {"code": 408, "indent": 4, "parameters": ["**************************************************"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 2, 0]}, {"code": 355, "indent": 5, "parameters": ["console.log(\"ヤリゾー絆Lvl2\")"]}, {"code": 108, "indent": 5, "parameters": ["ケツコキするイベント。"]}, {"code": 408, "indent": 5, "parameters": ["没にするかも。"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(61, 21, 3)"]}, {"code": 119, "indent": 5, "parameters": ["終了"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["**************************************************"]}, {"code": 408, "indent": 4, "parameters": ["Lvl3"]}, {"code": 408, "indent": 4, "parameters": ["**************************************************"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 3, 0]}, {"code": 355, "indent": 5, "parameters": ["var_from_sheet(61, 61, 21, 1, 31, 0)"]}, {"code": 655, "indent": 5, "parameters": ["var_from_sheet(62, 61, 21, 1, 32, 0)"]}, {"code": 655, "indent": 5, "parameters": ["var_from_sheet(63, 61, 21, 1, 33, 0)"]}, {"code": 102, "indent": 5, "parameters": [["\\v[61]", "\\v[62]", "\\v[63] en(s[138])"], -1, 0, 2, 0]}, {"code": 402, "indent": 5, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 6, "parameters": ["セックスイベント１"]}, {"code": 355, "indent": 6, "parameters": ["MapEvent.call(61, 21, 4)"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 6, "parameters": ["イラマチオ"]}, {"code": 355, "indent": 6, "parameters": ["MapEvent.call(61, 21, 5)"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [2, "\\v[63] en(s[138])"]}, {"code": 108, "indent": 6, "parameters": ["セックスイベント　シルエットアニメ"]}, {"code": 355, "indent": 6, "parameters": ["MapEvent.call(61, 21, 6)"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 102, "indent": 5, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 5, "parameters": [0, "\\v[80]"]}, {"code": 119, "indent": 6, "parameters": ["終了"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 119, "indent": 5, "parameters": ["終了"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["**************************************************"]}, {"code": 408, "indent": 4, "parameters": ["Lvl4"]}, {"code": 408, "indent": 4, "parameters": ["**************************************************"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 4, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["**************************************************"]}, {"code": 408, "indent": 4, "parameters": ["Lvl5"]}, {"code": 408, "indent": 4, "parameters": ["**************************************************"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 5, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["テントから物音"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_common476_10_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_common476_10_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [4, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["変数：ボディポーズで司祭が顔出してるかどうかを判定してる"]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [105]}, {"code": 231, "indent": 1, "parameters": [15, "event-0-hand", 0, 0, -80, 320, 100, 100, 255, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "heart", "pan": 0, "pitch": 115, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [15, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_common476_10_2a\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_common476_10_2b\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [105]}, {"code": 246, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_common476_10_3a\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_common476_10_3b\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3-5"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_6-7"]}, {"code": 108, "indent": 3, "parameters": ["******************************"]}, {"code": 408, "indent": 3, "parameters": ["* ヤリゾーとの進行度が最大だと寝取られイベント"]}, {"code": 408, "indent": 3, "parameters": ["******************************"]}, {"code": 111, "indent": 3, "parameters": [0, 32, 0]}, {"code": 108, "indent": 4, "parameters": ["************************************"]}, {"code": 408, "indent": 4, "parameters": ["イベントテストモードの処理"]}, {"code": 111, "indent": 4, "parameters": [0, 36, 0]}, {"code": 117, "indent": 5, "parameters": [993]}, {"code": 102, "indent": 5, "parameters": [["Netorare", "Cake"], -1, 0, 2, 0]}, {"code": 402, "indent": 5, "parameters": [0, "Netorare"]}, {"code": 119, "indent": 6, "parameters": ["寝取られ"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [1, "Cake"]}, {"code": 119, "indent": 6, "parameters": ["ケーキ作り"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["イベントテストモードの処理ここまで"]}, {"code": 408, "indent": 4, "parameters": ["************************************"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 4, 1]}, {"code": 118, "indent": 5, "parameters": ["寝取られ"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_100"]}, {"code": 224, "indent": 5, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 5, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 5, "parameters": [105]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_101-103"]}, {"code": 224, "indent": 5, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 5, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 5, "parameters": [58]}, {"code": 230, "indent": 5, "parameters": [60]}, {"code": 213, "indent": 5, "parameters": [4, 8, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_104"]}, {"code": 355, "indent": 5, "parameters": ["$tent_sound_event = 2"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 118, "indent": 5, "parameters": ["ケーキ作り"]}, {"code": 108, "indent": 5, "parameters": ["******************************"]}, {"code": 408, "indent": 5, "parameters": ["* それ以外の場合はヤリゾーとケーキ作ってる"]}, {"code": 408, "indent": 5, "parameters": ["******************************"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_200-202"]}, {"code": 224, "indent": 5, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 5, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 5, "parameters": [58]}, {"code": 230, "indent": 5, "parameters": [60]}, {"code": 213, "indent": 5, "parameters": [4, 8, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_203"]}, {"code": 355, "indent": 5, "parameters": ["$tent_sound_event = 1"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["ケーキ作り"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_8"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ61 イベント21 ページ３開始\")"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル２　キャンプ　寝込み遅いケツコキ"]}, {"code": 408, "indent": 0, "parameters": ["(仕様変更に伴って封印されしイベントとなる）"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"尻\""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 111, "indent": 1, "parameters": [1, 863, 0, 1, 1]}, {"code": 117, "indent": 2, "parameters": [995]}, {"code": 111, "indent": 2, "parameters": [0, 26, 0]}, {"code": 117, "indent": 3, "parameters": [29]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 8, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["開始"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー登場"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 8, 1)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 8, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-12"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツこき開始"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 8, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-41"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_extreme", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 8, 1)"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-111"]}, {"code": 108, "indent": 0, "parameters": ["！テキスト追加必要！"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["21... チャプター2-1"]}, {"code": 122, "indent": 1, "parameters": [863, 863, 0, 0, 21]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ61 イベント21 ページ３終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル３　キャンプ　テント内セックスその１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 111, "indent": 0, "parameters": [1, 863, 0, 31, 1]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"event_1st\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"event_2nd\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 2, "parameters": ["1回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["2回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン（1回目）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["1回目"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["ちんぽを腹の上に載せる"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-25"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["挿入"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-35"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-54"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_55"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-65"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-86"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-104"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-113"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公のシーンへ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["テント画像"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 50}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-141"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["テント内セックスシーンへ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 5)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 5)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_151-153"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 5)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_161-167"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["キスカットイン"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_170-171"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["子宮突きあげ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["テント顔出しあへ顔"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["テント画像"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 50}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-204"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["テント内セックスシーンへ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 5)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_210-212"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン（2回目以降）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["2回目"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2001-2003"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["31... チャプター3-1"]}, {"code": 122, "indent": 1, "parameters": [863, 863, 0, 0, 31]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル３　キャンプ　イラマチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 241, "indent": 0, "parameters": [{"name": "Night", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 204, "indent": 0, "parameters": [8, 4, 4]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["序幕"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-8"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 7)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 85}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-40"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["乳ひっぱり"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ 70-75"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting5", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-75"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-75"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["テントシーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-93"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["テント締まる"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [105]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["32... チャプター3-2"]}, {"code": 111, "indent": 1, "parameters": [1, 863, 0, 32, 4]}, {"code": 122, "indent": 2, "parameters": [863, 863, 0, 0, 32]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル３　キャンプ　テント内シルエットセックス（アニメ）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 241, "indent": 0, "parameters": [{"name": "Night", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [930]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 213, "indent": 0, "parameters": [-1, 10, true]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 204, "indent": 0, "parameters": [8, 4, 4]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["序幕"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 85}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [27, 27, 0]}, {"code": 122, "indent": 0, "parameters": [174, 174, 0, 0, 21]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["文章表示"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 0, 1001]}, {"code": 117, "indent": 0, "parameters": [930]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 121, "indent": 0, "parameters": [27, 27, 1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["32... チャプター3-3"]}, {"code": 111, "indent": 1, "parameters": [1, 863, 0, 33, 4]}, {"code": 122, "indent": 2, "parameters": [863, 863, 0, 0, 33]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取られ　Lvl4 スカートの垂れ幕で結合部が隠れたセックス　キャンプ　主人公が見張り"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 21"]}, {"code": 655, "indent": 0, "parameters": ["$page = 7"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※導入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　寝転ぶヤリゾーの上にまたがりマッサージ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　ちんぽはまだ未挿入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※素股　ＳＥ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　挿入　ＳＥくちゅ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-35"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　腰スウィング"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※主人公がテントから出てくる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　寝転ぶヤリゾーの上にまたがりマッサージ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　挿入中　結合部がスカートで隠れてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-57"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※表情　んお顔"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-68"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 7}, {"id": 22, "name": "主人公見張り時-バーバリアンイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 7}, {"id": 23, "name": "主人公見張り時-PMイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 225, 0, 5, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 23, 6)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 23, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["足を向けるな　レベル４"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["テントから足出してるＰＭ　レベル５"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["ここにグラフィック"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 36)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["選択肢"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 23, 6, 20, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 23, 6, 30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 36)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-53"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["ちんぽシコシコ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_soft", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-66"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーズ変更"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 36)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["足オナホにちんぽ挿入"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 36)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-89"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 36)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン終了"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_103-108"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 7}, {"id": 24, "name": "主人公見張り時-プッシーキャット", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 7}, {"id": 25, "name": "主人公見張り時-吸血姫", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 234, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 25, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["Lvl2 ガールズトーク"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-8"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 7}, {"id": 26, "name": "主人公見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 7}, {"id": 27, "name": "主人公見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 7}, {"id": 28, "name": "主人公見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 7}, {"id": 29, "name": "司祭見張り時-ヤリゾーイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー　Lvl3 イベント"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [0, 36, 0]}, {"code": 117, "indent": 3, "parameters": [993]}, {"code": 102, "indent": 3, "parameters": [["Lvl3", "Lvl4+"], 1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "Lvl3"]}, {"code": 119, "indent": 4, "parameters": ["Lvl3"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "Lvl4+"]}, {"code": 119, "indent": 4, "parameters": ["Lvl4"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 102, "indent": 3, "parameters": [["\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["*************************************"]}, {"code": 408, "indent": 3, "parameters": ["Lvl3"]}, {"code": 408, "indent": 3, "parameters": ["*************************************"]}, {"code": 111, "indent": 3, "parameters": [1, 204, 0, 3, 0]}, {"code": 118, "indent": 4, "parameters": ["Lvl3"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 29, 4)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["*************************************"]}, {"code": 408, "indent": 3, "parameters": ["Lvl4"]}, {"code": 408, "indent": 3, "parameters": ["*************************************"]}, {"code": 111, "indent": 3, "parameters": [1, 204, 0, 4, 1]}, {"code": 118, "indent": 4, "parameters": ["Lvl4"]}, {"code": 102, "indent": 4, "parameters": [["1", "2"], 1, 0, 2, 0]}, {"code": 402, "indent": 4, "parameters": [0, "1"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(61, 29, 5)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "2"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(61, 29, 6)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル３　キャンプ　司祭見張り時　イベント１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 111, "indent": 0, "parameters": [1, 863, 0, 31, 1]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"event_1st\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"event_2nd\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 2, "parameters": ["1回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["2回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["シーン（1回目）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["1回目"]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シコりだす"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-22"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["顔射"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 7]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [150]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-38"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["シーン（2回目）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["2回目"]}, {"code": 108, "indent": 0, "parameters": ["グラフィック"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2001-2004"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["31... チャプター3-1"]}, {"code": 122, "indent": 1, "parameters": [864, 864, 0, 0, 31]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["（ヤリゾー）"]}, {"code": 408, "indent": 0, "parameters": ["レベル４＆５　キャンプ　司祭見張り時　テントからちんぽルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "piss_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["Lvl4", "Lvl5"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Lvl4"]}, {"code": 119, "indent": 2, "parameters": ["Lvl4"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Lvl5"]}, {"code": 119, "indent": 2, "parameters": ["Lvl5"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル４"]}, {"code": 408, "indent": 0, "parameters": ["***************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl4"]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-202"]}, {"code": 108, "indent": 1, "parameters": ["手コキシーン"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-218"]}, {"code": 108, "indent": 1, "parameters": ["※ちん皮むき　カスびっしり"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-229"]}, {"code": 108, "indent": 1, "parameters": ["※フェラチオシーン"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_long", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_250-268"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 1, "parameters": ["※射精"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_280-290"]}, {"code": 108, "indent": 1, "parameters": ["スカトロＯＮの場合おしっこ"]}, {"code": 111, "indent": 1, "parameters": [0, 33, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_291"]}, {"code": 250, "indent": 2, "parameters": [{"name": "piss_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_292-296"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************"]}, {"code": 408, "indent": 0, "parameters": ["レベル５"]}, {"code": 408, "indent": 0, "parameters": ["***************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl5"]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300"]}, {"code": 108, "indent": 1, "parameters": ["※手コキシーン"]}, {"code": 108, "indent": 1, "parameters": ["手コキシーン"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_310-312"]}, {"code": 108, "indent": 1, "parameters": ["※フェラチオシーン"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_long", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_320-327"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 1, "parameters": ["※射精"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 27, 1)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_330-333"]}, {"code": 108, "indent": 1, "parameters": ["※セックスシーン"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 29, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_H_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_340-345"]}, {"code": 108, "indent": 1, "parameters": ["※フェードアウト　テントシーン"]}, {"code": 122, "indent": 1, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 29, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_370-372"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_373"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_374-376"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_377"]}, {"code": 108, "indent": 1, "parameters": ["※セックスシーン"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 29, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_380-382"]}, {"code": 108, "indent": 1, "parameters": ["※射精"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [1619, 1619, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 29, 1)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_383-384"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["31... チャプター3-1"]}, {"code": 122, "indent": 1, "parameters": [864, 864, 0, 0, 41]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取られ　Lvl4 ちん嗅ぎ　鼻コキ　（グラフィック　Ａキーリユーズ）　キャンプ　司祭が見張り"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 29"]}, {"code": 655, "indent": 0, "parameters": ["$page = 6"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※導入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-9"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン１　ヤリゾーのちんぽを鼻に押し付けられる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [176]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "volume": 100, "pitch": 70, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-21"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　司祭アヘ顔　鼻にちんぽ擦り付ける　泡"]}, {"code": 117, "indent": 0, "parameters": [176]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-36"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン２　射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [176]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-44"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン３　おしっこ（スカトロがＯＮのときのみ）"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 33, 0]}, {"code": 122, "indent": 1, "parameters": [1616, 1616, 0, 0, 211]}, {"code": 117, "indent": 1, "parameters": [176]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!piss_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51-53"]}, {"code": 108, "indent": 1, "parameters": ["※シーン３　ここまで"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-61"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 9}, {"id": 30, "name": "司祭見張り時-バーバリアン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["バーバリアンが従者として同行中"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 9}, {"id": 31, "name": "司祭見張り時-PMイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート(司祭見張り時、ＰＭと主人公がテントで休む）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公-PMと何かする（選択肢）"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 31, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 31, 1, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 61, 31, 1, 30, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(64, 61, 31, 1, 40, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(70, 61, 31, 1, 999, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] if(s[1132])", "\\v[62] if(s[1133])", "\\v[63] if(s[1135])"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] if(s[1132])"]}, {"code": 108, "indent": 1, "parameters": ["足コキ"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, 31, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] if(s[1133])"]}, {"code": 108, "indent": 1, "parameters": ["フェラチオ"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, 31, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] if(s[1135])"]}, {"code": 108, "indent": 1, "parameters": ["アナルセックス"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, 31, 5)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[64] if(v[225]>=5)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[64] if(v[225]>=5)"]}, {"code": 108, "indent": 1, "parameters": ["フェラチオアニメーション"]}, {"code": 355, "indent": 1, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61, 31, 6)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[70]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[70]"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["睡眠"]}, {"code": 108, "indent": 1, "parameters": ["絆レベルが５の場合、司祭フェラチオＰＭアナル舐めのイベントチャンス"]}, {"code": 111, "indent": 1, "parameters": [1, 225, 0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 2, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 1, 0]}, {"code": 108, "indent": 2, "parameters": ["抱き着きねんね"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 2, 2]}, {"code": 355, "indent": 2, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(61, 31, n)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["司祭テントフェラ　＋　ＰＭアナル舐め"]}, {"code": 355, "indent": 2, "parameters": ["n = $gameVariables.value(20)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(61, 43, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PMが主人公に抱き着きねんね"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 15)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 31, 2, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 31, 2, 200, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7-12"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["PMを起こす"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-106"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 15)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_108-111"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(116, 5, 15)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_113-114"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["このまま眠る"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-206"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_300-302"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["足コキ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-7"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["その時司祭は"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61, 40, 1)"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["フェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4-6"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["クリックループ"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 18)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["その時司祭は"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61, 40, 1)"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アナルセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116,5,22)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-7"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭のシーン"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 45}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11-13"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭テントに移動"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカーが顔を出す"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116,5,20)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカー引っ込む"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116,5,20)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカーもう一回顔を出す"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116,5,20)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-48"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["立ち絵セックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70,11,1)"]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214,10,6)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-103"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["エロ耐久判定"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["ゲームオーバー"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["******************************************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(214,10,6)"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70,11,1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_120-123"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 27]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70,11,1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_124-125"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ぐしゃあ"]}, {"code": 408, "indent": 1, "parameters": ["******************************************************"]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 231, "indent": 1, "parameters": [1, "blood", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_130-132"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_140"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_150-152"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 125, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_160-168"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["交渉判定"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"koushou\""]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-205"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(214,10,6)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-212"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["ゲームオーバー"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ＰＭ　シルエットフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [930]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [174, 174, 0, 0, 8]}, {"code": 122, "indent": 0, "parameters": [175, 175, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [27, 27, 0]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 121, "indent": 0, "parameters": [27, 27, 1]}, {"code": 122, "indent": 0, "parameters": [175, 175, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 355, "indent": 0, "parameters": ["$sp = screen.pictures[15]"]}, {"code": 655, "indent": 0, "parameters": ["i = 0;"]}, {"code": 655, "indent": 0, "parameters": ["while(i < 23){"]}, {"code": 655, "indent": 0, "parameters": ["var name = `animation/pm-sillhouette_bj/event-animation-pm-sillhouette_bj-${i}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$sp.show(name, 0, 0, 0, 50, 50, 0, 0, 0);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["i++;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 117, "indent": 0, "parameters": [740]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-103"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 0, 1002]}, {"code": 117, "indent": 0, "parameters": [930]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 121, "indent": 0, "parameters": [27, 27, 1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 9}, {"id": 32, "name": "司祭見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 9}, {"id": 33, "name": "司祭見張り時-吸血姫", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 234, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 33, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭見張り　絆レベル２　吸いたい"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 33, 2, 2, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 33, 2, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 33, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-4"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 1, "parameters": ["フェラチオシーン"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20-22"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_23"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェラチオシーン"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 6)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フェラチオ開始"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-42"]}, {"code": 108, "indent": 0, "parameters": ["ダブルピース　ステージ１"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-54"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-63"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 254]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 0, "parameters": [60, 60, 1, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 9}, {"id": 34, "name": "司祭見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 9}, {"id": 35, "name": "司祭見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 9}, {"id": 36, "name": "司祭見張り時-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 9}, {"id": 37, "name": "ヤリゾー見張り", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 21, 0]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 108, "indent": 2, "parameters": ["******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー　Lvl2以下 イベント"]}, {"code": 408, "indent": 2, "parameters": ["******************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 2, 2]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 4, "parameters": ["シーフが来る"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 3)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["イチャイチャ"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー　Lvl3 イベント"]}, {"code": 408, "indent": 2, "parameters": ["******************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 3, 0]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 4, "parameters": ["通常？イベント"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 4)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["tDTにあったイベント"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 6)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー　Lvl4 イベント"]}, {"code": 408, "indent": 2, "parameters": ["******************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 4, 0]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 4, "parameters": ["通常？イベント"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 5)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["tDTにあったイベント"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(61, 37, 6)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["寝取られがオフなら強制でイベント１"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 37, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー　レベル１＆２　主人公と司祭がめっちゃセックスしてる"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [488]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー　レベル２　シーフが来る！"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [488]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー　レベル３　ヤリゾー見張り時　１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 111, "indent": 0, "parameters": [1, 863, 0, 31, 1]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"event_1st\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"event_2nd\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 2, "parameters": ["1回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["2回目"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["シーン（1回目）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["1回目"]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーントイレシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー登場"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11-15"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["手マン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16-20"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["高速手マン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_21-24"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["潮噴き"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_25-29"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アナルもほじる"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-40"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-56"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アクメ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-63"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ぐでぇ"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [195]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-103"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["シーン（2回目）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["2回目"]}, {"code": 108, "indent": 0, "parameters": ["グラフィック"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 15, 4)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2001-2003"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 108, "indent": 1, "parameters": ["31... チャプター3-1"]}, {"code": 122, "indent": 1, "parameters": [865, 865, 0, 0, 31]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取られ　Lvl4 スカートの垂れ幕で結合部が隠れたセックス　キャンプ　yarizoが見張り"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 21"]}, {"code": 655, "indent": 0, "parameters": ["$page = 7"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※導入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　寝転ぶヤリゾーの上にまたがりマッサージ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　ちんぽはまだ未挿入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※素股　ＳＥ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　挿入　ＳＥくちゅ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!kuchu1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-35"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　腰スウィング"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※主人公がテントから出てくる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　寝転ぶヤリゾーの上にまたがりマッサージ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　挿入中　結合部がスカートで隠れてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-57"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※表情　んお顔"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109,16,2)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-68"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["絆レベル３+？　司祭がテントから抜け出てきてちんぽしゃぶる（寝取られてる場合）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [488]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 11}, {"id": 38, "name": "バーバリアン見張り", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "berserker_fem", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 11}, {"id": 39, "name": "ポーションメーカー見張り", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61, 39, 2)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - <PERSON>ももコき"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 25)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 39, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 39, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント開始"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 25)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-123"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 25)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-155"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 11}, {"id": 40, "name": "その時司祭は", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 45}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [4, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 40, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 40, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカー"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 3, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 12}, {"id": 41, "name": "保険", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["主人公見張り開始時　旧ヤリゾーレベル２"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 18, 3, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 61, 18, 3, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが司祭と同じテントで寝る事を許可"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 121, "indent": 1, "parameters": [137, 137, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーが司祭と同じテントで寝る事を拒否"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21-22"]}, {"code": 121, "indent": 1, "parameters": [137, 137, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 12}, {"id": 42, "name": "アニメーションリスト", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 27, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 12}, {"id": 43, "name": "司祭見張り時-従者無し", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["1"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "1"]}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 2, "parameters": ["尿意"]}, {"code": 118, "indent": 2, "parameters": ["1"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(61, 43, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["主人公がテントからちんぽを出す"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 61, 43, 2, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 61, 43, 2, 20, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 61, 43, 2, 30, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] if(v[1]>=999)", "\\v[62]", "\\v[63]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] if(v[1]>=999)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["テントからちんぽだけ出してトイレする"]}, {"code": 408, "indent": 1, "parameters": ["******************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!piss_slow", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-202"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_203-204"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [1620, 1620, 0, 0, 150]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138,1,1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-217"]}, {"code": 111, "indent": 1, "parameters": [1, 8, 0, 10, 0]}, {"code": 111, "indent": 2, "parameters": [1, 225, 0, 5, 1]}, {"code": 250, "indent": 3, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 3, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(138,1,1)"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_220-233"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["射精"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138,1,1)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_240-241"]}, {"code": 111, "indent": 1, "parameters": [0, 33, 0]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 3]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(138,1,1)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_250-252"]}, {"code": 250, "indent": 2, "parameters": [{"name": "!piss_slow", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_260-263"]}, {"code": 111, "indent": 2, "parameters": [1, 8, 0, 10, 0]}, {"code": 111, "indent": 3, "parameters": [1, 225, 0, 5, 1]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_270"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 9}, {"id": 44, "name": "ギガチャド主人公　イチャラブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーの目の前でイチャラブ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 44"]}, {"code": 655, "indent": 0, "parameters": ["$page = 1"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※導入"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　主人公による強制ベロチューしながらの乳揉み　ヤリゾーのハゲ頭が見てる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [985]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [132]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 0, "parameters": [132]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 0, "parameters": [132]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6-7"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　※テントシーン　（腕つかみの主人公版）　ヤリゾーのハゲ頭が見てる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$no = `stand-heroine-st_doggy-silhouetteProtag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_23-24"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["※シーン　ヤリゾーとの絆レベルが３以下"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 1, "parameters": ["※フェードアウトイン　背景キャンプ場"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [985]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 101, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 102, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_103"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 104, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 105, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_106"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 108, "indent": 1, "parameters": ["司祭アクメしまくり画像"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138, 1, 2)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_120-121"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138, 1, 2)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_130-133"]}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーがちんぽを出す"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_140"]}, {"code": 108, "indent": 1, "parameters": ["ちんぽが払いのけられる"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_150-152"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["※シーン　ヤリゾーとの絆レベルが４以下"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 1]}, {"code": 108, "indent": 1, "parameters": ["※フェードアウトイン　テントから顔出し　マジアクメ顔"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [105]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-206"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow8", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-224"]}, {"code": 108, "indent": 1, "parameters": ["※射精"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 1, "parameters": ["※司祭アクメ顔"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 1, "parameters": [105]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-235"]}, {"code": 108, "indent": 1, "parameters": ["※シーン　司祭を持ち上げてケツの穴にちんぽハメ"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138, 1, 3)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_240-245"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 250, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 61, 44, 1, 260, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_270"]}, {"code": 108, "indent": 1, "parameters": ["※アヘ顔"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138, 1, 3)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_280-283"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_284"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_285"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 108, "indent": 1, "parameters": ["※顔死～ん……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_290-291"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(138, 1, 3)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 108, "indent": 1, "parameters": ["※まんこから精子一気に逆流"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301-303"]}, {"code": 108, "indent": 1, "parameters": ["※シーン終了　背景"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 61, 44, 1, 310, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_311"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 28}, {"id": 45, "name": "寝取らせ　ちんぽ比べ　におい編", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ　ちんぽ比べ　におい編"]}, {"code": 408, "indent": 0, "parameters": ["(親イベントID コモン472"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 45"]}, {"code": 655, "indent": 0, "parameters": ["$page = 1"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※共通"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair0-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair1-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair2-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-Guess<PERSON>mell-head0-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-<PERSON><PERSON><PERSON>-head1-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-<PERSON><PERSON><PERSON>-head2-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモード"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["Lvl1-2", "Lvl3", "Lvl4", "Lvl5"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Lvl1-2"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Lvl3"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 3]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Lvl4"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Lvl5"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(61,45,num)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー絆lvlによって分岐"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※ヤリゾー絆lvlが１，２の時"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,45,2)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["※ヤリゾー絆lvlが３の時"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,45,3)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["※ヤリゾー絆lvlが４の時"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,45,4)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["※ヤリゾー絆lvlが５の時"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,45,5)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 45"]}, {"code": 655, "indent": 0, "parameters": ["$page = 2"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 1-2"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態でちんぽ二つ嗅がされてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 0, "parameters": ["※シーン5　主人公のほうに顔を向けてちんぽにキス"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!kuchu1_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1618, 1618, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 45"]}, {"code": 655, "indent": 0, "parameters": ["$page = 3"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 3"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態でちんぽ二つ嗅がされてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　驚き顔"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　驚き顔"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-63"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-91"]}, {"code": 108, "indent": 0, "parameters": ["※シーン6　ヤリゾーのほうに顔を向けてちんぽにキス"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!kuchu1_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1618, 1618, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 108, "indent": 0, "parameters": ["※シーン７　目隠しを外す"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-122"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックス"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61,47,1)"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 45"]}, {"code": 655, "indent": 0, "parameters": ["$page = 4"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 4"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態でちんぽ二つ嗅がされてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["※シーン８　にへら顔"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 108, "indent": 0, "parameters": ["※シーン6　ヤリゾーのほうに顔を向けてちんぽにキス"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 250, "indent": 0, "parameters": [{"name": "!kuchu1_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1618, 1618, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-53"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["※シーン７　目隠しを外す"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-62"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックス"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61,47,2)"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 2, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 45"]}, {"code": 655, "indent": 0, "parameters": ["$page = 5"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 1, 1684]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 5"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態でちんぽ二つ嗅がされてる"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうに顔を向ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　ヤリゾーのほうに顔を剥ける"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["※シーン８　にへら顔"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["※シーン6　ヤリゾーのほうに顔を向けてちんぽにキス"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "!kuchu1_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 108, "indent": 0, "parameters": ["※シーン９　ちんぽに高速舌舐り"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン10 射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-62"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["※シーン７　目隠しを外す"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-74"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※セックス"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(61,47,3)"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 24}, {"id": 46, "name": "寝取らせ　ちんぽ比べ　手コキ編い編", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ　ちんぽ比べ　手コキ編"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 1"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※共通"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair0-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair1-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-GuessSmell-hair2-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-Guess<PERSON>mell-head0-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-<PERSON><PERSON><PERSON>-head1-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "event-pri-<PERSON><PERSON><PERSON>-head2-0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントテストモード"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["Lvl1", "Lvl2", "Lvl3", "Lvl4", "Lvl5"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Lvl1"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Lvl2"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 3]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Lvl3"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Lvl4"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Lvl5"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 0, 6]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call($map,$event,num)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー絆lvlによって分岐"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(204) + 1"]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call($map,$event,num)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 2"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 1"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態で左右から伸びるちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうのみシコる　ヤリゾーのほうには手も入れない"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン３　射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"手\""]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22-24"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 3"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 2"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態で左右から伸びるちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　主人公のほうのみシコる"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　ヤリゾーのほうにコンパチする"]}, {"code": 108, "indent": 0, "parameters": ["――ピンッ！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1617, 1617, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン３　射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 4"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 3"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態で左右から伸びるちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　両方触る"]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン５　顔を赤らめる"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-23"]}, {"code": 108, "indent": 0, "parameters": ["※シーン６　ヤリゾーのちんぽを握ったまま主人公のちんぽをシコり出す"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　射精　主人公から"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　ヤリゾーのちんぽから手を離す"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-43"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 17"]}, {"code": 655, "indent": 0, "parameters": ["$page = 5"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 4"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態で左右から伸びるちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　両方触る"]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　ニヘラ顔"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-22"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　ヤリゾーのほうをシコりだす　主人公のほうはかなりゆっくりシコる"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1617, 1617, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　射精　ヤリゾーから"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-43"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　ヤリゾーのちんぽから手を離す"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 111, "indent": 0, "parameters": [0, 620, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************************************"]}, {"code": 408, "indent": 1, "parameters": ["セックス"]}, {"code": 408, "indent": 1, "parameters": ["********************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,46,7)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 6"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 5"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　目隠し状態で左右から伸びるちんぽ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　顔正面"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　ニヘラ顔でヤリゾーのほうだけシコりだす"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1617, 1617, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　射精　ヤリゾーから"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-22"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　主人公のちんぽをデコピン"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 140, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン　射精　主人公から"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 108, "indent": 0, "parameters": ["※シーン　司祭目隠しを取る"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,6)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 111, "indent": 0, "parameters": [0, 620, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************************************"]}, {"code": 408, "indent": 1, "parameters": ["セックス"]}, {"code": 408, "indent": 1, "parameters": ["********************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61,46,7)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 46"]}, {"code": 655, "indent": 0, "parameters": ["$page = 7"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※共通"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※テントシーン　影絵　外でそれを見ている主人公の頭"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 24}, {"id": 47, "name": "寝取らせ　ちんぽ当て後の罰ゲームセックス with ヤリゾー　主人公の目の前で", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ　ちんぽ当て後の罰ゲームセックス with ヤリゾー　主人公の目の前で"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 47"]}, {"code": 655, "indent": 0, "parameters": ["$page = 1"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 3"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　正常位"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　ちんぽをおまんこ、鼠径部の上にのっけるかんじであてがってる。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　挿入"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 108, "indent": 0, "parameters": ["※シーン３　腰を振り出す"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　カットイン　子宮押し上げ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "!piss_fast", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-42"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　カットイン　子宮押し上げ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $map, $event, $page, 60, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, $map, $event, $page, 70, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["※シーン　中出し"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["※シーン　外だし"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"体\""]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 4"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 47"]}, {"code": 655, "indent": 0, "parameters": ["$page = 2"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　正常位"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　ちんぽをおまんこ、鼠径部の上にのっけるかんじであてがってる。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　挿入　おほ顔"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 108, "indent": 0, "parameters": ["※シーン４　カットイン　子宮押し上げ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting6", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-48"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-52"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-64"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※Lvl 5"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$map = 61"]}, {"code": 655, "indent": 0, "parameters": ["$event = 47"]}, {"code": 655, "indent": 0, "parameters": ["$page = 3"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※グラフィック　正常位"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["※シーン１　ちんぽをおまんこ、鼠径部の上にのっけるかんじであてがってる。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-25"]}, {"code": 108, "indent": 0, "parameters": ["※シーン２　挿入　おほ顔"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-53"]}, {"code": 108, "indent": 0, "parameters": ["※カットイン"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-61"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※アクメ"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※射精"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108,2,7)"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $map, $event, $page, 100, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $map, $event, $page, 120, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, $map, $event, $page, 140, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_130"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_150"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-162"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $map, $event, $page, 170, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180"]}, {"code": 108, "indent": 0, "parameters": ["※後ろ姿歩き　腕組　ヤリゾー"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [47, 47, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [61]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -1]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 24}, {"id": 48, "name": "料理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["ルート"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 212, "indent": 0, "parameters": [-1, 39, true]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(0,100)"]}, {"code": 311, "indent": 0, "parameters": [0, 0, 0, 0, 9999, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_common_database_text_20-21"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["吸血姫　料理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 121, "indent": 0, "parameters": [99, 99, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [211]}, {"code": 355, "indent": 0, "parameters": ["$sp = screen.pictures[100]"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["x = $gameVariables.value(44)"]}, {"code": 655, "indent": 0, "parameters": ["y = $gameVariables.value(45)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["name = \"food/glass03_01\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$sp.show(name, 0, x, y, 100, 100, 255, 0)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-14"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 28}, {"id": 49, "name": "キャットシー見張り", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 11}, {"id": 50, "name": "吸血姫見張り", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ルート"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 234, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(61, 50, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["吸血姫絆レベル２ - 他人のセックス音をオカズに"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 225, "indent": 0, "parameters": [5, 5, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["マンズリシーン"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_crazy", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 7)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12-14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16-17"]}, {"code": 108, "indent": 0, "parameters": ["アクメ"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_soft", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 14, 7)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 11}]}