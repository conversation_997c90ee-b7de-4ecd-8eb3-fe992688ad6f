(function() {
    var _Window_EquipSlot_drawItem = Window_EquipSlot.prototype.drawItem;

    Window_EquipSlot.prototype.drawItem = function(index) {
        if (this._actor) {
            var rect = this.itemRectForText(index);
            this.changeTextColor(this.isEnabled(this._actor.equips()[index]) ? this.normalColor() : this.systemColor());
            this.drawText(this.slotName(index), rect.x, rect.y, 138, this.lineHeight());
            this.drawItemName(this._actor.equips()[index], rect.x + 138, rect.y);
        }
    };

    Window_EquipSlot.prototype.slotName = function(index) {
        var slots = ["武器", "盾", "頭", "身体", "装飾品", "指輪"];
        if (this._actor && (this._actor.actorId() === 1 || this._actor.actorId() === 21)) {
            return slots[index];
        } else {
            return this._actor ? $dataSystem.equipTypes[this._actor.equipSlots()[index]] : '';
        }
    };
})();
