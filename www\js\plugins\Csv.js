/*:
 * @plugindesc Custom CSV System
 * <AUTHOR>
 *
 * @help 
 *
 */

//

/*
    var csv_db_item = new CSV( $Node.fs.readFileSync("./csv/DB_Item.csv", "utf8") );
    csv_db_item

    new CSV(read()[0])

    S : Semicolon 
    C : Comma

    Format: C1

    CSV_C1
    [
        (i1),(i2),(i3)... 
    ]
    (\n)

    CSV_S1
    [
        (i1);(i2);(i3)... 
    ]
    (\n)

    CSV_C2
    [
        (i1),(i2),(i3)... 
    ]
    (\n)
    (\n)
*/

// gerade kein plan ist auch nicht so relevant

// only to support specific csv
// uses not needed last \n
function CSV(str)
{
    this.Data = [];
    this.Data.push([]);

    let entry = "";
    let row = 0;
    let lock = false;

    for (let i = 0; i < str.length+1; i++)
    {
        let char = str[i];
        //console.log(`${i}:${char} ${str.charCodeAt(i)}`);

        if(char === undefined)
        {
            this.Data.pop();
            break;
        }

        if(lock && char === '"' && (str[i+1] === "," || str[i+1] === "\n") )
        {
            lock = false;
            continue
        }
        else if(!lock && entry === "" && char === '"' && (str[i+1] !== ","  || str[i+1] !== "\n") )
        {
            lock = true;
            continue
        }

        if(!lock && (char === "," || char === '"' && str[i+1] === "," || char === "\n") )
        {
            this.Data[row].push(entry);
            entry = "";

            if(char === "\n")
            {
                this.Data.push([]);
                row++;
            }
        }
        else
        {
            if(char === '"' && str[i+1] === '"') i++;
            entry += char;
        }
    }

    console.log("-----------------------");
    console.log(this.Data.length);
    console.log(this.Data);
}

// test CSV
/*
let data = 
`"abc","def","ghi"
"012","345","678"
`;

let csv = new CSV(data);
console.log(csv);

let data2 = 
`abc,def,ghi
012,345,678
`;

let csv2 = new CSV(data2);
console.log(csv2);
*/

/*
let data3 = 
`"abc"
012
`;

let csv3 = new CSV(data3);
console.log(csv3);
*/