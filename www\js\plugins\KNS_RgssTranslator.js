/*:
 * @plugindesc ver.1.0.2 このスクリプトは全てのKNS_系プラグインの上に設置してください
 * <AUTHOR>
 *
 * @help
 * RubyとJSの構文の都合で実行できないイベントスクリプトを
 * 実行前に手動で置換します。
 * 他のプラグインで登録する場合は以下のように記述してください。
 *
 * if (typeof KNS_RgssTranslator !== "undefined"){
 *   // Game_Interpreterに使用
 *   KNS_RgssTranslator.addScriptPattern(文字列or正規表現, 置換文字列or関数);
 *   // Game_Characterの移動ルートスクリプトに使用
 *   KNS_RgssTranslator.addMovePattern(文字列or正規表現, 置換文字列or関数);
 * }
 *
 * ver.1.0.1(2023-05-14)
 * - RGSSのピクチャ表示、移動スクリプトに対応しました
 * ver.1.0.2(2023-05-17)
 * - RGSSの変数、スイッチ、セルフスイッチに一部対応しました
 * （不完全な機能です）
 */
const KNS_RgssTranslator = {
	name: "KNS_RgssTranslator",
	scriptPatterns: [],
	movePatterns: [],
	addScriptPattern: function(re, to){
		this.addGeneralPattern(this.scriptPatterns, re, to);
	},
	addMovePattern: function(re, to){
		this.addGeneralPattern(this.movePatterns, re, to);
	},
	addGeneralPattern: function(patterns, re, to){
		const found = patterns.find(function(pat){ return pat[0] === re; });
		if (found){
			found[1] = to;
		}else{
			patterns.push([re, to]);
		}
	},
	replaceScriptPattern: function(text){
		return this.replaceGeneralPattern(text, this.scriptPatterns);
	},
	replaceMovePattern: function(text){
		return this.replaceGeneralPattern(text, this.movePatterns);
	},
	replaceGeneralPattern: function(text, patterns){
		patterns.forEach(function(pat){ text = text.replace(...pat); });
		return text;
	},
};


(function(){
	[
		[/SceneManager\.call\(/g, "SceneManager.push("],
		[/\\[\s\S]\(/mg, "("],
		[/screen\.pictures\[(.+?)\]/g, `(function(){
	let pic = this.picture($1);
	if (!pic){
		pic = new Game_Picture()
		this._pictures[this.realPictureId($1)] = pic;
	}
	return pic;
}).call($gameScreen)`],
		[/\$game_(switches|self_switches|variables)\[([\s\S]+?)\]/mg, function(_, type, id){
			if (type === "self_switches"){
				return `$gameSelfSwitches._data[${id}]`;
			}else if (type === "switches"){
				return `$gameSwitches._data[${id}]`;
			}else{
				return `$gameVariables._data[${id}]`;
			}
		}],
	].forEach(function(pat){ this.addScriptPattern(...pat); }, this);

	//==========================================================
	// alias Array
	//==========================================================
	// ruby移植用
	Object.defineProperties(Array.prototype, {
		max: { get: function(){ return Math.max(...this)} },
		min: { get: function(){ return Math.min(...this)} },
	});

	//==========================================================
	// alias Game_Character
	//==========================================================
	const _Game_Character_processMoveCommand = Game_Character.prototype.processMoveCommand;
	Game_Character.prototype.processMoveCommand = function(command) {
		if (command.code === Game_Character.ROUTE_SCRIPT){
			eval(KNS_RgssTranslator.replaceMovePattern(command.parameters[0]));
		}else{
			_Game_Character_processMoveCommand.apply(this, arguments);
		}
	};

	//==========================================================
	// alias Game_Interpreter
	//==========================================================
	const _Game_Interpreter_command111 = Game_Interpreter.prototype.command111;
	Game_Interpreter.prototype.command111 = function() {
		if (this._params[0] === 12){
			let result = !!eval(KNS_RgssTranslator.replaceScriptPattern(this._params[1]));
			this._branch[this._indent] = result;
			if (this._branch[this._indent] === false) {
				this.skipBranch();
			}
		}else{
			_Game_Interpreter_command111.apply(this, arguments);
		}
		return true;
	};

	const _Game_Interpreter_command122 = Game_Interpreter.prototype.command122;
	Game_Interpreter.prototype.command122 = function() {
		if (this._params[3] === 4){
			let value = eval(KNS_RgssTranslator.replaceScriptPattern(this._params[4]));
			for (let i = this._params[0]; i <= this._params[1]; i++) {
				this.operateVariable(i, this._params[2], value);
			}
		}else{
			_Game_Interpreter_command122.apply(this, arguments);
		}
		return true;
	};

	Game_Interpreter.prototype.command355 = function() {
		let script = this.currentCommand().parameters[0] + '\n';
		while (this.nextEventCode() === 655) {
			this._index++;
			script += this.currentCommand().parameters[0] + '\n';
		}
		eval(KNS_RgssTranslator.replaceScriptPattern(script));
		return true;
	};
}).call(KNS_RgssTranslator);