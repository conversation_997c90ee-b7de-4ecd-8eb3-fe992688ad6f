/*:
 * @plugindesc Adds New Equip Type on Load
 * <AUTHOR> Name
 *
 * @help This plugin ensures that newly added equip types are properly handled when loading a save file to prevent crashes.
 */

(function() {
    var _Scene_Load_onLoadSuccess = Scene_Load.prototype.onLoadSuccess;
    Scene_Load.prototype.onLoadSuccess = function() {
        _Scene_Load_onLoadSuccess.call(this);
        this.addMissingEquipTypes();
    };

    Scene_Load.prototype.addMissingEquipTypes = function() {
        $gameActors._data.forEach(function(actor) {
            if (actor) {
                var equips = actor._equips;
                var equipTypes = $dataSystem.equipTypes;
                while (equips.length < equipTypes.length - 1) {
                    equips.push(new Game_Item());
                }
            }
        });
    };
})();
