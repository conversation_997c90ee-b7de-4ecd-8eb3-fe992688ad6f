{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 20, "note": "dark_night\n夜固定BGM\n王都\nプライベートエリア\n王都宿屋\n宿屋\n拠点\n従者同行不可", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 25, "data": [7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7424, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7428, 7452, 7452, 7452, 7436, 7452, 7452, 7452, 7436, 7452, 7452, 7452, 7432, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 7810, 7810, 7810, 7456, 7810, 7810, 7810, 7456, 7810, 7810, 7810, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 7816, 7816, 7816, 7456, 7816, 7816, 7816, 7456, 7816, 7816, 7816, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7424, 7448, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7456, 1540, 1541, 1540, 7440, 7448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7452, 7462, 1540, 7467, 7457, 7455, 7469, 1540, 7467, 7455, 7469, 1540, 7467, 7453, 7462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 771, 0, 0, 0, 771, 0, 0, 0, 771, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 779, 0, 0, 0, 779, 5, 0, 0, 779, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 591, 114, 0, 114, 591, 13, 0, 114, 591, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 599, 122, 0, 122, 599, 0, 0, 122, 599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 87, 0, 86, 0, 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 0, 94, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["オープニング、宿屋の部屋に司祭と二人ではじめて登場"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 121, "indent": 0, "parameters": [9, 9, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, false]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 241, "indent": 0, "parameters": [{"name": "Scene5", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 1, 1, 3, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 2, 8, 0]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,true);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6-7"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [104]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9-11"]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 120, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_14"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [110]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 241, "indent": 0, "parameters": [{"name": "PerituneMaterial_Flow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "indent": null, "parameters": [6]}, {"code": 38, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Break", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "indent": null, "parameters": [6]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Break", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "Wind1", "pan": 0, "pitch": 150, "volume": 80}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 121, "indent": 0, "parameters": [8, 8, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 333, 18, 13, 6, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 255], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Wind1", "pan": 0, "pitch": 150, "volume": 80}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 7, 8, 2]}, {"code": 121, "indent": 0, "parameters": [9, 9, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 121, "indent": 0, "parameters": [8, 8, 1]}, {"code": 241, "indent": 0, "parameters": [{"name": "PerituneMaterial_Flow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 108, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_111\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-54"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [213]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-75"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 1, 1, 100, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_76-77"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-103"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_104-108"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,false);"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 121, "indent": 0, "parameters": [209, 209, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 209, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["キスシーン　（シーンID:OP2）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 117, "indent": 0, "parameters": [211]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-11"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12-14"]}, {"code": 250, "indent": 0, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15"]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [103]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 1, 2, 40, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16-18"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 129, "indent": 0, "parameters": [2, 0, 0]}, {"code": 319, "indent": 0, "parameters": [2, 6, 79]}, {"code": 129, "indent": 0, "parameters": [2, 1, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_42-46"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 108, "indent": 0, "parameters": ["------------------------------------------------------------"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["エロシーン開始"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["------------------------------------------------------------"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 0, 3)"]}, {"code": 108, "indent": 0, "parameters": ["------------------------------------------------------------"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["翌日"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["------------------------------------------------------------"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 0, 4)"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 215, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["OP-エロシーン　はじめてのセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,true);"]}, {"code": 201, "indent": 0, "parameters": [0, 22, 4, 5, 8, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 4, 4, 2]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 0, "parameters": [9, 9, 0]}, {"code": 319, "indent": 0, "parameters": [2, 2, 0]}, {"code": 319, "indent": 0, "parameters": [2, 3, 0]}, {"code": 319, "indent": 0, "parameters": [2, 4, 0]}, {"code": 319, "indent": 0, "parameters": [2, 5, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 245, "indent": 0, "parameters": [{"name": "Night", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_101\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_102\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 0, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["こっからフェラチオシーン"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-60"]}, {"code": 108, "indent": 0, "parameters": ["★　表情をイき顔に変更"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_61"]}, {"code": 108, "indent": 0, "parameters": ["臭いかいでイく"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_201\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_202\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_62-65"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_91"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_95"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-112"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-126"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_127-129"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 118, "indent": 0, "parameters": ["ループ"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_301\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_302\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_131-136"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["ループ"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 108, "indent": 0, "parameters": ["フラッシュアンド頭掴み　目はイき耐え"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 231, "indent": 0, "parameters": [20, "event_bj1_man-arm", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-152"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 231, "indent": 0, "parameters": [18, "event_bj1_zamen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-166"]}, {"code": 108, "indent": 0, "parameters": ["●司祭　しゃぶりながらアヘ目　司祭もイく"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_170-173"]}, {"code": 108, "indent": 0, "parameters": ["●司祭　疲れ果てた目"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180"]}, {"code": 108, "indent": 0, "parameters": ["●　口もにゅ"]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190-196"]}, {"code": 108, "indent": 0, "parameters": ["●口開け"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_401\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_402\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-202"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_204"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_206"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 12]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [221]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-215"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-222"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-233"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["こっからセックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_300"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_301-307"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_310-312"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_501\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_502\")"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 231, "indent": 0, "parameters": [9, "event-tetsunagikkusu-semen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_320-323"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["ごめん、中に～"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_330"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["子供が～"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_340"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_350-362"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_370"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 231, "indent": 0, "parameters": [9, "event-tetsunagikkusu-semen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_380-389"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_400-402"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_410-413"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_420"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_430-433"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[0, 255, 136, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_440-441"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 1"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 402]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ー------------------------------"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 319, "indent": 0, "parameters": [2, 2, 82]}, {"code": 319, "indent": 0, "parameters": [2, 3, 92]}, {"code": 319, "indent": 0, "parameters": [2, 4, 102]}, {"code": 319, "indent": 0, "parameters": [2, 5, 79]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,false);"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 211, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["オープニング、司祭とセックスした翌日"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 121, "indent": 0, "parameters": [8, 8, 1]}, {"code": 352, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 0, "parameters": [9, 9, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_ALL_STOP "]}, {"code": 314, "indent": 0, "parameters": [0, 0]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,100)"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,100)"]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, false]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 201, "indent": 0, "parameters": [0, 22, 4, 5, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,true);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["$heroine_naked2", 0], "indent": null}, {"code": 40, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine_naked2", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 4, 5, 8]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 32, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["$prot_sleep", 0], "indent": null}, {"code": 31, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$prot_sleep", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "Sparrow-Real_Ambi01-1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 1, 4, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 22, 1, 4, 15, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_16"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェラチオシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-39"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_short", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-63"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 231, "indent": 0, "parameters": [18, "event_bj1_zamen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-82"]}, {"code": 108, "indent": 0, "parameters": ["フェラチオ終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["$protag-naked", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$protag-naked", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 319, "indent": 0, "parameters": [2, 2, 0]}, {"code": 319, "indent": 0, "parameters": [2, 3, 0]}, {"code": 319, "indent": 0, "parameters": [2, 4, 0]}, {"code": 319, "indent": 0, "parameters": [2, 5, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,false);"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_ALL_STOP "]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 319, "indent": 0, "parameters": [2, 2, 82]}, {"code": 319, "indent": 0, "parameters": [2, 3, 92]}, {"code": 319, "indent": 0, "parameters": [2, 4, 102]}, {"code": 319, "indent": 0, "parameters": [2, 5, 0]}, {"code": 121, "indent": 0, "parameters": [211, 211, 0]}, {"code": 201, "indent": 0, "parameters": [0, 17, 1, 9, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 5, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["はじめてのセックス"]}, {"code": 111, "indent": 0, "parameters": [1, 7, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 0, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [97]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 8}, {"id": 3, "name": "ベッド", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_inn1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_inn2\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_inn3\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(64, \"select_inn4\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(65, \"status_up\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[63]", "\\v[64] en(v[83]>=200)", "\\v[65]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[63]"]}, {"code": 117, "indent": 1, "parameters": [325]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[64] en(v[83]>=200)"]}, {"code": 108, "indent": 1, "parameters": ["プロテクションを解除してのセックス"]}, {"code": 117, "indent": 1, "parameters": [949]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 3, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[65]"]}, {"code": 117, "indent": 1, "parameters": [860]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$prot_sleep", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [0, 10, true]}, {"code": 117, "indent": 0, "parameters": [324]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "down5", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [0, 10, true]}, {"code": 117, "indent": 0, "parameters": [324]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["子宮プロテクション解除セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["疲労チェック"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 4, "$gameActors.actor(1).tp;"]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 100, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_tired\")"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 4, "$gameActors.actor(2).tp;"]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 100, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_tired\")"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["本編"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-15"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックス"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-52"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["妊娠判定"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 121, "indent": 0, "parameters": [25, 25, 1]}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(173, 2, 1)"]}, {"code": 108, "indent": 0, "parameters": ["****カットイン呼び出しここまで*****"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_pro_breed\")"]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [163, 163, 0, 1, 465]}, {"code": 122, "indent": 0, "parameters": [164, 164, 0, 1, 466]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"haramase\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 1, "parameters": ["成功"]}, {"code": 108, "indent": 1, "parameters": ["****カットイン呼び出し*****"]}, {"code": 121, "indent": 1, "parameters": [25, 25, 1]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(173, 2, 1)"]}, {"code": 108, "indent": 1, "parameters": ["****カットイン呼び出しここまで*****"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_pro_breed_success\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [212]}, {"code": 121, "indent": 1, "parameters": [25, 25, 1]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_80-82"]}, {"code": 242, "indent": 1, "parameters": [3]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [240]}, {"code": 201, "indent": 1, "parameters": [0, 245, 8, 7, 0, 0]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["失敗"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["**************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 5, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 4}, {"id": 4, "name": "司祭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 60, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 4}, {"id": 5, "name": "司祭・主人公[合流]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [12]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 5, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 115, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 8, "pattern": 0, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [115, 115, 1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 96, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 97, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 261, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 5}, {"id": 6, "name": "Rando_mob1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor4", "direction": 8, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 215, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 470, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["侵入イベント用"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************"]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 122, "indent": 0, "parameters": [473, 473, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [472, 472, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [650]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_steal\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_bukkake\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_fuck\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(70, \"leave\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\c[18]\\v[62]", "\\c[18]\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*********************"]}, {"code": 408, "indent": 1, "parameters": ["盗む"]}, {"code": 408, "indent": 1, "parameters": ["*********************"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [466]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[18]\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*********************"]}, {"code": 408, "indent": 1, "parameters": ["Bukkake"]}, {"code": 408, "indent": 1, "parameters": ["*********************"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 213, "indent": 1, "parameters": [-1, 4, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-10)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[18]\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["*********************"]}, {"code": 408, "indent": 1, "parameters": ["モブセックス"]}, {"code": 408, "indent": 1, "parameters": ["*********************"]}, {"code": 108, "indent": 1, "parameters": ["********************モブセックススタート*********************"]}, {"code": 117, "indent": 1, "parameters": [512]}, {"code": 108, "indent": 1, "parameters": ["********************モブセックス終了*********************"]}, {"code": 111, "indent": 1, "parameters": [0, 116, 0]}, {"code": 122, "indent": 2, "parameters": [294, 294, 0, 0, 0]}, {"code": 355, "indent": 2, "parameters": ["key = [22, 6, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 119, "indent": 2, "parameters": ["末"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[70]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[70]"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [6, 6, 0, 0, -1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 201, "indent": 0, "parameters": [0, 17, 8, 4, 2, 0]}, {"code": 122, "indent": 0, "parameters": [294, 294, 0, 0, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 121, "indent": 0, "parameters": [19, 19, 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 215, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 40, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "down3x", "direction": 4, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 5}, {"id": 7, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 2}, {"id": 8, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 2}, {"id": 9, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 2}, {"id": 10, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 11}, {"id": 11, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 11}, {"id": 12, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 11}, {"id": 13, "name": "客　3号室", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Behavior2", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 4}, {"id": 14, "name": "ギャラリーモード仮", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [1, 3, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [10, 60, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 128, "indent": 2, "parameters": [60, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [75, 75, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"mode_tdt\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"mode_gallery\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"mode_recollection\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(64, \"mode_cheat\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(65, \"mode_ntr_off\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(66, \"mode_scat_onoff\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(67, \"mode_prot_face\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(68, \"mode_name_change\")"]}, {"code": 111, "indent": 0, "parameters": [8, 999]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [75, 75, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[1]>=9999)", "\\v[63] if(v[1]>=9999)", "\\v[64] en(v[75] == 1)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_no\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_yes\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 121, "indent": 2, "parameters": [200, 200, 0]}, {"code": 201, "indent": 2, "parameters": [0, 44, 12, 42, 8, 0]}, {"code": 119, "indent": 2, "parameters": ["末"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[1]>=9999)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] if(v[1]>=9999)"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 121, "indent": 2, "parameters": [5, 5, 0]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 135, "indent": 2, "parameters": [0]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 111, 1, 15, 0, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64] en(v[75] == 1)"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 126, "indent": 1, "parameters": [999, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[65] en(s[32])"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[65] en(s[32])"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_91"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_92"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 102, "indent": 2, "parameters": [["\\v[62]", "\\v[61]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[62]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[61]"]}, {"code": 121, "indent": 3, "parameters": [32, 32, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_93"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[66]"]}, {"code": 108, "indent": 1, "parameters": ["スカトロスイッチ切り替え"]}, {"code": 111, "indent": 1, "parameters": [0, 33, 0]}, {"code": 121, "indent": 2, "parameters": [33, 33, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["OFF"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 121, "indent": 2, "parameters": [33, 33, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["ON"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[67]", "\\v[68]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[67]"]}, {"code": 108, "indent": 1, "parameters": ["主人公の顔の表示非表示"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200"]}, {"code": 102, "indent": 1, "parameters": [["ON", "OFF"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "ON"]}, {"code": 121, "indent": 2, "parameters": [37, 37, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "OFF"]}, {"code": 121, "indent": 2, "parameters": [37, 37, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[68]"]}, {"code": 102, "indent": 1, "parameters": [["\\n[1]", "\\n[2]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\n[1]"]}, {"code": 108, "indent": 2, "parameters": ["主人公の名前入力"]}, {"code": 356, "indent": 2, "parameters": ["InputNamePrompt 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\n[2]"]}, {"code": 108, "indent": 2, "parameters": ["司祭名前入力"]}, {"code": 356, "indent": 2, "parameters": ["InputNamePrompt 2"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[80]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 102, "indent": 0, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_explanation_a\")"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_explanation_s\")"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_explanation_d\")"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 4}, {"id": 15, "name": "<OXY:0,4>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$fade_inn", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 294, "variableValid": false, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["出現条件($gameVariables.value(294)==2)"]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 6, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 201, "indent": 0, "parameters": [0, 17, 8, 4, 0, 0]}, {"code": 122, "indent": 0, "parameters": [294, 294, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 8}, {"id": 16, "name": "<OXY:0,4>", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$fade_inn", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 294, "variableValid": false, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["出現条件($gameVariables.value(294)==3)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 201, "indent": 0, "parameters": [0, 17, 13, 4, 0, 0]}, {"code": 122, "indent": 0, "parameters": [294, 294, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 8}, {"id": 17, "name": "司祭自室浮気イベ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 243, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 17, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 17, 1, 100, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 22, 2, 7, 2, 0]}, {"code": 203, "indent": 1, "parameters": [6, 0, 2, 6, 2]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [108]}, {"code": 250, "indent": 1, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 245, "indent": 1, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-13"]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [108]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_14"]}, {"code": 245, "indent": 1, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [108]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_15-18"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 4, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_19"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [108]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_22-23"]}, {"code": 245, "indent": 1, "parameters": [{"name": "teman_crazy", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_24"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 4, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_25-26"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_27-28"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 4, 1)"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 411]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_29"]}, {"code": 108, "indent": 1, "parameters": ["終了"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 201, "indent": 1, "parameters": [0, 22, 4, 4, 2, 0]}, {"code": 203, "indent": 1, "parameters": [6, 0, 4, 5, 8]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 244, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 0}, {"id": 18, "name": "違法侵入イベ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 294, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 6, 4, 0]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 6, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 117, "indent": 0, "parameters": [511]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 0}, {"id": 19, "name": "ヤリゾー寝取らせ開始イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": true, "switch2Id": 133, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameSwitches.value(643) === true && $gameSwitches.value(133) === true &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameSwitches.value(503) === false &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameParty.members().includes($gameActors.actor(2)) && $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameParty.size() === 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["出現条件($gameVariables.value(10)==2 && $game_switches[503]==false)"]}, {"code": 355, "indent": 0, "parameters": ["$page = 1"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 111, "indent": 1, "parameters": [0, 260, 1]}, {"code": 108, "indent": 2, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾーとの初期イベ終えてない場合は初期イベ発生"]}, {"code": 408, "indent": 2, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 19, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 4, "$gameActors.actor(2).tp;"]}, {"code": 111, "indent": 2, "parameters": [1, 54, 0, 100, 1]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_tired\")"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾーとの初期イベ終えてる場合は出来る事一覧表示"]}, {"code": 408, "indent": 2, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 22, 19, $page, 10, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 22, 19, $page, 20, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(63, 22, 19, $page, 30, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(64, 22, 19, $page, 40, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(65, 22, 19, $page, 50, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(66, 22, 19, $page, 60, 0)"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]", "\\v[63] en(v[761]>=1)", "\\v[64] en(v[761]>=1)"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["手マン"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 3)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["手コキ"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 4)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[63] en(v[761]>=1)"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["手マン 再び"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 5)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [3, "\\v[64] en(v[761]>=1)"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["クンニ"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 6)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["\\v[65] en(v[761]>=2)"], 0, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[65] en(v[761]>=2)"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["フェラ"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 7)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["\\v[66] en(v[761]>=3)"], 0, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[66] en(v[761]>=3)"]}, {"code": 108, "indent": 3, "parameters": ["************************************"]}, {"code": 408, "indent": 3, "parameters": ["3P"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 19, 8)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_999"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["▽▽コピー用▽▽"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 1, "parameters": ["絆レベル３以下"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["絆レベル４以上"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["△△コピー用△△"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": false, "switch2Id": 133, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 2"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭にヤリゾーとの寝取らセックスについて話す"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "recollection-yarizo-meeting", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 234, "indent": 0, "parameters": [1, [0, 0, 0, 255], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-4"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 2, "parameters": ["絆レベル３以上の場合"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_30-33"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 22, 19, $page, 40, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 22, 19, $page, 50, 0)"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_41-44"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["絆レベル４以上の場合"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_100-103"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_105"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾー寝取らせ開始できるようになる"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー呼んでスケベダンスするシーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["エロイベ開始"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 4, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 2, 8, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_201"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_202"]}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーとの関係性で分岐"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーとの関係性が３以下のとき"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_203-211"]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_212"]}, {"code": 245, "indent": 1, "parameters": [{"name": "heart", "pan": 0, "pitch": 130, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_213-220"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーとの関係性が４以上のとき"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300-302"]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_303-304"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公タンスから出てくる"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 130, "volume": 100}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 40, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 32, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 31, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Blow4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 41, "indent": null, "parameters": ["$o0096012813123122260", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Blow4", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$o0096012813123122260", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_230"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_231-233"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 240, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 245, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_241"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_246"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_242-243"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 250, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_251-252"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーとの関係性が３以下"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_260-262"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヤリゾーとの関係性が４以上"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_270"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 22, 19, $page, 271, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_272"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_280-281"]}, {"code": 121, "indent": 0, "parameters": [260, 260, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_900"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 201, "indent": 0, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 1]}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 31, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 3"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーの手マン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 111, "indent": 0, "parameters": [1, 420, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 5, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 241, "indent": 0, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 108, "indent": 1, "parameters": ["絆レベル３以下"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["絆レベル４以上"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_15-16"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 5, 82]}, {"code": 122, "indent": 1, "parameters": [29, 29, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["絆レベル３以下"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-124"]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"npc_ero_giryou\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_125"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭イく"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_126"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭ダウン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1"]}, {"code": 117, "indent": 0, "parameters": [195]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 160, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 170, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 22, 19, $page, 180, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-152"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_161"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_171"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_181"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-204"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting9", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_205-207"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 411]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 544]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭起き上がる"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,100)"]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 1]}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_210-211"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 220, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 230, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 22, 19, $page, 240, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_221"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_231"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_241"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(2,-100)"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 4"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーに手コキ"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 5, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 241, "indent": 0, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン　ヤリゾー手コキ開始"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-20"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン　ちんぽシゴき始め"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-41"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン　くちゅ音になる"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-68"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_69-71"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_short_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_72-76"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ハッとした顔"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-83"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_short_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_84-85"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_short_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_86-88"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_short_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_89-91"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_short_strong", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_92-101"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_extreme", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 5]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 1, "parameters": [467, 467, 0, 1, 1684]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-112"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 6]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_113-118"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 130, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 140, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_119"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 445]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 117, "indent": 1, "parameters": [902]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["事後"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 160, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 170, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 22, 19, $page, 180, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] if(s[33])", "\\v[63]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["精子を拭う"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [2001, 2100, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 2, "parameters": [90, 90, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_161"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] if(s[33])"]}, {"code": 108, "indent": 1, "parameters": ["おしっこで精子を洗い流す"]}, {"code": 250, "indent": 1, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_171"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [2001, 2100, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 2, "parameters": [90, 90, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_172"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["何もしない"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ご満足いただけましか？"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 210, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["良かったと言ってキスをする"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["まだ愛の言葉を聞いていない"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_211-212"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 220, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 230, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["終了させる"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["更に司祭に自分を手コキさせる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_231"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [160]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_240-253"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [458, 458, 0, 4, "\"手\""]}, {"code": 250, "indent": 2, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [160]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 2, "parameters": ["司祭の経験数[主人公]"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 108, "indent": 2, "parameters": ["主人公の性経験値"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 117, "indent": 2, "parameters": [513]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [12, "$cum_amount < 1000"]}, {"code": 108, "indent": 3, "parameters": ["射精量が1000未満"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_260-265"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(1,-100)"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["射精量が1000以上"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_270-273"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(1,-100)"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 5"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー手マン再び"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 5, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 241, "indent": 0, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン　ヤリゾー手マン開始"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-17"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["おめこ触る"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-25"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["拘束手メコ開始　司祭耐える表情"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_crazy", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-40"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フラッシュ　司祭顔赤らめて耐える"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭汗だくですごい表情"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-64"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ダイスロール後にイく　のけぞり"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-75"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウト　ベッドでぶったおれ司祭"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [196]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-104"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, $page, 110, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, $page, 120, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_111-112"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_121"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(2,-100)"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 6"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾークンニ"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 5, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン　テキスト"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 241, "indent": 0, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾークンニ開始　びっくり顔"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-18"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ナメクジ舌はいずり　顔真っ赤　目細目耐え　歯食いしばり"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-38"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["高速レロレロ　顔真っ赤　上目アヘ　歯食いしばり"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 125, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-55"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["クリ吸引しながら舌先転がし　顔真っ赤　口開け　ひにゃあ顔"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["顔真っ赤　口閉じ激耐え"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-94"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["判定"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"npc_ero_giryou\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["のけぞりアクメ"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130-134"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 1, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 7"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーフェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 5, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 4, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 102, "indent": 1, "parameters": [["1(Yarizo lvl 1-3)", "2(Yarizo lvl 4-5)", "現在の関係性レベル"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "1(Yarizo lvl 1-3)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "2(Yarizo lvl 4-5)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["2"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "現在の関係性レベル"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 2]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["絆レベル1-3"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 241, "indent": 1, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-6"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんしゃぶ開始"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 13]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-20"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽれろれろ開始"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 14]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-39"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40-43"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精アゲイン"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-52"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽから口離す"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_weak", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_60-61"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽ目隠し状態"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 4)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-72"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["2"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["絆レベル4-5"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 241, "indent": 1, "parameters": [{"name": "BGM_sweet", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-104"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんしゃぶ開始"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110-123"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_130-138"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精アゲイン"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_140-141"]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽから口離す"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_weak", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 1, "parameters": [150]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_150-151"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 1, "parameters": ["************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽ目隠し状態"]}, {"code": 408, "indent": 1, "parameters": ["************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 4)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_160-165"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["共通事後"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, 7, 210, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, 7, 220, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 22, 19, 7, 230, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(64, 22, 19, 7, 240, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[64] if(v[204]>=4)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_211"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_221"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(141, 5, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_231"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64] if(v[204]>=4)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_241-243"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 3]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$page = 8"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーと３Ｐ　（寝取らせ ヤリゾー Lvl3)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["実行準備"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入シーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-pondering", "pan": 0, "pitch": 100, "volume": 85}]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-16"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-24"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 0, "parameters": ["*ベロチュー終わり　顔１"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-61"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-87"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-91"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, 8, 110, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, 8, 120, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_111"]}, {"code": 108, "indent": 1, "parameters": ["キス"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_112"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_121"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130-131"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-141"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 19, 8, 150, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 22, 19, 8, 160, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 119, "indent": 1, "parameters": ["ヤリゾー中出し"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["ヤリゾー外出し"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー中出し"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["ヤリゾー中出し"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-201"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_210-212"]}, {"code": 111, "indent": 0, "parameters": [0, 33, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220"]}, {"code": 108, "indent": 1, "parameters": ["小便出す"]}, {"code": 250, "indent": 1, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-234"]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["数値処理"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 119, "indent": 0, "parameters": ["その後のシーン"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー中出し"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["ヤリゾー外出し"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_240"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ抜ける"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_250-251"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 5)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_260-263"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["数値処理"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["その後のシーン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["その後のシーン"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_270-273"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 1, "parameters": [1, 761, 0, 2, 0]}, {"code": 122, "indent": 2, "parameters": [761, 761, 0, 0, 3]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 17, 3, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 7}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["key = [22, 3, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 2}, {"id": 21, "name": "他のマップからの引継ぎイベ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["王都中央地区ナンパ男寝取らせからの引継ぎイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 121, "indent": 0, "parameters": [115, 115, 1]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 5, \"D\"];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 243, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "heart", "pan": 0, "pitch": 120, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [110]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 21, 1, 40, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 22, 21, 1, 50, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [150]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["足コキシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"足\""]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_mid", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-122"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-153"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["足コキシーン後"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [0, 80, 0]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 21, 1, 170, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 22, 21, 1, 180, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[73] >= 1)"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_171"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[73] >= 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_181"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["報告後セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(141, 5, 2)"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 244, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 2}, {"id": 22, "name": "ヤリゾーが部屋に来る", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 204, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(204) >= 2 && $gameSwitches.value(503) === false &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameSwitches.value(115) === true;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロ - ヤリゾー宿屋に来る"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$page = 1"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーとの初期イベ終えてる場合は出来る事一覧表示"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 22, $page, 2, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 22, 22, $page, 3, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 111, "indent": 1, "parameters": [0, 36, 0]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 22, 22, $page, 4, 0)"]}, {"code": 655, "indent": 2, "parameters": ["var_from_sheet(62, 22, 22, $page, 5, 0)"]}, {"code": 655, "indent": 2, "parameters": ["var_from_sheet(63, 22, 22, $page, 6, 0)"]}, {"code": 655, "indent": 2, "parameters": ["var_from_sheet(64, 22, 22, $page, 7, 0)"]}, {"code": 655, "indent": 2, "parameters": ["var_from_sheet(65, 22, 22, $page, 8, 0)"]}, {"code": 655, "indent": 2, "parameters": ["var_from_sheet(66, 22, 22, $page, 9, 0)"]}, {"code": 102, "indent": 2, "parameters": [["\\v[62]", "\\v[63]", "\\v[64]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[62]"]}, {"code": 108, "indent": 3, "parameters": ["Lvl2"]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[63]"]}, {"code": 108, "indent": 3, "parameters": ["Lvl3"]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 3]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[64]"]}, {"code": 108, "indent": 3, "parameters": ["Lvl4"]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 4]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 102, "indent": 2, "parameters": [["\\v[65]", "\\v[66]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[65]"]}, {"code": 108, "indent": 3, "parameters": ["Lvl5"]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 0, 5]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[66]"]}, {"code": 108, "indent": 3, "parameters": ["現在のレベル"]}, {"code": 119, "indent": 3, "parameters": ["通常再生"]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 1, 204]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(54)"]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(0, 22, num)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["通常再生"]}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(204)"]}, {"code": 655, "indent": 2, "parameters": ["MapEvent.call(0, 22, num)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロ - ヤリゾー宿屋に来る Lvl2"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 243, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 7, 2, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 39, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 2, 9, 8]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 241, "indent": 0, "parameters": [{"name": "DBM_Ghost_Town", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 105, "volume": 80}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 20, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 105, "volume": 80}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 20, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 105, "volume": 80}]}, {"code": 225, "indent": 0, "parameters": [5, 5, 20, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_21"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 8, 8]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}, {"code": 17, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロ - ヤリゾー宿屋に来る Lvl3"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 243, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 7, 2, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 39, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 2, 9, 8]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-7"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 6, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "jazzy-dream_loop", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-38"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-evil3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-81"]}, {"code": 241, "indent": 0, "parameters": [{"name": "DBM_Ghost_Town", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーンちん嗅ぎシーン"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-126"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ擦り付け"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130-141"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-153"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["アヘ顔"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-163"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["驚き顔"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_170"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["歯食いしばり"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180-183"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190-192"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※ちんぽ離れる　顔に精子と陰毛まみれ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 18, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-202"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_210-215"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_220-223"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 8, 8]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}, {"code": 17, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 0]}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロ - ヤリゾー宿屋に来る Lvl4"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 243, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 7, 2, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 39, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 2, 9, 8]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-6"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["着替える"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 6, 8]}, {"code": 128, "indent": 0, "parameters": [113, 0, 0, 1, false]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 319, "indent": 0, "parameters": [2, 5, 113]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-journey", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-33"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ドギー"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-47"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["x-ray　ちんぽが子宮押し上げ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※X-ray 　ちんぽ引き下がり"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※X-ray　ちんぽが子宮を押し上げ"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-81"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-97"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※X-ray　ちんぽが子宮を押し上げ　連続交互"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [52, 52, 0, 0, 3]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [1619, 1619, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [1619, 1619, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [52, 52, 2, 0, 1]}, {"code": 111, "indent": 1, "parameters": [1, 52, 0, 0, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-102"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-123"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [1619, 1619, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130-132"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-142"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ちんぽ抜く"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["精子逆流"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 19, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-167"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 128, "indent": 0, "parameters": [113, 1, 0, 1, false]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 8, 8]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}, {"code": 17, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 0]}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロ - ヤリゾー宿屋に来る Lvl5"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 243, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 22, 2, 7, 2, 2]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 39, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 2, 9, 8]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["土下座"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 2)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "harmonic-dungeon3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["土下座　頭踏む"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "insert1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 2)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※騎乗位（ヤリゾーはエロ本読んでる）"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 3)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-60"]}, {"code": 108, "indent": 0, "parameters": ["※アクメサウンド"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70-72"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 3)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-84"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※主人公帰ってくる"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 231, "indent": 0, "parameters": [1, "BG-shop1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-203"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※扉前"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 231, "indent": 0, "parameters": [1, "BG-shop1-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_210-217"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※司祭扉から顔出す　（困り顔汗、目はにっこりでも口元に陰毛"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [108]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_230-231"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 22, 22, 5, 232, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_233-234"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※部屋ビューのセックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 20, 4)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_250-253"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※扉閉まる"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_260-261"]}, {"code": 108, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 203, "indent": 0, "parameters": [0, 0, 3, 8, 8]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}, {"code": 17, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 22, 3, 5, 2, 0]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 0]}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-relaxedlove", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 8}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["オープニング、司祭とセックスした翌日"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************"]}, {"code": 121, "indent": 0, "parameters": [8, 8, 1]}, {"code": 352, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [9, 9, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 314, "indent": 0, "parameters": [0, 0]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,100)"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,100)"]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, false]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-6"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7"]}, {"code": 121, "indent": 0, "parameters": [216, 216, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["OP-エロシーン　はじめてのセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,true);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_naked2", 0]}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 4, 4, 2]}, {"code": 201, "indent": 0, "parameters": [0, 22, 4, 5, 8, 0]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 0, "parameters": [9, 9, 0]}, {"code": 319, "indent": 0, "parameters": [2, 2, 0]}, {"code": 319, "indent": 0, "parameters": [2, 3, 0]}, {"code": 319, "indent": 0, "parameters": [2, 4, 0]}, {"code": 319, "indent": 0, "parameters": [2, 5, 0]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 245, "indent": 0, "parameters": [{"name": "Night", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_101\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_102\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 0, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ー------------------------------"]}, {"code": 408, "indent": 0, "parameters": ["こっからフェラチオシーン"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-60"]}, {"code": 108, "indent": 0, "parameters": ["★　表情をイき顔に変更"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_61"]}, {"code": 108, "indent": 0, "parameters": ["臭いかいでイく"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_201\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_202\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_62-65"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_81"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_91"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_95"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_weak_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-112"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-126"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_127-129"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 118, "indent": 0, "parameters": ["ループ"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_301\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_302\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_131-136"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["ループ"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 108, "indent": 0, "parameters": ["フラッシュアンド頭掴み　目はイき耐え"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 231, "indent": 0, "parameters": [20, "event_bj1_man-arm", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_150-152"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 231, "indent": 0, "parameters": [18, "event_bj1_zamen1", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_160-166"]}, {"code": 108, "indent": 0, "parameters": ["●司祭　しゃぶりながらアヘ目　司祭もイく"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_170-173"]}, {"code": 108, "indent": 0, "parameters": ["●司祭　疲れ果てた目"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_180"]}, {"code": 108, "indent": 0, "parameters": ["●　口もにゅ"]}, {"code": 235, "indent": 0, "parameters": [18]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_190-196"]}, {"code": 108, "indent": 0, "parameters": ["●口開け"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_401\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_402\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200-202"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_204"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_206"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 12]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [221]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-215"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-222"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-233"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["こっからセックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 3"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bed1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101-107"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_108-110"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map22_ev1_p5_501\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map22_ev1_p5_502\")"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_111-114"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_115"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [180]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_116"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_117-129"]}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"ero_taikyu\""]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_ALL_STOP"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 231, "indent": 0, "parameters": [9, "event-tetsunagikkusu-semen1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_131-140"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [221]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_141-143"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_144-147"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_148"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_149-152"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 0, "parameters": [[0, 255, 136, 255], 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_153-154"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 402]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ー------------------------------"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 319, "indent": 0, "parameters": [2, 2, 82]}, {"code": 319, "indent": 0, "parameters": [2, 3, 92]}, {"code": 319, "indent": 0, "parameters": [2, 4, 102]}, {"code": 319, "indent": 0, "parameters": [2, 5, 79]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["key = [22, 4, \"C\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key,false);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 22, 4, 4, 2, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 121, "indent": 0, "parameters": [215, 215, 0]}, {"code": 122, "indent": 0, "parameters": [4, 4, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [5, 5, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [6, 6, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 16}, null]}