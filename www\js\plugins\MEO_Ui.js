let $UI={};{$Require("fs");let e=new QuickCache;e.Set("ui_number_0",ImageManager.loadPicture("ui/ui_number_0")),e.Set("ui_number_1",ImageManager.loadPicture("ui/ui_number_1")),e.<PERSON>("ui_number_2",ImageManager.loadPicture("ui/ui_number_2")),e.<PERSON>("ui_number_3",ImageManager.loadPicture("ui/ui_number_3")),e.<PERSON>("ui_number_4",ImageManager.loadPicture("ui/ui_number_4")),e.<PERSON>("ui_number_5",ImageManager.loadPicture("ui/ui_number_5")),e.<PERSON>("ui_number_6",ImageManager.loadPicture("ui/ui_number_6")),e.<PERSON>("ui_number_7",ImageManager.loadPicture("ui/ui_number_7")),e.<PERSON>("ui_number_8",ImageManager.loadPicture("ui/ui_number_8")),e.<PERSON>("ui_number_9",ImageManager.loadPicture("ui/ui_number_9")),$UI.Cache=e;let t=$HTML.CreateComponent("ui");$UI.Component=t,$UI.Show=function(){this.Component.Show()},$UI.Hide=function(){this.Component.Hide()},$UI.DrawDays=function(e){this.Component.DrawDays(e)},$UI.SetAP=function(e){this.Component.UpdateAP(e)},$UI.SetHornyMeter=function(e){this.Component.UpdateHornyMeter(e)},$UI.SetLustMeter=function(e){this.Component.UpdateLustMeter(e)},$UI.SetPregnancyState=function(e){this.Component.UpdatePregnancyState(e)}}function SetDay(e){$UI.DrawDays(e)}function SetDayByVar(){SetDay(Var(4))}function SetAP(e){$UI.SetAP(e)}function SetAPByVar(){SetAP(Var(5))}function SetHornyMeter(e){$UI.SetHornyMeter(e)}function SetHornyMeterByVar(){SetHornyMeter(Var(85))}function SetLustMeter(e){$UI.SetLustMeter(e)}function SetLustMeterByVar(){SetLustMeter(Var(84))}function SetPregnancyState(e){$UI.SetPregnancyState(e)}function SetPregnancyStateByVar(){SetPregnancyState(Var(95))}function UpdateUIByVars(){SetDayByVar(),SetAPByVar(),SetHornyMeterByVar(),SetLustMeterByVar(),SetPregnancyStateByVar()}function UIShow(){$UI.Show()}function UIHide(){$UI.Hide()}