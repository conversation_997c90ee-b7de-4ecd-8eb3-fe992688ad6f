/*:
 * @plugindesc ver.1.0.0 マップ上にクエスト状況のHUDを表示（KNS_QuestListより下）
 * <AUTHOR>
 * 
 * @param invisibleHudSwitch
 * @text HUD非表示スイッチ
 * @type switch
 * @default 1
 * 
 * @param maxQuestNumber
 * @text 最大クエスト件数
 * @type number
 * @default 3
 * 
 * @param hudX
 * @text HUD・X座標
 * @type number
 * @default 960
 * @min -65536
 * @max 65535
 * 
 * @param hudY
 * @text HUD・Y座標
 * @type number
 * @default 128
 * @min -65536
 * @max 65535
 * 
 * @param hudWidth
 * @text HUD横幅
 * @type number
 * @default 320
 * 
 * @param questTitleFontSize
 * @text タイトル・フォントサイズ
 * @type number
 * @default 28
 * 
 * @param questTitlePaddingBottom
 * @text タイトル・パディング高さ
 * @type number
 * @default 4
 * 
 * @param questDescFontSize
 * @text 文章・フォントサイズ
 * @type number
 * @default 22
 * 
 * @param questDescPaddingBottom
 * @text 文章・パディング高さ
 * @type number
 * @default 32
 * 
 * @help
 * ※このプラグインはKNS_QuestList以降に設置してください。
 * マップ上にクエスト状況のHUDを表示します。
 * 
 * 【仕様】
 * ・イベント中・指定のスイッチがONのときHUDは非表示になります。
 * ・プレイヤーがウィンドウ上にいるとき、HUDの透明度が半分になります。
 * 
 * 【提供するスクリプト】
 * show_navi_window
 * hide_navi_window
 * 　HUDの表示状態を切り替えます。
 * 　（非表示スイッチを切り替える処理のみ行います）
 * 
 * update_quest_navi
 * 　HUDの表示内容を強制的に更新します。
 * 　（$gameVariables._dataなどでマップの更新をスキップした場合に使う）
 * 
 * 【更新履歴】
 * ver.1.0.0(2023-07-26)
 * - デモ
 * ver.1.1.0(2023-07-30)
 * - ナビに表示するクエストを選べるようになりました。
 */

//=======================================================
// new KNS_QuestHUD
//=======================================================
const KNS_QuestHUD = {
	name: "KNS_QuestHUD",
	param: null,
	set_navi_window_visibility: function(bool){
		if ($gameSwitches){
			$gameSwitches.setValue(this.param.invisibleHudSwitch, bool);
		}
	}
};

(function(){
	this.param = PluginManager.parameters(this.name);

	this.param.invisibleHudSwitch = Number(this.param.invisibleHudSwitch || 0);
	this.param.maxQuestNumber     = Number(this.param.maxQuestNumber || 0);
	this.param.hudX     = Number(this.param.hudX || 0);
	this.param.hudY     = Number(this.param.hudY || 0);
	this.param.hudWidth = Number(this.param.hudWidth || 0);

	this.param.questTitleFontSize      = Number(this.param.questTitleFontSize || 0);
	this.param.questTitlePaddingBottom = Number(this.param.questTitlePaddingBottom || 0);
	this.param.questDescFontSize       = Number(this.param.questDescFontSize || 0);
	this.param.questDescPaddingBottom  = Number(this.param.questDescPaddingBottom || 0);

	Object.defineProperties(window, {
		update_quest_navi: {
			get: function(){
				if ($gameTemp){
					$gameTemp.knsRequestQuestHudRefresh();
				}
			},
			configurable: true
		},
		show_navi_window: {
			get: this.set_navi_window_visibility.bind(this, false),
			configurable: true
		},
		hide_navi_window: {
			get: this.set_navi_window_visibility.bind(this, true),
			configurable: true
		}
	});
	
	
}).call(KNS_QuestHUD);


//=======================================================
// new Window_QuestHud
//=======================================================
class Window_QuestHud extends Window_Selectable{
	maxCols(){ return 1; }
	maxItems(){ return this._data ? this._data.length : 0; }
	lineHeight(){ return KNS_QuestHUD.param.questDescFontSize; }
	standardPadding(){ return 0; }
	initialize(rect){
		super.initialize(rect.x, rect.y, rect.width, rect.height);
		this.deactivate();
		this.opacity = 0;
		this.visible = false;
		this._knsItemY = 0;
	}
	makeItemList(){
		this._data = $gameSystem.knsGetQuestNaviList().map(function(quest_id){
			return quest_id ? $csvQuestList[quest_id] : null;
		});
	}

	knsIsVisible(){
		if ($gameSwitches.value(KNS_QuestHUD.param.invisibleHudSwitch)){
			return false;
		}
		return !$gameMap.isEventRunning();
	}
	update(){
		super.update();
		const visible = this.knsIsVisible();
		if (this.visible !== visible){
			this.visible = visible;
			$gameTemp.knsRequestQuestHudRefresh();
		}

		if (this.visible){
			const to = this.knsIsPlayerAroundWindow() ? 127 : 255;
			if (this.contentsOpacity !== to){
				this.contentsOpacity += this.contentsOpacity > to ? -8 : 8; 
			}
			if ($gameTemp.knsIsRequestedQuestHudRefresh()){
				this.refresh();
				$gameTemp.knsRequestQuestHudRefresh(false);
			}
		}
	}
	knsIsPlayerAroundWindow(){
		const pad = 24;
		const px = $gamePlayer.screenX();
		if (this.x <= px + pad && px < this.x + this.width + pad){
			const py = $gamePlayer.screenY();
			return this.y <= py + pad && py < this.y + this._knsItemY + pad;
		}
		return false;
	}
	refresh(){
		this._knsItemY = 0;
		this.makeItemList();
		super.refresh();
	}
	drawItem(index){
		const item = this._data[index];
		if (!item){ return; }
		this.knsDrawContent(item.title, KNS_QuestHUD.param.questTitleFontSize, true);
		this._knsItemY += KNS_QuestHUD.param.questTitlePaddingBottom;

		const progress = item.progress[KNS_QuestBase.getCurrentProgressIndex(item)];
		if (progress){
			this.knsDrawContent(progress[1], KNS_QuestHUD.param.questDescFontSize, false);
		}
		this._knsItemY += KNS_QuestHUD.param.questDescPaddingBottom;
	}
	knsDrawContent(text, fontSize, drawBack){
		this.contents.fontSize = fontSize;
		let y = this._knsItemY;
		this._knsItemY = this.knsDrawTextAutoline(text, 0, this._knsItemY);
		if (!drawBack){ return; }
		const ctx = this.contents._context;

		const grad = ctx.createLinearGradient(0, y, this.width, y);
		grad.addColorStop(0.000, '#00000000');
		grad.addColorStop(0.125, '#00000088');
		grad.addColorStop(0.875, '#00000088');
		grad.addColorStop(1.000, '#00000000');

		ctx.save();
		ctx.fillStyle = grad;
		ctx.globalCompositeOperation = 'destination-over';
		ctx.fillRect(0, y, this.width, this._knsItemY - y);
		ctx.restore();
	}
	resetFontSettings() {
		const size = this.contents.fontSize;
		super.resetFontSettings();
		this.contents.fontSize = size;
	}
};

(function(){
	//=======================================================
	// alias Game_Temp
	//=======================================================
	Game_Temp.prototype.knsRequestQuestHudRefresh = function(bool=true) {
		this._needsQuestHudRefresh = bool;
	};

	Game_Temp.prototype.knsIsRequestedQuestHudRefresh = function() {
		return this._needsQuestHudRefresh;
	};

	//=======================================================
	// alias Scene_Map
	//=======================================================
	const _Game_Map_requestRefresh = Game_Map.prototype.requestRefresh;
	Game_Map.prototype.requestRefresh = function(mapId) {
		_Game_Map_requestRefresh.apply(this, arguments);
		$gameTemp.knsRequestQuestHudRefresh();
	};

	//=======================================================
	// alias Scene_Map
	//=======================================================
	const _Scene_Map_createAllWindows = Scene_Map.prototype.createAllWindows;
	Scene_Map.prototype.createAllWindows = function(){
		_Scene_Map_createAllWindows.apply(this, arguments);
		this.knsCreateQuestHudWindow();
	}

	Scene_Map.prototype.knsCreateQuestHudWindow = function(){
		const p = KNS_QuestHUD.param;
		this._knsQuestHudWindow = new Window_QuestHud(new Rectangle(
			p.hudX, p.hudY, p.hudWidth, Graphics.height - p.hudY
		));
		this.addWindow(this._knsQuestHudWindow);
	}

	const _Scene_Map_callMenu = Scene_Map.prototype.callMenu;
	Scene_Map.prototype.callMenu = function() {
		this._windowLayer.removeChild(this._knsQuestHudWindow);
		_Scene_Map_callMenu.apply(this, arguments);
	};
	
	const _Scene_Map_snapForBattleBackground = Scene_Map.prototype.snapForBattleBackground;
	Scene_Map.prototype.snapForBattleBackground = function() {
		this._windowLayer.removeChild(this._knsQuestHudWindow);
		_Scene_Map_snapForBattleBackground.apply(this, arguments);
		this.addWindow(this._knsQuestHudWindow);
	};
})();