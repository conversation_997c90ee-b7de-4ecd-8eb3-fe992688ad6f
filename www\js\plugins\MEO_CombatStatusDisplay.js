let $CSDM,$dataCSDM,$CombatStatusDisplayManager,$dataCombatBattleDisplay;{function CombatStatusDisplayManager(t){this.X=t.root.X||0,this.Y=t.root.Y||0,this.ScaleX=t.root.ScaleX||1,this.ScaleY=t.root.ScaleY||1,this.Visible=t.root.Visible||!1,t.mc.Updater=CombatStatusDisplayManager.Updater.Actor,t.femc.Updater=CombatStatusDisplayManager.Updater.Actor,t.follower.Updater=CombatStatusDisplayManager.Updater.Actor,t.enemy.Updater=CombatStatusDisplayManager.Updater.Enemy,this.WindowMC=new Window_CombatStatusDisplay(t.mc),this.WindowFeMC=new Window_CombatStatusDisplay(t.femc),this.WindowFollower=new Window_CombatStatusDisplay(t.follower),this.WindowEnemy=new Window_CombatStatusDisplay(t.enemy),this.Root=new PIXI.Container,this.Root.update=function(){this.children.forEach(function(t){t.update&&t.update()})},this.Root.addChild(this.WindowMC),this.Root.addChild(this.WindowFeMC),this.Root.addChild(this.WindowFollower),this.Root.addChild(this.WindowEnemy),this.draw()}let t=function(t){let a=t.Actor;t.HP_StatusPoints.Value=a._hp,t.HP_StatusPoints.Max=a.mhp,t.HP_Bar.Value=a._hp/a.mhp,t.MP_StatusPoints.Value=a._mp,t.MP_StatusPoints.Max=a.mmp,t.MP_Bar.Value=a._mp/a.mmp,t.FP_StatusPoints.Value=a._tp,t.FP_StatusPoints.Max=100,t.FP_Bar.Value=a._tp/100},a=function(t){t.SetNameText(t.Actor._name)},s=function(t){t.HP_StatusPoints.Value=Var(123),t.HP_StatusPoints.Max=Var(122),t.HP_Bar.Value=Var(123)/Var(122),t.MP_StatusPoints.Value=Var(125),t.MP_StatusPoints.Max=Var(124),t.MP_Bar.Value=Var(125)/Var(124)},i=function(t){t.SetNameText(Var(139))},o=function(t){t.draw()};function Window_CombatStatusDisplay(t){let a=t.X||0,s=t.Y||0,i=t.Width||200,o=t.Height||200;this.X=a,this.Y=s,this.Width=i,this.Height=o,this.ScaleX=t.ScaleX||1,this.ScaleY=t.ScaleY||1,this.Visible=t.Visible||!1,this.Actor=null,this.WindowSkin=t.WindowSkin,this.Updater=t.Updater,Window_Base.prototype.initialize.call(this,a,s,i,o),this.Components=[],this.Background=new Component_Image(Object.assign({Parent:this},t.Background)),this.Portrait=new Component_Image(Object.assign({Parent:this},t.Portrait)),this.Name=new Component_Text(Object.assign({Parent:this},t.Name)),this.HP_StatusPoints=new Component_StatusPoints(Object.assign({Parent:this},t.HP_StatusPoints)),this.HP_Bar=new Component_Bar(Object.assign({Parent:this},t.HP_Bar)),this.MP_StatusPoints=new Component_StatusPoints(Object.assign({Parent:this},t.MP_StatusPoints)),this.MP_Bar=new Component_Bar(Object.assign({Parent:this},t.MP_Bar)),this.FP_StatusPoints=new Component_StatusPoints(Object.assign({Parent:this},t.FP_StatusPoints)),this.FP_Bar=new Component_Bar(Object.assign({Parent:this},t.FP_Bar)),this.StatusIcons=new Component_StatusIcons(Object.assign({Parent:this},t.StatusIcons)),this.Components.push(this.Background),this.Components.push(this.Portrait),this.Components.push(this.Name),this.Components.push(this.HP_StatusPoints),this.Components.push(this.HP_Bar),this.Components.push(this.MP_StatusPoints),this.Components.push(this.MP_Bar),this.Components.push(this.FP_StatusPoints),this.Components.push(this.FP_Bar),this.Components.push(this.StatusIcons),this.draw()}CombatStatusDisplayManager.Updater={},CombatStatusDisplayManager.Updater.Actor={UpdateValues(a){t(a),o(a)},UpdateName(t){a(t),o(t)}},CombatStatusDisplayManager.Updater.Enemy={UpdateValues(t){s(t),o(t)},UpdateName(t){i(t),o(t)}},CombatStatusDisplayManager.prototype.UpdateSprites=function(){this.Root.x=this.X,this.Root.y=this.Y,this.Root.scale.set(this.ScaleX,this.ScaleY),this.Root.visible=this.Visible},CombatStatusDisplayManager.prototype.draw=function(){this.UpdateSprites()},CombatStatusDisplayManager.prototype.update=function(){},CombatStatusDisplayManager.prototype.AddToScene=function(t){t.addWindow(this.Root)},CombatStatusDisplayManager.prototype.AddToCurrentScene=function(){SceneManager._scene.addWindow(this.Root)},CombatStatusDisplayManager.prototype.SetActorsByParty=function(t=!0){let a=$gameParty._actors,s=$gameActors._data[a[0]],i=$gameActors._data[a[1]],o=$gameActors._data[a[2]];s&&this.WindowMC.SetActor(s),i&&this.WindowFeMC.SetActor(i),o&&this.WindowFollower.SetActor(o),t&&(s?this.WindowMC.Show():this.WindowMC.Hide(),i?this.WindowFeMC.Show():this.WindowFeMC.Hide(),o?this.WindowFollower.Show():this.WindowFollower.Hide())},CombatStatusDisplayManager.prototype.UpdateNameAll=function(){this.WindowMC.UpdateName(),this.WindowFeMC.UpdateName(),this.WindowFollower.UpdateName(),this.WindowEnemy.UpdateName()},CombatStatusDisplayManager.prototype.UpdateValuesAll=function(){this.WindowMC.UpdateValues(),this.WindowFeMC.UpdateValues(),this.WindowFollower.UpdateValues(),this.WindowEnemy.UpdateValues()},CombatStatusDisplayManager.prototype.SetVisible=function(t){this.Visible=t,this.UpdateSprites()},CombatStatusDisplayManager.prototype.SetPosition=function(t,a){this.X=t,this.Y=a,this.UpdateSprites()},CombatStatusDisplayManager.prototype.SetScale=function(t,a){this.ScaleX=t,this.ScaleY=a,this.UpdateSprites()},Window_CombatStatusDisplay.prototype=Object.create(Window_Base.prototype),Window_CombatStatusDisplay.prototype.constructor=Window_CombatStatusDisplay,Window_CombatStatusDisplay.prototype.loadWindowskin=function(){this.windowskin=ImageManager.loadSystem(this.WindowSkin)},Window_CombatStatusDisplay.prototype.UpdateSprites=function(){this.x=this.X,this.y=this.Y,this.width=this.Width,this.height=this.Height,this.scale.set(this.ScaleX,this.ScaleY),this.visible=this.Visible},Window_CombatStatusDisplay.prototype.draw=function(){for(let t=0;t<this.Components.length;t++)this.Components[t].draw&&this.Components[t].draw();this.UpdateSprites()},Window_CombatStatusDisplay.prototype.update=function(){for(let t=0;t<this.Components.length;t++)this.Components[t].update&&this.Components[t].update()},Window_CombatStatusDisplay.prototype.UpdateValues=function(){this.Updater&&this.Actor&&this.Updater.UpdateValues(this)},Window_CombatStatusDisplay.prototype.UpdateName=function(){this.Updater&&this.Actor&&this.Updater.UpdateName(this)},Window_CombatStatusDisplay.prototype.Show=function(){this.Visible=!0,this.draw()},Window_CombatStatusDisplay.prototype.Hide=function(){this.Visible=!1,this.draw()},Window_CombatStatusDisplay.prototype.SetActor=function(t){this.Actor=t,this.draw()},Window_CombatStatusDisplay.prototype.SetPortrait=function(t){this.Portrait.SetImage(t),this.draw()},Window_CombatStatusDisplay.prototype.SetNameText=function(t,a=!1){this.Name.Text=a?t:"- "+t+" -",this.draw()},CombatStatusDisplayManager.prototype.SetVisible=function(t){this.Visible=t,this.UpdateSprites()},CombatStatusDisplayManager.prototype.SetPosition=function(t,a){this.X=t,this.Y=a,this.UpdateSprites()},CombatStatusDisplayManager.prototype.SetScale=function(t,a){this.ScaleX=t,this.ScaleY=a,this.UpdateSprites()},Window_CombatStatusDisplay.prototype.SetHP=function(t,a){this.HP_StatusPoints.Value=t,this.HP_StatusPoints.Max=a,this.HP_Bar.Value=this.HP_StatusPoints.Value/this.HP_StatusPoints.Max,this.draw()},Window_CombatStatusDisplay.prototype.SetMP=function(t,a){this.MP_StatusPoints.Value=t,this.MP_StatusPoints.Max=a,this.MP_Bar.Value=this.MP_StatusPoints.Value/this.MP_StatusPoints.Max,this.draw()},Window_CombatStatusDisplay.prototype.SetFP=function(t,a){this.FP_StatusPoints.Value=t,this.FP_StatusPoints.Max=a,this.FP_Bar.Value=this.FP_StatusPoints.Value/this.FP_StatusPoints.Max,this.draw()};let e=Scene_Boot.prototype.start;Scene_Boot.prototype.start=function(){e.call(this,arguments),$CSDM=$CombatStatusDisplayManager=new CombatStatusDisplayManager($dataCombatBattleDisplay)},$dataCSDM=$dataCombatBattleDisplay={},$dataCombatBattleDisplay.root=JSON.parse(LoadFileUTF8Escaped("./data/CombatBattleDisplay/CombatBattleDisplay.json")),$dataCombatBattleDisplay.mc=JSON.parse(LoadFileUTF8Escaped("./data/CombatBattleDisplay/MC.json")),$dataCombatBattleDisplay.femc=JSON.parse(LoadFileUTF8Escaped("./data/CombatBattleDisplay/FEMC.json")),$dataCombatBattleDisplay.follower=JSON.parse(LoadFileUTF8Escaped("./data/CombatBattleDisplay/Follower.json")),$dataCombatBattleDisplay.enemy=JSON.parse(LoadFileUTF8Escaped("./data/CombatBattleDisplay/Enemy.json"))}