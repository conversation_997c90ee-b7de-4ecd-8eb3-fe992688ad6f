/*:
 * @plugindesc 錬金/鍛冶システムを実装します
 * <AUTHOR>
 * 
 * @param itemConfig
 * @text ■錬金
 * 
 * @param itemCraftAddVarId
 * @parent itemConfig
 * @text 成功率加算変数
 * @type variable
 * @default 3
 * 
 * @param itemCraftSucceedSe
 * @parent itemConfig
 * @text 錬金成功SE
 * @type struct<Se>
 * @default {"name":"Hammer","volume":"80","pitch":"100","pan":"0"}
 * 
 * @param itemCraftFailedSe
 * @parent itemConfig
 * @text 錬金失敗SE
 * @type struct<Se>
 * @default {"name":"Miss","volume":"80","pitch":"100","pan":"0"}
 * 
 * @param itemAdditionalCost
 * @parent itemConfig
 * @text 錬金追加コストスイッチ
 * @type struct<Additionalcost>[]
 * @default []
 * 
 * 
 * @param weaponConfig
 * @text ■鍛冶
 * 
 * @param weaponCraftAddVarId
 * @parent weaponConfig
 * @text 成功率加算変数
 * @type variable
 * @default 4
 * 
 * @param weaponCraftSucceedSe
 * @parent weaponConfig
 * @text 鍛冶成功SE
 * @type struct<Se>
 * @default {"name":"Hammer","volume":"80","pitch":"100","pan":"0"}
 * 
 * @param weaponCraftFailedSe
 * @parent weaponConfig
 * @text 鍛冶失敗SE
 * @type struct<Se>
 * @default {"name":"Miss","volume":"80","pitch":"100","pan":"0"}
 * 
 * @param weaponAdditionalCost
 * @parent weaponConfig
 * @text 鍛冶追加コストスイッチ
 * @type struct<Additionalcost>[]
 * @default []
 * 
 * 
 * @param itemHelpRect
 * @text ■ヘルプウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"0","width":"1280","height":"96"}
 * 
 * @param itemHelpRect
 * @text ■ヘルプウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"0","width":"1280","height":"96"}
 * 
 * @param helpFontSize
 * @parent itemHelpRect
 * @text フォントサイズ
 * @type number
 * @default 32
 * 
 * 
 * @param itemListRect
 * @text ■リストウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"96","width":"360","height":"792"}
 * 
 * @param itemGoldRect
 * @text ■所持金ウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"0","y":"888","width":"360","height":"72"}
 * 
 * @param itemInfoRect
 * @text ■詳細ウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"360","y":"96","width":"920","height":"864"}
 * 
 * @param infoLargeFontSize
 * @parent itemInfoRect
 * @text 見出しフォントサイズ
 * @type number
 * @default 36
 * 
 * @param infoSmallFontSize
 * @parent itemInfoRect
 * @text 通常フォントサイズ
 * @type number
 * @default 28
 * 
 * @param infoHorzPadding
 * @parent itemInfoRect
 * @text 横方向パディング
 * @type number
 * @default 128
 * 
 * @param infoTitleY
 * @parent itemInfoRect
 * @text 見出しY座標
 * @type number
 * @default 12
 * 
 * @param infoDescY
 * @parent itemInfoRect
 * @text 説明文Y座標
 * @type number
 * @default 78
 * 
 * @param infoRequirementY
 * @parent itemInfoRect
 * @text 合成素材Y座標
 * @type number
 * @default 330
 * 
 * @param infoBottomY
 * @parent itemInfoRect
 * @text 成功率Y座標
 * @type number
 * @default 690
 * 
 * @param infoExecuteRect
 * @parent itemInfoRect
 * @text 実行ボタン座標
 * @type struct<Rectangle>
 * @default {"x":"500","y":"690","width":"270","height":"36"}
 * 
 * 
 * @param infoBorders
 * @parent itemInfoRect
 * @text ボーダー座標
 * @type number[]
 * @default ["66","680"]
 * 
 * @param infoBorderColor
 * @parent itemInfoRect
 * @text ボーダーカラー
 * @default #ffffff88
 * 
 * 
 * @param itemDialogRect
 * @text ■ダイアログウィンドウ
 * @type struct<Rectangle>
 * @default {"x":"320","y":"300","width":"640","height":"360"}
 * 
 * @param dialogFontSize
 * @parent itemDialogRect
 * @text フォントサイズ
 * @type number
 * @default 32
 * 
 * 
 * @help
 * ■セットアップ
 * 1. プラグインパラメータ「錬金、鍛冶」の「成功率加算変数」に
 * 　　それぞれの成功率に加算する変数IDを指定する
 * 
 * 2. logsheet.csvに以下の必要文言を追加
 * craft_weapon_title,鍛冶,,,,,
 * craft_item_title,錬金,,,,,
 * craft_weapon_succeed,"%1
 * の鍛冶に成功した！",,,,,
 * craft_item_succeed,"%1
 * の錬金に成功した！",,,,,
 * craft_weapon_failed,"%1
 * の鍛冶に失敗した……",,,,,
 * craft_item_failed,"%1
 * の錬金に失敗した……",,,,,
 * craft_weapon_requirement,■鍛冶に必要なアイテム,,,,,
 * craft_item_requirement,■錬金に必要なアイテム,,,,,
 * craft_weapon_successRate,■鍛冶成功率,,,,,
 * craft_item_successRate,■錬金成功率,,,,,
 * craft_weapon_create,[ 作成する ],,,,,
 * craft_item_create,[ 錬金する ],,,,,
 * craft_dialog_ok,OK,,,,,
 * 
 * 
 * ■プラグインコマンド
 * KNS_Craft item
 * 　錬金画面を表示します
 * 
 * KNS_Craft weapon
 * 　鍛冶画面を表示します
 * 
 * 
 * ■アイテムノート
 * 　以下の形式で合成後のアイテムに記述。
 * <KNS_Craft target {
 *   // 鍛冶で付与する装備と抽選確率を指定（省略時は合成せず出力）
 *   // 確率の合計が100%に満たない場合は何も選ばれない
 *   // IDのみ記載した場合は残りの確率から抽選する
 *   prefix: [[ID, 確率], [ID, 確率], [ID, 確率]...],
 *   suffix: [[ID, 確率], [ID, 確率], [ID, 確率]...],
 *   // 合成で追加されるアイテム数を指定、省略時は１。
 *   exportSize: 個数,
 *   // アイテム・武器合成の基礎成功率を指定します。
 *   successRate: 百分率,
 *   // 合成の際に消費するアイテム・通貨を指定します。
 *   cost: [
 *     ['item', 1, 2], // 分類、ID、個数
 *     ['weapon', 1, 2],
 *     ['armor', 1, 2],
 *     ['gold', 1000], // 所持金, 必要G
 *   ],
 *   // 鍛冶・合成画面で表示する条件
 *   displayConditions: [
 *     ['gold', 100], // 所持金, 必要G
 *     ['switch', 1], // スイッチ、ID
 *     ['variable', 1, 10], // 変数、ID、条件値（変数1が10以上であれば表示）
 *     ['item', 1], // 分類、ID、個数（省略時は１個）
 *     ['weapon', 1],
 *     ['armor', 1],
 *   ],
 * }>
 * 
 * 例）
 * <KNS_Craft target {
 *   prefix: [[8,70],[9,30]],    // 8,9いずれかをPrefixに指定
 *   suffix: [[25]],     // 25をSuffixに指定
 *   exportSize: 2,    // 合成後アイテムを2個出力する
 *   successRate: 100, // 成功率を100％に指定する
 *   cost: [
 *     ['gold', 500],  // 合成の際に500G必要とする
 *   ],
 * }>
 * 
 * 
 * <KNS_Craft plusSuccessRate [カテゴリー item|weapon] [百分率]>
 * 　アイテム・スキルのメモ欄に使用。
 * 　パーティが所持している数に応じて百分率の値が成功率に加算されます。
 * 例）
 * 　<KNS_Craft plusSuccessRate item 25>
 * 　　錬金成功率を25%あげる
 * 　<KNS_Craft plusSuccessRate weapon -30>
 * 　　鍛冶成功率を30%さげる
 */
/*~struct~Rectangle:
 * @param x
 * @text X座標
 * @type number
 * @min -9999999
 * @max 9999999
 * @default 0
 * 
 * @param y
 * @text Y座標
 * @type number
 * @min -9999999
 * @max 9999999
 * @default 0
 * 
 * @param width
 * @text 横幅
 * @type number
 * @min -9999999
 * @max 9999999
 * @default 0
 * 
 * @param height
 * @text 高さ
 * @type number
 * @min -9999999
 * @max 9999999
 * @default 0
 */
/*~struct~Se:
 * @param name
 * @type file
 * @dir audio/se
 * @default Miss
 * 
 * @param volume
 * @type number
 * @default 80
 * 
 * @param pitch
 * @type number
 * @default 100
 * 
 * @param pan
 * @type number
 * @min -100
 * @max -100
 * @default 0
 */
/*~struct~AdditionalCost:
 * @param switchId
 * @type switch
 * @default 1
 * 
 * @param value
 * @type number
 * @min -99999999
 * @max 99999999
 * @default 100
 */

const KNS_Craft = {
    name: 'KNS_Craft',
    param: null,
    exports: {},
    enumItemType: {
        switch: 'switch',
        variable: 'variable',
        gold: 'gold',
        item: 'item',
        weapon: 'weapon',
        armor: 'armor'
    },
    parseRectangle(key) {
        const { x, y, width, height } = JsonEx.parse(this.param[key] || "{}");
        this.param[key] = {
            x: Number(x) || 0,
            y: Number(y) || 0,
            width: Number(width) || 0,
            height: Number(height) || 0,
        };
    },
    parseSe(key) {
        const { name, volume, pitch, pan } = JsonEx.parse(this.param[key] || "{}");
        this.param[key] = { name, volume: Number(volume), pitch: Number(pitch), pan: Number(pan) };
    },
    parseAdditionalCost(key) {
        this.param[key] = JsonEx.parse(this.param[key]).map(cost => {
            const { switchId, value } = JsonEx.parse(cost);
            return {
                switchId: Number(switchId),
                value: Number(value),
            };
        });
    },
    parseNumber(key) {
        this.param[key] = Number(this.param[key]);
    },
    parseString(key) {
        this.param[key] = String(this.param[key]);
    },
    getTargetInfo(item, category) {
        if (item && /<KNS_Craft target (\{[\s\S]+?\})>/.test(item.note)) {
            const info = eval(`(${RegExp.$1})`);
            if (info.cost) {
                info.cost = info.cost.map(([type, value, num]) => {
                    const data = {
                        category,
                        type,
                        data: null,
                        num: 0,
                        isGold: type === this.enumItemType.gold,
                        getPossessionNum() {
                            if (this.isGold) return $gameParty.gold();
                            return $gameParty.numIndependentItems(this.data);
                        },
                        getActualNum() {
                            if (this.isGold) {
                                let sum = this.num;
                                KNS_Craft.param[`${this.category}AdditionalCost`].forEach(({ switchId, value }) => {
                                    if ($gameSwitches.value(switchId)) sum += value;
                                });
                                return Math.max(0, sum);
                            }
                            return this.num;
                        },
                        canCost() {
                            return this.getPossessionNum() >= this.getActualNum();
                        },
                    };
                    if (data.isGold) {
                        data.num = value || 0;
                    } else {
                        data.num = num || 1;
                        switch(type) {
                            case this.enumItemType.item:   data.data = $dataItems[value];   break;
                            case this.enumItemType.weapon: data.data = $dataWeapons[value]; break;
                            case this.enumItemType.armor:  data.data = $dataArmors[value];  break;
                        };
                    }
                    return data;
                });
            }
            if (info.displayConditions) {
                info.displayConditions = info.displayConditions.map(([type, id, value = 1]) => {
                    return {
                        type,
                        id,
                        value: type === KNS_Craft.enumItemType.gold ? id : value,
                        isMatched() {
                            const types = KNS_Craft.enumItemType;
                            switch(this.type) {
                                case types.switch:   return $gameSwitches.value(this.id);
                                case types.variable: return $gameVariables.value(this.id) >= this.value;
                                case types.gold:     return $gameParty.gold() >= this.id;
                                case types.item:     return $gameParty.numIndependentItems($dataItems[this.id]) >= this.value;
                                case types.weapon:   return $gameParty.numIndependentItems($dataWeapons[this.id]) >= this.value;
                                case types.armor:    return $gameParty.numIndependentItems($dataArmors[this.id]) >= this.value;
                            }
                        },
                    };
                });
            }
            return Object.assign(
                {
                    category,
                    id: item.id,
                    data: item,
                    prefix: [],
                    suffix: [],
                    displayConditions: [],
                    exportSize: 1,
                    successRate: 0,
                    cost: [],
                    prefixRates: KNS_Craft.parseCompositeRates(item, info.prefix || []),
                    suffixRates: KNS_Craft.parseCompositeRates(item, info.suffix || []),
                    calcActualSuccessRate() {
                        let sum = this.successRate;
                        if (this.category === KNS_Craft.enumItemType.item) {
                            sum += $gameVariables.value(KNS_Craft.param.itemCraftAddVarId);
                        } else {
                            sum += $gameVariables.value(KNS_Craft.param.weaponCraftAddVarId);
                        }
                        sum += $gameParty.knsGetCraftSuccessRate(this.category);
                        return Math.max(0, Math.min(sum, 100));
                    },
                    isEnabled() {
                        return this.cost.every(item => item.canCost());
                    },
                    isDisplayed() {
                        return this.displayConditions.every(cond => cond.isMatched());
                    },
                    selectCompositeItem(rates) {
                        if (rates.length === 0) return null;
                        let totalWeight = rates.reduce((r, item) => r + item.rate, 0);
                        let r = Math.randomInt(totalWeight);
                        const chooseRate = r;
                        const found = rates.find(({ rate }) => {
                            if (r < rate) return true;
                            r -= rate;
                        });
                        const getName = rate => rate && rate.item ? rate.item.name : "[NONE]";
                        console.log(
                            "%c[%1]%2: %3".format(
                                this.prefixRates === rates ? 'PREFIX' : 'SUFFIX',
                                this.data.id.padZero(3),
                                this.data.name,
                            ),
                            "color:white; background-color:purple; padding:2px; border-radius:4px;",
                            '\n抽選結果(%1%):%2'.format(
                                chooseRate,
                                rates.reduce((r, rate) => {
                                    return r + "\n%1%2(%3%%4)".format(
                                        found === rate ? '[!]' : ' - ',
                                        getName(rate),
                                        rate.rate,
                                        rate.autoCalced ? ', 自動計算' : ''
                                    );
                                }, ""),
                            ),
                        );
                        return found ? found.item : null;
                    },
                },
                info
            );
        }
        return null;
    },
    parseCompositeRates(item, items) {
        if (items.length === 0) return [];
        const isWeapon = DataManager.isWeapon(item);
        const isArmor = DataManager.isArmor(item);
        if (!isWeapon && !isArmor) return [];

        const list = isWeapon ? $dataWeapons : $dataArmors;
        let sum = 0, cnt = 0;
        items = items.map(item => {
            if (Array.isArray(item)) {
                let rate = item[1];
                if (rate) {
                    sum += rate;
                    cnt++;
                } else {
                    rate = -1;
                }
                return { item: list[item[0]], rate };
            }
            return { item: list[item], rate: -1 };
        });
        const randomMax = items.length - cnt;
        if (randomMax === 0 && sum < 100) {
            items.push({ item: null, rate: 100 - sum, autoCalced: true });
        } else {
            const rate = (100 - sum) / randomMax;
            items.forEach(item => {
                if (item.rate === -1) {
                    item.rate = rate;
                    item.autoCalced = true;
                }
            });
        }
        items.sort((a, b) => b.rate - a.rate);
        return items;
    },
    getCraftSuccessRate(item, category) {
        if (
            item &&
            new RegExp(`<KNS_Craft\\s+plusSuccessRate\\s+${category}\\s+(-?\\d+)>`).test(item.note)
        ) {
            return Number(RegExp.$1);
        }
        return 0;
    },
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.parseSe('itemCraftSucceedSe');
    this.parseSe('itemCraftFailedSe');
    this.parseSe('weaponCraftSucceedSe');
    this.parseSe('weaponCraftFailedSe');
    this.parseAdditionalCost('itemAdditionalCost');
    this.parseAdditionalCost('weaponAdditionalCost');

    this.parseRectangle('itemHelpRect');
    this.parseRectangle('itemListRect');
    this.parseRectangle('itemGoldRect');
    this.parseRectangle('itemInfoRect');
    this.parseRectangle('itemDialogRect');
    this.parseRectangle('infoExecuteRect');

    this.parseNumber('itemCraftAddVarId');
    this.parseNumber('weaponCraftAddVarId');

    this.parseNumber('dialogFontSize');
    this.parseNumber('helpFontSize');
    this.parseNumber('infoLargeFontSize');
    this.parseNumber('infoSmallFontSize');
    this.parseString('infoBorderColor');

    this.parseNumber('infoHorzPadding');
    this.parseNumber('infoTitleY');
    this.parseNumber('infoDescY');
    this.parseNumber('infoRequirementY');
    this.parseNumber('infoBottomY');
    this.param.infoBorders = JsonEx.parse(this.param.infoBorders).map(n => Number(n));

    //=======================================================
    // alias Game_Interpreter
    //=======================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_Craft.name){ return; }
        switch ((args[0] || "").toLowerCase()){
            case KNS_Craft.enumItemType.item:   SceneManager.push(Scene_ItemCraft); break;
			case KNS_Craft.enumItemType.weapon: SceneManager.push(Scene_WeaponCraft); break;
        }
    };

    //=======================================================
    // alias Game_Party
    //=======================================================
    Game_Party.prototype.knsGetCraftSuccessRate = function(category) {
        let sum = 0;
        sum += this.allItems().reduce((r, item) => {
            return r + KNS_Craft.getCraftSuccessRate(item, category) * this.numIndependentItems(item);
        }, 0);
        sum += this.allMembers().reduce((r, actor) => r + actor.knsGetCraftSuccessRate(category), 0)
        return sum;
    }

    //=======================================================
    // alias Game_Party
    //=======================================================
    Game_Actor.prototype.knsGetCraftSuccessRate = function(category) {
        return this.skills().reduce((r, item) => r + KNS_Craft.getCraftSuccessRate(item, category), 0);
    }

    //=======================================================
    // new Window_CraftItemList
    //=======================================================
    class Window_CraftItemList extends Window_Selectable {
        initialize(category, { x, y, width, height }) {
            super.initialize(x, y, width, height);
            this._data = [];
            this._category = category;
            this.knsMakeCache();

            this.refresh();
            this.activate();
            this.select(0);
        }
        maxCols() { return 1; }
        maxItems() { return this._data ? this._data.length : 1; }
        item() { return this._data ? this._data[this.index()] || null : null; }
        isCurrentItemEnabled() {
            const cache = this.item();
            return cache && cache.isEnabled();
        }
        updateHelp() {
            this._helpWindow.setItem(this.item());
        };

        knsMakeCache() {
            let list = [];
            if (this._category === KNS_Craft.enumItemType.item) {
                list = $dataItems.filter(item => item && !item.baseItemId);
            }
            if (this._category === KNS_Craft.enumItemType.weapon) {
                list.push(
                    ...$dataWeapons.filter(item => item && !item.baseItemId),
                    ...$dataArmors.filter(item => item && !item.baseItemId),
                );
            }
            this._knsCache = list
                .map(item => KNS_Craft.getTargetInfo(item, this._category))
                .filter(item => item);
        }
        refresh() {
            this._data = this._knsCache.filter(cache => cache.isDisplayed());
            this.createContents();
            this.drawAllItems();
        };
        craftItem(cache, isSuccess) {
            cache.cost.forEach(cost => {
                if (cost.isGold) {
                    $gameParty.loseGold(cost.getActualNum());
                } else {
                    $gameParty.loseItem(cost.data, cost.getActualNum());
                }
            });
            if (isSuccess) {
                const exports = [];
                for (let i = 0; i < cache.exportSize; i++) {
                    const selectedPrefix = cache.selectCompositeItem(cache.prefixRates);
                    const selectedSuffix = cache.selectCompositeItem(cache.suffixRates);
                    if (selectedPrefix || selectedSuffix) {
                        const weapon = DreamX.RandomPrefixSuffix.knsMakeSpecifiedItem(
                            cache.data, selectedPrefix, selectedSuffix
                        );
                        $gameParty.gainItem(weapon, +1);
                        DataManager.registerNewItem(weapon);
                        exports.push(weapon);
                    } else {
                        $gameParty.gainItem(cache.data, 1);
                        exports.push(cache.data);
                    }
                }
                return exports;
            } else {
                return [cache.data];
            }
        }

        drawItem(index) {
            const cache = this._data[index];
            if (cache) {
                const { x, y, width } = this.itemRect(index);
                this.changePaintOpacity(cache.isEnabled());
                this.drawItemName(cache.data, x, y, width);
            }
        }
    };


    //=======================================================
    // new Window_CraftItemInfo
    //=======================================================
    class Window_CraftItemInfo extends Window_Selectable {
        initialize(category, { x, y, width, height }) {
            super.initialize(x, y, width, height);
            this._knsCategory = category;
        }
        standardFontSize() { return KNS_Craft.param.infoSmallFontSize; }
        setItem(cache) {
            this.contents.clear();
            KNS_Craft.param.infoBorders.forEach(y => {
                this.contents.fillRect(0, y, this.contents.width, 2, KNS_Craft.param.infoBorderColor);
            });
            if (!cache) return;
            this.drawTitle(cache);

            const padX = KNS_Craft.param.infoHorzPadding;
            const contentWidth = this.contents.width - padX * 2;
            this.drawLongDescription(cache.data, padX, contentWidth);
            this.drawAllCosts(cache, padX, contentWidth);
            this.drawSuccessRate(cache, padX);
            this.drawButton();
        }
        drawTitle(cache) {
            const item = cache.data;
            const title = `${item.name} × ${cache.exportSize}`;
            this.contents.fontSize = KNS_Craft.param.infoLargeFontSize;
            const width = this.contents.fontSize + this.textWidth(title);
            const x = this.contents.width - width >> 1;
            const y = KNS_Craft.param.infoTitleY;
            this.drawIcon(item.iconIndex, x, y);
            this.drawText(title, x + this.contents.fontSize, y, width);
            this.contents.fontSize = this.standardFontSize();
        }
        drawLongDescription(item, padX, contentWidth) {
            this.knsDrawTextAutoline(
                item.description || "",
                padX, KNS_Craft.param.infoDescY, contentWidth + padX
            );
            this.resetFontSettings();
        }
        drawAllCosts(cache, padX, contentWidth) {
            let y = KNS_Craft.param.infoRequirementY;
            this.changeTextColor(this.systemColor());
            this.drawText($LogSheetCSV.Get(`craft_${this._knsCategory}_requirement`), padX, y, contentWidth);
            this.changeTextColor(this.normalColor());
            cache.cost.forEach(cost => {
                y += this.lineHeight();
                this.drawCost(cost, padX, y, contentWidth);
            });
        }
        drawCost(item, x, y, contentWidth) {
            this.changePaintOpacity(item.canCost());
            if (item.isGold) {
                this.drawText(item.getActualNum(), x, y, contentWidth);
                this.changeTextColor(this.systemColor());
                this.drawText(TextManager.currencyUnit, x + this.textWidth(item.getActualNum()), y, contentWidth);
            } else {
                const possessionWidth = this.textWidth('000)');
                const numberWidth = this.textWidth('×000(');
                const allWidth = numberWidth + possessionWidth;
                this.drawItemName(item.data, x, y, contentWidth - allWidth);
                this.drawText('×', x, y, contentWidth - allWidth, 'right');
                this.drawText(`${item.getActualNum()}(`, x - possessionWidth, y, contentWidth, 'right');
                this.drawText(`${item.getPossessionNum()})`, x, y, contentWidth, 'right');
            }
            this.changePaintOpacity(true);
            this.resetFontSettings();
        }
        drawSuccessRate(cache, padX) {
            const width = 280;
            const y = KNS_Craft.param.infoBottomY;
            this.changeTextColor(this.systemColor());
            this.drawText($LogSheetCSV.Get(`craft_${this._knsCategory}_successRate`), padX, y, width, 'left');
            this.changeTextColor(this.normalColor());
            this.drawText(`${cache.calcActualSuccessRate()}%`, padX, y, width, 'right');
        }
        drawButton() {
            const { x, y, width } = this.itemRect(0);
            this.drawText($LogSheetCSV.Get(`craft_${this._knsCategory}_create`), x, y, width, 'center');
        }
        itemRect(index) {
            if (index === 0) { return KNS_Craft.param.infoExecuteRect; }
            return new Rectangle(0, 0, 0, 0);
        }
    };

    //=======================================================
    // new Window_Dialog
    //=======================================================
    class Window_Dialog extends Window_Selectable {
        initialize(text, { x, y, width, height }) {
            super.initialize(x, y, width, height);
            this.openness = 0;
            this.select(0);
            this.activate();
            this.open();

            this.setText(text);
        }
        maxItems() { return 1; }
        itemRect() {
            const height = this.itemHeight();
            return new Rectangle(0, this.contents.height - height, this.contents.width, height);
        }
        setText(text) {
            const { x, y, width } = this.itemRect(0);
            this.drawText($LogSheetCSV.Get('craft_dialog_ok'), x, y, width, 'center');

            const lines = text.split('\n');
            this.contents.fontSize = KNS_Craft.param.dialogFontSize;
            const lineHeight = KNS_Craft.param.dialogFontSize + 4;
            let lineY = y - lineHeight * lines.length >> 1;
            lines.forEach(line => {
                this.drawText(line, 0, lineY, width, 'center');
                lineY += lineHeight;
            });
        }
        updateClose() {
            const isClosing = this._closing;
            super.updateClose();
            if (isClosing && this.openness === 0) {
                this.emit('closeEnd');
            }
        }
    }

    //=======================================================
    // new Scene_CraftBase
    //=======================================================
    class Scene_CraftBase extends Scene_MenuBase{
        itemCategory() { return ''; }
        craftHelpWindowRect() { return KNS_Craft.param.itemHelpRect; }
        craftListWindowRect() { return KNS_Craft.param.itemListRect; }
        craftGoldWindowRect() { return KNS_Craft.param.itemGoldRect; }
        craftInfoWindowRect() { return KNS_Craft.param.itemInfoRect; }
        craftDialogWindowRect() { return KNS_Craft.param.itemDialogRect; }

        create() {
            super.create();
            this.createHelpWindow();

            const listWindow = new Window_CraftItemList(this.itemCategory(), this.craftListWindowRect());
            this.addWindow(listWindow);

            const infoWindow = new Window_CraftItemInfo(this.itemCategory(), this.craftInfoWindowRect());
            this.addWindow(infoWindow);

            const goldWindow = new Window_Gold(0, 0);
            Object.assign(goldWindow, this.craftGoldWindowRect());
            goldWindow.createContents();
            goldWindow.refresh();
            this.addWindow(goldWindow);

            listWindow.setHandler('ok', () => {
                infoWindow.select(0);
                infoWindow.activate();
            });
            listWindow.setHandler('cancel', this.popScene.bind(this));
            listWindow.setHelpWindow(infoWindow);

            infoWindow.setHandler('ok', this.execute.bind(this, listWindow, infoWindow, goldWindow));
            infoWindow.setHandler('cancel', () => {
                infoWindow.deselect();
                listWindow.activate();
            });
        }
        createHelpWindow() {
            const { x, y, width, height } = this.craftHelpWindowRect();
            const helpWindow = new Window_Base(x, y, width, height);
            helpWindow.contents.fontSize = KNS_Craft.param.helpFontSize;
            helpWindow.contents.drawText(
                $LogSheetCSV.Get(`craft_${this.itemCategory()}_title`),
                0, 0, helpWindow.contents.width, helpWindow.contents.height, 'center',
            );
            this.addWindow(helpWindow);
        }

        execute(listWindow, infoWindow, goldWindow) {
            const category = this.itemCategory();
            const cache = listWindow.item();
            const isSucceed = Math.random() <= cache.calcActualSuccessRate() / 100;
            const resultItems = listWindow.craftItem(cache, isSucceed);

            AudioManager.playSe(KNS_Craft.param[
                `${category}Craft${isSucceed ? 'Succeed' : 'Failed'}Se`
            ]);;
            this.showDialog(
                $LogSheetCSV.Get(`craft_${category}_${isSucceed ? 'succeed' : 'failed'}`)
                    .format(resultItems.map(item => item.name).join('\n')),
                dialog => {
                    infoWindow.deselect();
                    listWindow.refresh();
                    listWindow.activate();
                    goldWindow.refresh();
                    dialog.close();
                },
            );
        }
        showDialog(text, okHandler) {
            const dialog = new Window_Dialog(text, this.craftDialogWindowRect());
            dialog.setHandler('ok', okHandler.bind(this, dialog));
            dialog.setHandler('cancel', okHandler.bind(this, dialog));
            dialog.on('closeEnd', () => dialog.destroy({ children: true }));
            this.addWindow(dialog);
        }
    };

    //=======================================================
    // new Scene_ItemCraft
    //=======================================================
    class Scene_ItemCraft extends Scene_CraftBase {
        itemCategory() { return KNS_Craft.enumItemType.item; }
    };

    //=======================================================
    // new Scene_WeaponCraft
    //=======================================================
    class Scene_WeaponCraft extends Scene_CraftBase {
        itemCategory() { return KNS_Craft.enumItemType.weapon; }
    };

    this.exports = {
        Window_CraftItemList,
        Window_CraftItemInfo,
        Window_Dialog,
        Scene_CraftBase,
        Scene_ItemCraft,
        Scene_WeaponCraft,
    };
}).call(KNS_Craft);