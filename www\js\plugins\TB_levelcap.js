/*:
 * @plugindesc Set Maximum Level for Actors
 * <AUTHOR> Name
 *
 * @param Max Level
 * @type number
 * @min 1
 * @max 99
 * @desc The maximum level that actors can achieve.
 * @default 5
 *
 * @help This plugin sets the maximum level for all actors.
 */

(function() {
    var parameters = PluginManager.parameters('TB_levelcap');
    var maxLevel = parseInt(parameters['Max Level'] || 5);

    Game_Actor.prototype.changeExp = function(exp, show) {
        if (this.isMaxLevel()) {
            this._exp[this._classId] = this.currentExp();
        } else {
            this._exp[this._classId] = exp;
            this._exp[this._classId] = Math.max(this.currentExp(), 0);
        }
        
        var lastLevel = this._level;
        while (!this.isMaxLevel() && this.currentExp() >= this.nextLevelExp()) {
            this.levelUp();
        }
        while (this.currentExp() < this.currentLevelExp()) {
            this.levelDown();
        }
        if (show && this._level > lastLevel) {
            this.displayLevelUp(this.findNewSkills(lastLevel));
        }
        
        this.refresh();

        // Override max level
        if (this._level >= maxLevel) {
            this._level = maxLevel;
            this._exp[this._classId] = this.currentExp();
        }
    };
})();
