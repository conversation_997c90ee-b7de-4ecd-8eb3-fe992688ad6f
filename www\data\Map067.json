{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "トラップ - 落とし穴", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 5]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 落とし穴処理開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 1, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(67, 1, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$hole", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "トラップ - アナルねぶりスライム", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 落とし穴処理開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 2, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(\"トラップ結果:${$trap_activate}\")"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(67, 2, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "セクハラホームレス", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "harassment", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 10, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["セクハラホームレス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sekuhara_homeless_common1\")"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sekuhara_homeless_common2\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_intimidate\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_ignore\")"]}, {"code": 108, "indent": 0, "parameters": ["選択肢位置(0, 200)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["****************"]}, {"code": 408, "indent": 1, "parameters": ["筋力判定"]}, {"code": 408, "indent": 1, "parameters": ["****************"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"intimidate\""]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Run", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 214, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_sekuhara_homeless_common99\")"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツ触り"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 1, 0]}, {"code": 117, "indent": 1, "parameters": [801]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_sekuhara_homeless_grope_ass1\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_sekuhara_homeless_grope_ass2\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["ぶっかけ"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 2, 0]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 1, "parameters": [39, 39, 0, 1, 456]}, {"code": 122, "indent": 1, "parameters": [468, 468, 0, 2, 1, 3]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 2, 1, 10]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 2, 1, 10]}, {"code": 122, "indent": 1, "parameters": [467, 467, 0, 2, 1, 10]}, {"code": 117, "indent": 1, "parameters": [833]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_sekuhara_homeless_bukkake_ass1\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_sekuhara_homeless_bukkake_ass2\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 121, "indent": 1, "parameters": [144, 144, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Run", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sekuhara_homeless_common99\")"]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sekuhara_homeless_common100\")"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "harassment", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "トラップ-舐りヒル（乳首）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* イベント開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 4, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(67, 4, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "トラップ-舐りヒル（クリトリス）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* イベント開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 5, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(67, 5, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "トラップ-煙幕", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* イベント開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 6, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(67, 6, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "トラップ - イソギンチャク", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップ処理開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 7, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 7, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "トラップ - フラワー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップランク設定"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 判定処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [601]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* トラップ処理開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 8, 1)"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["* 終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["解除"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["発動後"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(`トラップ結果:${$trap_activate}`)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 170, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$trap_perception", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(69, 7, 1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}]}