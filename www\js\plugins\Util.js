function Access(t,e){"string"==typeof e&&(e=e.split(/[\s./]/));let n=t;for(let t=0;t<e.length;t++){if(null==n)return null;n=n[e[t]]}return null==n?null:n}function GetType(t){return t.__proto__.constructor.name}function StrMul(t,e){let n="";for(let o=0;o<e;o++)n+=t;return n}function Var(t){return $gameVariables._data[t]||0}function Vars(){let t=[];for(const e of arguments)t.push($gameVariables._data[e]||0);return t}function SetVar(t,e){$gameVariables._data[t]=e}function HexStringRandomFormat(t){let e="";for(let n=0;n<t.length;n++)e+="r"===t[n]?RandomInt(16).toString(16):t[n];return e}function RandomFloat(t=1){return Math.random()*t}function RandomInt(t=1){return Math.floor(Math.random()*t)}function RandomRange(t=1,e=t){return Math.random()*(e-t)+t}function RandomRangeInt(t=1,e=t){return Math.floor(Math.random()*(e+1-t))+t}function QuickCache(){this.Map=new Map}function TypeObj(t={}){let e=Object.keys(t);for(let n=0;n<e.length;n++){let o=e[n],r=t[o];"string"==typeof r&&(r=r.split(/[ ]+/));for(let t of r)this[t]=o}}function LoadFileUTF8Escaped(t){return $Node.fs.readFileSync(t,"utf8").replace(/\r/g,"")}Window_Base.prototype.drawBitmap=function(t,e,n,o=0,r=0,i=t.width,a=t.height){this.contents.blt(t,o,r,i,a,e,n,t.width,t.height)},Window_Base.prototype.drawPicture=function(t,e=0,n=0){let o=ImageManager.loadPicture(t);return this.contents.blt(o,0,0,o.width,o.height,e,n,o.width,o.height),o},Window_Base.prototype.drawBitmapCut=function(t,e=0,n=0,o=0,r=0,i,a){i=i||t.width,a=a||t.height,this.contents.blt(t,o,r,i,a,e,n,i,a)},Window_Base.prototype.drawPictureCut=function(t,e=0,n=0,o=0,r=0,i,a){let h=ImageManager.loadPicture(t);return i=i||h.width,a=a||h.height,this.contents.blt(h,o,r,i,a,e,n,i,a),h},Window_Base.prototype.drawLine=function(t,e,n=this.contents.width,o=2){},Window_Base.prototype.drawTextWrap=function(t,e,n,o=this.contents.width,r=0,i="left"){let a,h=[],s=[],l=[/^\n+/,/^[ ]+/,/^[^ |^\n]+/],c=t.length;for(let e=0;e<c;e++)for(let n=0;n<l.length;n++)(a=l[n].exec(t))&&(t=t.slice(a[0].length),e+=a[0].length-1,h.push(a[0]),s.push(n));let u=0,p=e,d=n,f=!1,g=this.contents.fontSize;for(let t=0;t<h.length;t++){let n=h[t],a=this.contents.measureTextWidth(n);((u+=a)>o||0===s[t])&&(p=e,d+=g+r,u=a,1!==s[t]&&0!==s[t]||(f=!0)),f||(this.contents.drawText(n,p,d,o,g,i),p+=a),f=!1}},Window_Base.prototype.drawTextWrapMeasured=function(){let t,e=[],n=[],o=[/^\n+/,/^[ ]+/,/^[^ |^\n]+/],r=text.length;for(let i=0;i<r;i++)for(let r=0;r<o.length;r++)(t=o[r].exec(text))&&(text=text.slice(t[0].length),i+=t[0].length-1,e.push(t[0]),n.push(r));let i=0,a=x,h=y,s=!1,l=this.contents.fontSize;for(let t=0;t<e.length;t++){let o=e[t],r=this.contents.measureTextWidth(o);((i+=r)>width||0===n[t])&&(a=x,h+=l+margin,i=r,1!==n[t]&&0!==n[t]||(s=!0)),s||(this.contents.drawText(o,a,h,width,l,align),a+=r),s=!1}},Game_Event.prototype.jumpTo=function(t,e){let n=this._x-t,o=this._y-e;this.jump(-n,-o)},Game_Event.prototype.restore=function(){this._erased=!1,this.refresh()},Game_Map.prototype.setTileId=function(t,e,n,o){let r=$dataMap.width,i=(n*$dataMap.height+e)*r+t;$dataMap.data[i]=o,SceneManager._scene.children[0]._tilemap.refresh()},QuickCache.prototype.Add=function(t,e=[]){let n;(n=this.Map.get(t))?this.Map.set(t,n.concat(e)):this.Map.set(t,e)},QuickCache.prototype.Set=function(t,e=[]){return this.Map.set(t,e)},QuickCache.prototype.Get=function(t){return this.Map.get(t)},QuickCache.prototype.ForEeach=function(t){let e,n=[];for(const o of this.Map.keys(this))(e=t(o,this.Map.get(o)))&&n.push(e);return n};