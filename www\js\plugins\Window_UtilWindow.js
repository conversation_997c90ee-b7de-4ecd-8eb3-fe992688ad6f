let $UtilWindow;function Window_UtilWindow(){this.initialize.apply(this,arguments)}Window_UtilWindow.prototype=Object.create(Window_Base.prototype),Window_UtilWindow.prototype.constructor=Window_UtilWindow;let base__Scene_Title__start=Scene_Title.prototype.start;Scene_Title.prototype.start=function(){base__Scene_Title__start.call(this),$UtilWindow=new Window_UtilWindow(0,0,Graphics.width,Graphics.height)};