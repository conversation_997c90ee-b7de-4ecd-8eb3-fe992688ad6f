/*:
    <AUTHOR>
    @plugindesc WMTS (World Map Travel System)

    @param Cursor

    @param Position
    @parent Cursor

    @param X
    @type number
    @default 0
    @parent Position

    @param Y
    @type number
    @default 0
    @parent Position

    @param SoundEffect
    @parent Cursor

    @param SoundName
    @type text
    @default Cursor1
    @parent SoundEffect

    @param SoundPitch
    @type number
    @default 100
    @parent SoundEffect

    @param SoundPan
    @type number
    @default 0
    @parent SoundEffect

    @param SoundVolume
    @type number
    @default 90
    @parent SoundEffect

    @param Delay
    @type number
    @min 1
    @max 120
    @default 15
    @parent Cursor

    @param MoveCE
    @type number
    @default 1

    @param Enabled
    @type text
    @default @default [1, 128]
*/
let $WMTS=null;{let t=new PluginParams("WMTS",new TypeObj({string:"SoundName",number:"X Y Delay SoundPan SoundPitch SoundVolume MoveCE",json:"Enabled"}));function WMTS(e=0,a=0,s=[]){if(this.X=e,this.Y=a,this.States=new Array(256).fill(!1),s)for(const t of s)this.Enable(t);this.Tiles=null,this.Regions=null,this.Cursor=null,this.CursorData=null,this.StateEvents={},this.CursorSE={name:t.SoundName,pan:t.SoundPan,pitch:t.SoundPitch,volume:t.SoundVolume},this.Path=[],this.PathIndex=1,this.IsMoving=!1,this.Pause=!1,this.Frame=0}WMTS.prototype.Save=function(){return{X:this.X,Y:this.Y,States:this.States}},WMTS.prototype.Load=function(t){this.X=t.X,this.Y=t.Y,this.States=t.States},WMTS.prototype.Set=function(t,e){this.States[t]=e},WMTS.prototype.Get=function(t){return this.States[t]},WMTS.prototype.Enable=function(t){this.States[t]=!0},WMTS.prototype.Disable=function(t){this.States[t]=!1},WMTS.prototype.EnableAll=function(){for(let t=0;t<this.States.length;t++)this.States[t]=!0},WMTS.prototype.DisableAll=function(){for(let t=0;t<this.States.length;t++)this.States[t]=!1},WMTS.prototype.Toggle=function(t){this.States[t]=!this.States[t]},WMTS.prototype.Prepare=function(){let t=$dataMap.events.length;for(let e=1;e<t;e++){let t=$dataMap.events[e];if(t){let e=t.name;if("Cursor"===e||"<WMTS:Cursor>"===e){this.CursorData=t,this.Cursor=$gameMap.event(t.id);break}}}this.Cursor.jumpTo(this.X,this.Y);let e=$dataMap.width,a=$dataMap.height,s=ArrayHelper.CreateArray(a,e);for(let t=0;t<a;t++)for(let a=0;a<e;a++)s[t][a]=$gameMap.tileId(a,t,3),$gameMap.setTileId(a,t,2,0);this.Tiles=s;let i=ArrayHelper.CreateArray(a,e);for(let t=0;t<a;t++)for(let a=0;a<e;a++)i[t][a]=$gameMap.regionId(a,t);this.Regions=i},WMTS.prototype.Render=function(){let t=$dataMap.width,e=$dataMap.height;for(let a=0;a<e;a++)for(let e=0;e<t;e++)$gameMap.setTileId(e,a,3,this.Get($gameMap.regionId(e,a))?this.Tiles[a][e]:0)},WMTS.prototype.Move=function(t,e){this.IsMoving||(this.Path=AlgoHelper.Pathfind2d(this.Regions,this.X,this.Y,t,e),this.Path=this.Path.filter(t=>this.Regions[t[1]][t[0]]>=128),this.PathIndex=1,this.Path.length>1&&(this.IsMoving=!0))},WMTS.prototype.Update=function(){if(this.IsMoving&&!this.Pause&&this.Cursor&&this.Frame%t.Delay==0){AudioManager.playStaticSe(this.CursorSE);let e=this.Path[this.PathIndex];this.X=e[0],this.Y=e[1],this.Cursor.jumpTo(this.X,this.Y),this.PathIndex++,$gameTemp.reserveCommonEvent(t.MoveCE),this.PathIndex>=this.Path.length&&(this.IsMoving=!1)}this.Frame++};let e=Scene_Load.prototype.onLoadSuccess;Scene_Load.prototype.onLoadSuccess=function(){e.call(this),$gameSystem.WMTS&&$WMTS.Load($gameSystem.WMTS)};let a=DataManager.saveGame;DataManager.saveGame=function(){return $gameSystem.WMTS=$WMTS.Save(),a.apply(DataManager,arguments)};let s=DataManager.createGameObjects;DataManager.createGameObjects=function(){s.call(this),$WMTS=new WMTS(t.X||0,t.Y||0,t.Enabled||[])};let i=SceneManager.updateMain;function WMTS_Prepare(){$WMTS.Prepare(),$WMTS.Render()}function WMTS_EnableAll(){$WMTS.EnableAll(),$WMTS.Render()}function WMTS_DisableAll(){$WMTS.DisableAll(),$WMTS.Render()}function WMTS_MoveToPlayer(){$WMTS.Move($gamePlayer.x,$gamePlayer.y)}SceneManager.updateMain=function(){i.call(this),$WMTS&&$WMTS.Update()}}