/*:
 * @plugindesc Adds up to 5 windows above the currency window in the menu scene to display specified variable names and their values. Variable IDs are set via plugin parameters.
 * <AUTHOR> Name
 *
 * @param Variable ID 1
 * @desc ID of the first variable to display.
 * @default 1
 *
 * @param Variable ID 2
 * @desc ID of the second variable to display.
 * @default 2
 *
 * @param Variable ID 3
 * @desc ID of the third variable to display.
 * @default 3
 *
 * @param Variable ID 4
 * @desc ID of the fourth variable to display.
 * @default 4
 *
 * @param Variable ID 5
 * @desc ID of the fifth variable to display.
 * @default 5
 *
 * @help This plugin does not provide plugin commands.
 */

(function() {
    var parameters = PluginManager.parameters('TB_VariablesWindow');
    var variableIds = [];
    for (var i = 1; i <= 5; i++) {
        variableIds.push(Number(parameters[`Variable ID ${i}`] || 0));
    }

    var _Scene_Menu_create = Scene_Menu.prototype.create;
    Scene_Menu.prototype.create = function() {
        _Scene_Menu_create.call(this);
        this.createVariableWindows();
    };

    Scene_Menu.prototype.createVariableWindows = function() {
        this._variableWindows = [];
        var wx = this._goldWindow.x;
        var wy = this._goldWindow.y;
        var ww = this._goldWindow.width;
        var wh = this._goldWindow.height;

        for (var i = 0; i < variableIds.length; i++) {
            if (variableIds[i] > 0) {
                wy -= wh; // Stack windows above each other
                var window = new Window_Variable(wx, wy, ww, wh, variableIds[i]);
                this._variableWindows.push(window);
                this.addWindow(window);
            }
        }
    };

    function Window_Variable() {
        this.initialize.apply(this, arguments);
    }

    Window_Variable.prototype = Object.create(Window_Base.prototype);
    Window_Variable.prototype.constructor = Window_Variable;

    Window_Variable.prototype.initialize = function(x, y, width, height, variableId) {
        Window_Base.prototype.initialize.call(this, x, y, width, height);
        this._variableId = variableId;
        this.refresh();
    };

    Window_Variable.prototype.refresh = function() {
        this.contents.clear();
        var variableName = $dataSystem.variables[this._variableId];
        var variableValue = $gameVariables.value(this._variableId);
        this.drawText(variableName + ': ' + variableValue, 0, 0, this.contents.width);
    };

})();
