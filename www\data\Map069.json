{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "落とし穴", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 250, "indent": 1, "parameters": [{"name": "Fall", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Earth5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 1, "parameters": [0, 1, 1, 0, 999, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["グラフィック読み込み"]}, {"code": 117, "indent": 1, "parameters": [605]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [605]}, {"code": 108, "indent": 1, "parameters": ["****************************"]}, {"code": 408, "indent": 1, "parameters": ["* ヤリゾーがいる場合"]}, {"code": 408, "indent": 1, "parameters": ["****************************"]}, {"code": 111, "indent": 1, "parameters": [4, 21, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(69, 1, 3)"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["****************************"]}, {"code": 408, "indent": 1, "parameters": ["* パーティは司祭と二人"]}, {"code": 408, "indent": 1, "parameters": ["****************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(69, 1, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 111, "indent": 0, "parameters": [12, "$trap_activate == -1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 13, "indent": null, "parameters": []}, {"code": 13, "indent": null, "parameters": []}, {"code": 13, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　パーティに司祭と二人っきりの場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [605]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　パーティにヤリゾーがいる場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************"]}, {"code": 408, "indent": 0, "parameters": ["寝取られの場合"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 32, 0]}, {"code": 108, "indent": 1, "parameters": ["*********************************************"]}, {"code": 408, "indent": 1, "parameters": ["最大寝取られの場合は司祭が下がってヤリゾーとおまんこしまくる"]}, {"code": 408, "indent": 1, "parameters": ["*********************************************"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 5, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1001"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1002"]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1003"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1004-1006"]}, {"code": 245, "indent": 2, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 50}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1008"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1009"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1010"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1011"]}, {"code": 245, "indent": 2, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 85}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1012"]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1013"]}, {"code": 245, "indent": 2, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1014"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1015-1016"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 2, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1017"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1018-1020"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 245, "indent": 2, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1021-1024"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1025"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1026"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1027"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1028"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1029"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1030"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1031-1032"]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 245, "indent": 2, "parameters": [{"name": "bj_dankyu_long", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1033"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1034"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1035"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1036"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1037"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 2, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1038"]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1039-1040"]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 4]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1041"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1042"]}, {"code": 118, "indent": 2, "parameters": ["ヤリ１０質問"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map69_ev1_p3_1001a\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map69_ev1_p3_1001b\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(63, \"select_map69_ev1_p3_1001c\")"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_1043-1045"]}, {"code": 119, "indent": 3, "parameters": ["ヤリ１０質問"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_1046-1048"]}, {"code": 119, "indent": 3, "parameters": ["ヤリ１０質問"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_1049"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [544, 544, 1, 0, 1]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*********************************************"]}, {"code": 408, "indent": 1, "parameters": ["８の場合はヤリゾーに無理やりおまんこされてカンジまくる"]}, {"code": 408, "indent": 1, "parameters": ["*********************************************"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 4, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_801"]}, {"code": 250, "indent": 2, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_802-804"]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_805"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [605]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_806"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(171, \"hantei_climbing\")"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 245, "indent": 2, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_807"]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, true]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_808"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(171, \"hantei_climbing\")"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 245, "indent": 2, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_809-810"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(171, \"hantei_climbing\")"]}, {"code": 108, "indent": 2, "parameters": ["クライミング判定"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 0, 99]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 99]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(54, 1, 3)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 225, "indent": 2, "parameters": [9, 9, 20, false]}, {"code": 246, "indent": 2, "parameters": [1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_811"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_812-813"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_814"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [195]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_815-819"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_820"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_821-826"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map69_ev1_p3_801a\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map69_ev1_p3_801b\")"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_827"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_828"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_829"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************"]}, {"code": 408, "indent": 0, "parameters": ["寝取られてない場合"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [62]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$hole", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$hole", 0]}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-16"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 22, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 22, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_17-18"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 50}]}, {"code": 250, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "アナルねぶりスライム", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 111, "indent": 1, "parameters": [0, 33, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 6]}, {"code": 122, "indent": 2, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 235, "indent": 2, "parameters": [19]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [72]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3-4"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [72]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_5-8"]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 108, "indent": 2, "parameters": ["********************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾーがパーティに居る場合"]}, {"code": 408, "indent": 2, "parameters": ["********************************"]}, {"code": 111, "indent": 2, "parameters": [4, 21, 0]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(69, 2, 2)"]}, {"code": 111, "indent": 3, "parameters": [12, "$branch1 == 1"]}, {"code": 119, "indent": 4, "parameters": ["噴き出し"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 544]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["********************************"]}, {"code": 408, "indent": 2, "parameters": ["ポーションメーカーがパーティに居る場合"]}, {"code": 408, "indent": 2, "parameters": ["********************************"]}, {"code": 111, "indent": 2, "parameters": [4, 10, 0]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(69, 2, 3)"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["********************************"]}, {"code": 408, "indent": 2, "parameters": ["従者がパーティに居ない場合"]}, {"code": 408, "indent": 2, "parameters": ["********************************"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [70]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_9-10"]}, {"code": 108, "indent": 2, "parameters": ["********************************"]}, {"code": 408, "indent": 2, "parameters": ["噴き出し"]}, {"code": 408, "indent": 2, "parameters": ["********************************"]}, {"code": 118, "indent": 2, "parameters": ["噴き出し"]}, {"code": 250, "indent": 2, "parameters": [{"name": "squirting7", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [70]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_11-13"]}, {"code": 250, "indent": 2, "parameters": [{"name": "squirting7", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_14-16"]}, {"code": 250, "indent": 2, "parameters": [{"name": "squirting9", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 4]}, {"code": 117, "indent": 2, "parameters": [70]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_17-18"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_message_prevent_scat\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 485]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 403]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 311, "indent": 1, "parameters": [0, 2, 1, 0, 1, false]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(2,-30)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーがいる場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-6"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 69, 2, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 69, 2, 2, 30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] if(s[31])"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-13"]}, {"code": 355, "indent": 1, "parameters": ["$branch1 = 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] if(s[31])"]}, {"code": 355, "indent": 1, "parameters": ["$branch1 = 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31-34"]}, {"code": 108, "indent": 1, "parameters": ["************************************"]}, {"code": 408, "indent": 1, "parameters": ["ちんぽ挿入"]}, {"code": 408, "indent": 1, "parameters": ["************************************"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [457, 457, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 245, "indent": 1, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 355, "indent": 1, "parameters": ["$womb = 1"]}, {"code": 655, "indent": 1, "parameters": ["$guy_type = 1"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [66]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40-50"]}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_B_slow", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [66]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51-57"]}, {"code": 108, "indent": 1, "parameters": ["************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$womb = 4"]}, {"code": 655, "indent": 1, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 1, "parameters": [66]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-71"]}, {"code": 108, "indent": 1, "parameters": ["************************************"]}, {"code": 408, "indent": 1, "parameters": ["ザーメンとスライムゲロ"]}, {"code": 408, "indent": 1, "parameters": ["************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["$womb = 5"]}, {"code": 655, "indent": 1, "parameters": ["$guy_type = 1"]}, {"code": 655, "indent": 1, "parameters": ["$slime = 1"]}, {"code": 655, "indent": 1, "parameters": ["$cum_mouth = 1"]}, {"code": 117, "indent": 1, "parameters": [66]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_80-82"]}, {"code": 355, "indent": 1, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 1, "parameters": ["$guy_type = 0"]}, {"code": 655, "indent": 1, "parameters": ["$slime = 0"]}, {"code": 655, "indent": 1, "parameters": ["$cum_mouth = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PMが居る場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [85]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 108, "indent": 0, "parameters": ["************************************"]}, {"code": 408, "indent": 0, "parameters": ["発射"]}, {"code": 408, "indent": 0, "parameters": ["************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting7", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 108, "indent": 0, "parameters": ["司祭グラ"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 108, "indent": 0, "parameters": ["ＰＭグラ"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 10, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-12"]}, {"code": 108, "indent": 0, "parameters": ["司祭グラ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting9", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 108, "indent": 0, "parameters": ["ＰＭグラ"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 10, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_13-15"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 0, "parameters": [0, 10, 1, 0, 1, false]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(10,-30)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "ホームレス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "ネブリヒル（乳首）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 111, "indent": 1, "parameters": [4, 21, 0]}, {"code": 122, "indent": 2, "parameters": [72, 72, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 3]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 1, "parameters": [["\\n[1]", "\\n[21] if(v[72]>=1)"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\n[1]"]}, {"code": 108, "indent": 2, "parameters": ["********************"]}, {"code": 408, "indent": 2, "parameters": ["主人公"]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"ero_giryou\""]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\n[21] if(v[72]>=1)"]}, {"code": 108, "indent": 2, "parameters": ["********************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 3, 2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_20-21"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_30-31"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 2, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 2, "parameters": [466, 466, 0, 1, 1683]}, {"code": 122, "indent": 2, "parameters": [456, 456, 0, 0, 21]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"npc_ero_giryou\""]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_100"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [63]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_101"]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 250, "indent": 2, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [27, 27, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [63]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_102"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 2, "parameters": [0, 2, 1, 0, 10, false]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "舐りヒル（クリトリス）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [72]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "スモッグ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 212, "indent": 1, "parameters": [-1, 31, true]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ここにグラフィック"]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [606]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 69, 6, 1, 100, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 69, 6, 1, 200, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(63, 69, 6, 1, 300, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["*****************************"]}, {"code": 408, "indent": 2, "parameters": ["煙が収まるまで待つ"]}, {"code": 408, "indent": 2, "parameters": ["*****************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_101"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 108, "indent": 2, "parameters": ["ダイス判定"]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 10, 0]}, {"code": 108, "indent": 3, "parameters": ["アイテム入手"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_110"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 250, "indent": 3, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 5, 1]}, {"code": 108, "indent": 4, "parameters": ["何もない"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_120"]}, {"code": 119, "indent": 4, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 2, 1]}, {"code": 108, "indent": 5, "parameters": ["小ダメージ"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_130"]}, {"code": 355, "indent": 5, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 5, "parameters": [0, 1, 1, 0, 5, false]}, {"code": 101, "indent": 5, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 5, "parameters": [""]}, {"code": 119, "indent": 5, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 108, "indent": 5, "parameters": ["大ダメージ"]}, {"code": 119, "indent": 5, "parameters": ["大ダメージ"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["*****************************"]}, {"code": 408, "indent": 2, "parameters": ["声を上げて仲間の安全を確認する"]}, {"code": 408, "indent": 2, "parameters": ["*****************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_201"]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 8, 1]}, {"code": 108, "indent": 3, "parameters": ["大丈夫"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_210"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_211"]}, {"code": 119, "indent": 3, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["大ダメージ"]}, {"code": 119, "indent": 3, "parameters": ["大ダメージ"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 2, "parameters": ["*****************************"]}, {"code": 408, "indent": 2, "parameters": ["煙の中を仲間を探しに行く"]}, {"code": 408, "indent": 2, "parameters": ["*****************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_301"]}, {"code": 111, "indent": 2, "parameters": [4, 21, 0]}, {"code": 108, "indent": 3, "parameters": ["******************************"]}, {"code": 408, "indent": 3, "parameters": ["ヤリゾーが居る場合"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_350"]}, {"code": 111, "indent": 3, "parameters": [1, 204, 0, 5, 1]}, {"code": 108, "indent": 4, "parameters": ["セックス"]}, {"code": 122, "indent": 4, "parameters": [28, 28, 0, 0, 5]}, {"code": 117, "indent": 4, "parameters": [606]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_380"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_381-383"]}, {"code": 108, "indent": 4, "parameters": ["膣内精液+"]}, {"code": 121, "indent": 4, "parameters": [23, 23, 0]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 544]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 402]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 1704]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 200]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 123, "indent": 4, "parameters": ["B", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 4, 1]}, {"code": 108, "indent": 5, "parameters": ["フェラ"]}, {"code": 122, "indent": 5, "parameters": [28, 28, 0, 0, 4]}, {"code": 117, "indent": 5, "parameters": [606]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_370"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_371"]}, {"code": 108, "indent": 5, "parameters": ["口内に精液+"]}, {"code": 121, "indent": 5, "parameters": [23, 23, 0]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 544]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 404]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 1904]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 200]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 123, "indent": 5, "parameters": ["B", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 108, "indent": 5, "parameters": ["セクハラ"]}, {"code": 121, "indent": 5, "parameters": [23, 23, 0]}, {"code": 122, "indent": 5, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [606]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_360"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_361-362"]}, {"code": 122, "indent": 5, "parameters": [28, 28, 0, 0, 0]}, {"code": 117, "indent": 5, "parameters": [606]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["_event_data_base_text_363-364"]}, {"code": 108, "indent": 5, "parameters": ["セクハラ+1"]}, {"code": 122, "indent": 5, "parameters": [58, 58, 0, 0, 411]}, {"code": 122, "indent": 5, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 5, "parameters": [2]}, {"code": 123, "indent": 5, "parameters": ["A", 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["******************************"]}, {"code": 408, "indent": 3, "parameters": ["ヤリゾーが居ない場合"]}, {"code": 122, "indent": 3, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 6, 1]}, {"code": 108, "indent": 4, "parameters": ["大丈夫"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_310"]}, {"code": 119, "indent": 4, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["大ダメージ"]}, {"code": 118, "indent": 4, "parameters": ["大ダメージ"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_311"]}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 4, "parameters": [0, 1, 1, 0, 10, false]}, {"code": 101, "indent": 4, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 4, "parameters": [""]}, {"code": 119, "indent": 4, "parameters": ["スモッグ終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************************"]}, {"code": 408, "indent": 1, "parameters": ["スモッグ終了"]}, {"code": 408, "indent": 1, "parameters": ["*****************************"]}, {"code": 118, "indent": 1, "parameters": ["スモッグ終了"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_900-901"]}, {"code": 108, "indent": 1, "parameters": ["司祭がセクハラされた"]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_910"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["司祭がフェラかセックスした"]}, {"code": 111, "indent": 1, "parameters": [2, "B", 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_920"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 123, "indent": 1, "parameters": ["B", 1]}, {"code": 121, "indent": 1, "parameters": [23, 23, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "イソギンチャク", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(214, 10, 5)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 69, 7, 1, 20, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 69, 7, 1, 30, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"strength\""]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [0, 80, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_100"]}, {"code": 111, "indent": 3, "parameters": [4, 10, 0]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_101"]}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["本処理"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["本処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["本処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 118, "indent": 1, "parameters": ["本処理"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 245, "indent": 1, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [1602, 1602, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200-201"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [1602, 1602, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(214, 10, 5)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_202"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-212"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_213"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [1602, 1602, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [63]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [1602, 1602, 0, 0, 3]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(214, 10, 5)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_232"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["アクメ"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-231"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["数値処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 100, 4]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 311, "indent": 1, "parameters": [0, 2, 1, 0, 1, false]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(2,-30)"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 311, "indent": 2, "parameters": [0, 10, 1, 0, 1, false]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(10,-30)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["事後"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_240"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_241"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "フラワー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [12, "$trap_activate == 1"]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 2, 10, 20]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 2, 10, 20]}, {"code": 122, "indent": 1, "parameters": [467, 467, 0, 2, 5000, 10000]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(69, 8, 2)"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(69, 8, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 69, 7, 1, 20, 0)"]}, {"code": 655, "indent": 1, "parameters": ["var_from_sheet(62, 69, 7, 1, 30, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"strength\""]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [0, 80, 0]}, {"code": 250, "indent": 3, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_100"]}, {"code": 111, "indent": 3, "parameters": [4, 10, 0]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_110"]}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["本処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["本処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 118, "indent": 1, "parameters": ["本処理"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!cum_out_once2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(69, 8, 2)"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(69, 8, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精数値計上処理"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 10001]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"顔\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 10001]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"胸\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_210"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["数値処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 100, 4]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 311, "indent": 1, "parameters": [0, 2, 1, 0, 1, false]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(2,-30)"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 311, "indent": 2, "parameters": [0, 10, 1, 0, 1, false]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(10,-30)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["事後"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_300"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 0]}, {"code": 122, "indent": 2, "parameters": [1634, 1634, 0, 0, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_310"]}, {"code": 122, "indent": 2, "parameters": [1634, 1634, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*******************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["*******************************************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭画像呼び出し"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 117, "indent": 0, "parameters": [62]}, {"code": 108, "indent": 0, "parameters": ["花トラップ本体"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-trapFlower_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["花トラップ　液体"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-trapFlower_upper`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer31]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ＰＭ画像呼び出し"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 10, 1)"]}, {"code": 108, "indent": 0, "parameters": ["花トラップ本体"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-trapFlower_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["花トラップ　液体"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-trapFlower_upper`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 1}]}