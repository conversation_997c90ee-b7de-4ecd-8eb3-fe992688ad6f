{function StatusPoints(){}function Component_StatusPoints(t){Object.assign(this,{SrcIcon:"battle/icon_hp",IconX:0,IconY:0,IconWidth:14,IconHeight:14,X:0,Y:0,Width:1,Height:1,FontSize:28,Value:0,Max:1,Visible:!1},t),this.BitmapIcon=ImageManager.loadPicture(this.SrcIcon),this.SpriteIcon=new Sprite(this.BitmapIcon),this.Bitmap=new Bitmap(this.Width,this.Height),this.Sprite=new Sprite(this.Bitmap),this.Bitmap.fontSize=this.FontSize,this.Parent&&(this.Parent.addChild(this.SpriteIcon),this.Parent.addChild(this.Sprite)),BitmapHelper.OnceIsLoadedAll([this.BitmapIcon],this,function(){this.draw()})}Component_StatusPoints.prototype.UpdateSprites=function(){this.Sprite.x=this.X,this.Sprite.y=this.Y,this.Sprite.visible=this.Visible,this.SpriteIcon.x=this.X-this.IconWidth+this.IconX,this.SpriteIcon.y=this.Y+this.IconY,this.SpriteIcon.scale.set(this.IconWidth/this.BitmapIcon.width,this.IconHeight/this.BitmapIcon.height),this.SpriteIcon.visible=this.Visible},Component_StatusPoints.prototype.draw=function(){this.Bitmap.clear(),BitmapHelper.DrawFraction(this.Bitmap,{value:Math.max(0,Math.min(this.Value,this.Max)),max:this.Max}),this.UpdateSprites()}}