(function() {
    var _Game_Interpreter_pluginCommand =
        Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.call(this, command, args);
        if (command === 'TurnTowardEvent') {
            var eventId = Number(args[0]);
            var event = eventId === 0 ? $gameMap.event(this._eventId) : $gameMap.event(eventId);
            if (event) {
                $gamePlayer.turnTowardCharacter(event);
                $gamePlayer.followers().forEach(function(follower) {
                    follower.turnTowardCharacter(event);
                });
            }
        }
    };

    Game_CharacterBase.prototype.turnTowardCharacter = function(character) {
        var sx = this.deltaXFrom(character.x);
        var sy = this.deltaYFrom(character.y);
        if (Math.abs(sx) > Math.abs(sy)) {
            this.setDirection(sx > 0 ? 6 : 4); // 6: Right, 4: Left
        } else if (sy !== 0) {
            this.setDirection(sy > 0 ? 2 : 8); // 2: Down, 8: Up
        }
    };
})();
