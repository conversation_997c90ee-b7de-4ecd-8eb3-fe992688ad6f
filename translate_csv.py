import csv
import os
from googletrans import Translator

def translate_file(csv_file):
    # Tạo tên file mới
    new_file = f"{os.path.splitext(csv_file)[0]}_vi.csv"
    
    # Khởi tạo translator
    translator = Translator()
    
    with open(csv_file, 'r', encoding='utf-8') as infile:
        with open(new_file, 'w', newline='', encoding='utf-8') as outfile:
            reader = csv.DictReader(infile)
            fieldnames = reader.fieldnames
            
            # Thêm cột mới cho tiếng Việt
            new_fieldnames = []
            for field in fieldnames:
                if 'ENG_' in field:
                    new_fieldnames.append(field)
                    new_fieldnames.append(field.replace('ENG_', 'VIE_'))
                else:
                    new_fieldnames.append(field)
            
            writer = csv.DictWriter(outfile, fieldnames=new_fieldnames)
            writer.writeheader()
            
            for row in reader:
                new_row = {}
                for field in fieldnames:
                    if 'ENG_' in field:
                        # <PERSON><PERSON><PERSON> từ ti<PERSON><PERSON> <PERSON><PERSON> sang tiếng Việt
                        try:
                            vi_text = translator.translate(row[field], src='en', dest='vi').text
                            new_row[field] = row[field]
                            new_row[field.replace('ENG_', 'VIE_')] = vi_text
                        except Exception as e:
                            print(f"Error translating {field}: {e}")
                            new_row[field] = row[field]
                            new_row[field.replace('ENG_', 'VIE_')] = row[field]
                    else:
                        new_row[field] = row[field]
                writer.writerow(new_row)

# Danh sách các file cần dịch
csv_files = [
    'DB_Actor.csv',
    'DB_Armor.csv',
    'DB_Item.csv',
    'DB_Skill.csv',
    'DB_Weapon.csv',
    'QuestList.csv',
    'DB_NPC.csv'
]

# Dịch từng file
for file in csv_files:
    print(f"Translating {file}...")
    translate_file(f'csv/{file}')
    print(f"Done translating {file}")
