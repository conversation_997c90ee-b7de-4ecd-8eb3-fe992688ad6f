{function Component_Bar(t={}){Object.assign(this,{SrcImage:"battle/healthbar_green",SrcImageBackground:"battle/healthbar_red",X:0,Y:0,BarWidth:4,BarHeight:16,Value:0,Max:1,Visible:!1},t),Object.defineProperty(this,"ScaleX",{get(){return this.BarWidth/this.BitmapBar.width}}),Object.defineProperty(this,"ScaleY",{get(){return this.BarHeight/this.BitmapBar.width}}),Object.defineProperty(this,"MaxScaleX",{get(){return this.BarWidth/this.BitmapBar.width}}),Object.defineProperty(this,"MaxScaleY",{get(){return this.BarHeight/this.BitmapBar.height}}),this.BitmapBar=ImageManager.loadPicture(this.SrcImage),this.BitmapBarBackground=ImageManager.loadPicture(this.SrcImageBackground),this.SpriteBar=new Sprite(this.BitmapBar),this.SpriteBarBackground=new Sprite(this.BitmapBarBackground),this.Parent&&(this.Parent.addChild(this.SpriteBarBackground),this.Parent.addChild(this.SpriteBar)),BitmapHelper.OnceIsLoadedAll([this.BitmapBar,this.BitmapBarBackground],this,function(){this.UpdateSprites()})}Component_Bar.prototype.UpdateSprites=function(){this.SpriteBar.x=this.X,this.SpriteBar.y=this.Y,this.SpriteBar.scale.set(Math.max(0,Math.min(this.Value,this.Max))/this.Max*this.MaxScaleX,this.MaxScaleY),this.SpriteBar.visible=this.Visible,this.SpriteBarBackground.x=this.X,this.SpriteBarBackground.y=this.Y,this.SpriteBarBackground.scale.set(this.MaxScaleX,this.MaxScaleY),this.SpriteBarBackground.visible=this.Visible},Component_Bar.prototype.update=function(){},Component_Bar.prototype.draw=function(){this.UpdateSprites()}}