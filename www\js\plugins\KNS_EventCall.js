/*:
 * @plugindesc ver.1.0.3 別マップを指定して、イベントの複製・ページの呼び出しを行います
 * <AUTHOR>
 *
 * @help
 * ※このプラグインはEventReSpawn、KNS_RgssTranslator、KNS_CsvReader
 * 　以降に設置してください。
 *
 * 別マップを指定して、イベントの複製・ページの呼び出しを行います。
 * マップデータの読み込みが必要な場合、読み込みが完了するまで
 * ウェイトが挟まれます。
 *
 * ■イベントコマンド「スクリプト」
 * ●マップIDに0を入れると実行中のイベントがいるマップIDが指定されます。
 * MapEvent.call(マップID, イベントID, ページID);
 * 　指定したイベントのページを読み込み、その場で実行します。
 *
 * add_event(x, y, イベントID, マップID, 向き);
 * 　トリアコンタン氏のイベント生成プラグインの機能を拡張し、
 * 　別マップからのイベント呼び出しを実装します。
 * ※この関数はマップ読み込み済みの場合のみGame_PrefabEventを
 * 　返します。変数に代入して操作する場合、下記のマップメモ欄の
 * 　記述で指定のマップを事前に読み込む必要があります。
 *
 * ■マップメモ欄
 * <PreloadMap: 1, 2, 3, 4>
 * 　マップ移動時、指定したIDのマップデータを事前に読み込むことで
 * 　呼び出し時のロード時間を回避します。
 *
 * ■更新履歴
 * ver.1.0.0(2023-06-04)
 * - デモ
 * ver.1.0.1(2023-06-05)
 * - スクリプトからマップID0が指定されたとき、実行中の
 *   イベントのマップIDを返すよう変更しました。
 * - 存在しないマップが指定したときフリーズする仕様を
 *   変更しました。
 * ver.1.0.2(2023-07-07)
 * - スクリプトからイベントID0が指定されたとき、
 *   自分のイベントIDを返すよう変更しました。
 * ver.1.0.3(2023-11-21)
 * - イベント待ちウェイト直後に文章の操作などのウェイトがない
 * 　イベントを挟むとMapEvent.callがスキップされてしまう不具合を修正
 */

(function(){
	[
		[/add_event = add_event\(/g, "let add_event = this.knsAddEvent("],
		[/add_event\(/g, "this.knsAddEvent("],
		[/MapEvent\.call\(/g, "this.knsCallMapEvent\("]
	].forEach(function(args){ KNS_RgssTranslator.addScriptPattern(...args); })

	//==========================================================
	// alias DataManager
	//==========================================================
	DataManager.knsIsLoadingEventCallSubMap = function(){
		return (
			this._knsEventCallLoadingStuck &&
			this._knsEventCallLoadingStuck.length > 0
		);
	}

	DataManager.knsLoadEventCallSubMap = function(mapId) {
		if (this._knsEventCallLoadingStuck){
			this._knsEventCallLoadingStuck.push(mapId);
		}else{
			this._knsEventCallLoadingStuck = [mapId];
		}
		this.knsLoadDataWithInfo(mapId, 'data/Map%1.json'.format(mapId.padZero(3)));
	};

	DataManager.knsLoadDataWithInfo = function(mapId, url){
		const xhr = new XMLHttpRequest();
		xhr.open('GET', url);
		xhr.overrideMimeType('application/json');
		xhr.onload = function() {
			DataManager.knsShiftLoadingSubMapStuck();
			if (xhr.status < 400) {
				DataManager.knsOnLoadEventCallSubMap(mapId, JSON.parse(xhr.responseText));
			}
		};
		xhr.onerror = function() {
			DataManager.knsShiftLoadingSubMapStuck();
			$gameTemp.knsAddEventCallList(mapId, null);
		};
		xhr.send();
	}

	DataManager.knsShiftLoadingSubMapStuck = function(mapId){
		this._knsEventCallLoadingStuck.splice(
			this._knsEventCallLoadingStuck.indexOf(mapId), 1
		);
	}

	DataManager.knsOnLoadEventCallSubMap = function(mapId, object){
		for (let i = 0; i < object.events.length; i++) {
			const data = object.events[i];
			if (data && data.note !== undefined) { this.extractMetadata(data); }
		}
		$gameTemp.knsAddEventCallList(mapId, object);
	}

	//==========================================================
	// alias Game_Temp
	//==========================================================
	Game_Temp.prototype.knsPreloadEventCallSubMap = function(){
		const mapId = $gameMap.mapId();
		if (this._knsMapCallEventList && this._knsMapCallEventList.mapId === mapId){
			return; // ロード済みなので何もしない
		}
		this._knsMapCallEventList = { mapId: mapId };
		if ($dataMap && typeof $dataMap.meta.PreloadMap === "string"){
			$dataMap.meta.PreloadMap.split(",").forEach(function(mapId){
				this.knsLoadEventCallSubMap(Math.floor(mapId));
			}, this);
		}
	}

	Game_Temp.prototype.knsLoadEventCallSubMap = function(mapId){
		DataManager.knsLoadEventCallSubMap(mapId);
	}

	Game_Temp.prototype.knsAddEventCallList = function(mapId, map){
		if (this._knsMapCallEventList){
			if (map){
				this._knsMapCallEventList[mapId] = map.events;
			}else{
				this._knsMapCallEventList[mapId] = [];
				console.log(`[KNS_EventCall]マップ${mapId}は存在しません！`)
			}
		}else{
			console.log("[KNS_EventCall]プリロード前にイベントが呼び出された？（想定外の不具合）")
		}
	}

	Game_Temp.prototype.knsIsEventCallSubMapLoaded = function(mapId){
		if (mapId === $gameMap.mapId()){ return true; }
		return this._knsMapCallEventList && this._knsMapCallEventList[mapId];
	}

	Game_Temp.prototype.knsGetEventCallEvent = function(mapId, eventId){
		if (mapId === $gameMap.mapId()){
			return $dataMap.events[eventId] || null;
		}
		if (this.knsIsEventCallSubMapLoaded(mapId)){
			return this._knsMapCallEventList[mapId][eventId] || null;
		}
		return null;
	}

	//==========================================================
	// alias Scene_Map
	//==========================================================
	const _Scene_Map_create = Scene_Map.prototype.create;
	Scene_Map.prototype.create = function() {
		this._knsIsSubMapsLoaded = false;
		_Scene_Map_create.apply(this, arguments);
	};

	const _Scene_Map_isReady = Scene_Map.prototype.isReady;
	Scene_Map.prototype.isReady = function() {
		if (_Scene_Map_isReady.apply(this, arguments)){
			if (this._knsIsSubMapsLoaded){
				return !DataManager.knsIsLoadingEventCallSubMap();
			}else{
				this._knsIsSubMapsLoaded = true;
				$gameTemp.knsPreloadEventCallSubMap();
			}
		}
		return false;
	};

	//==========================================================
	// alias Game_Interpreter
	//==========================================================
	Game_Interpreter.prototype.knsAddEvent = function(x, y, eventId, mapId, dir){
		return this.knsCheckEventCallNeedsWait(mapId, function(x, y, eventId, mapId, dir){
			const ev = $gameTemp.knsGetEventCallEvent(mapId, eventId);
			if (ev){
				$gameMap.knsSpawnEventInOtherMap(x, y, dir, mapId, ev);
			}else{
				console.log(`[KNS_EventCall]マップ${mapId}にイベント${eventId}は存在しません！`)
			}
			return ev || null;
		}.bind(this, x, y, eventId || this.eventId(), mapId || this._knsMessageMapId, dir));
	}

	Game_Interpreter.prototype.knsCallMapEvent = function(mapId, eventId, pageId){
		return this.knsCheckEventCallNeedsWait(mapId, function(mapId, eventId, pageId){
			const ev = $gameTemp.knsGetEventCallEvent(mapId, eventId);
			if (ev){
				if (ev.pages[pageId - 1]){
					this.setupChild(ev.pages[pageId - 1].list, eventId, mapId, pageId);
				}else{
					console.log(`[KNS_EventCall]イベント${eventId}にページ${pageId}は存在しません！`)
				}
			}else{
				console.log(`[KNS_EventCall]マップ${mapId}にイベント${eventId}は存在しません！`)
			}
		}.bind(this, mapId || this._knsMessageMapId, eventId || this.eventId(), pageId));
	}

	Game_Interpreter.prototype.knsCheckEventCallNeedsWait = function(mapId, callBack){
		if ($gameTemp.knsIsEventCallSubMapLoaded(mapId)){
			return callBack();
		}else{
			$gameTemp.knsLoadEventCallSubMap(mapId);
			const obj = [$gameMap.mapId(), callBack];
			if (this._knsOnEventCallSucceeds){
				this._knsOnEventCallSucceeds.push(obj);
			}else{
				this._knsOnEventCallSucceeds = [obj];
			}
		}
		return null;
	}

	const _Game_Interpreter_updateWaitMode = Game_Interpreter.prototype.updateWaitMode;
	Game_Interpreter.prototype.updateWaitMode = function() {
		if (this._knsOnEventCallSucceeds){
			if (!DataManager.knsIsLoadingEventCallSubMap()) {
				this._knsOnEventCallSucceeds.forEach(function(obj){
					obj[0] === $gameMap.mapId() && obj[1]();
				});
				this._knsOnEventCallSucceeds = null;
			}
			return true;
		}else{
			return _Game_Interpreter_updateWaitMode.apply(this, arguments);
		}
	};

	//==========================================================
	// alias Game_Map
	//==========================================================
	Game_Map.prototype.knsSpawnEventInOtherMap = function(x, y, dir, originalMapId, originalEvent) {
		const eventId = this.getEventIdSequence();
		this._lastSpawnEventId = eventId;
		const ev = new Game_KnsPrefabCallEvent(
			this.mapId(), eventId, originalMapId, originalEvent.id, x, y, originalEvent
		);
		ev._originalDirection = dir;
		ev.setDirection(dir);
		this._events[eventId] = ev;
	};
})();

//==========================================================
// new Game_KnsPrefabCallEvent
//==========================================================
function Game_KnsPrefabCallEvent(){
	this.initialize.apply(this, arguments);
}
Game_KnsPrefabCallEvent.prototype = Object.create(Game_PrefabEvent.prototype);
Game_KnsPrefabCallEvent.prototype.constructor = Game_KnsPrefabCallEvent;

Game_KnsPrefabCallEvent.prototype.initialize = function(
	mapId, eventId, originalMapId, originalEventId, x, y, originalEvent
){
	this._knsOriginalMapId = originalMapId;
	this._knsOriginalEvent = originalEvent;
	Game_PrefabEvent.prototype.initialize.call(this,
		mapId, eventId, originalEventId, x, y, false
	);
}

Game_KnsPrefabCallEvent.prototype.knsGetMessageMapId = function(){
	return this._knsOriginalMapId;
}

Game_KnsPrefabCallEvent.prototype.linkEventData = function(){
	$dataMap.events[this.eventId()] = this._knsOriginalEvent;
}