{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "背後からのセクハラ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ70 1 1 背後からのセクハラグラフィック開始\")"]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 16]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["3 ossan"]}, {"code": 408, "indent": 0, "parameters": ["4arm1 under"]}, {"code": 408, "indent": 0, "parameters": ["5head"]}, {"code": 408, "indent": 0, "parameters": ["6hair"]}, {"code": 408, "indent": 0, "parameters": ["7face"]}, {"code": 408, "indent": 0, "parameters": ["8hat"]}, {"code": 108, "indent": 0, "parameters": ["9body"]}, {"code": 408, "indent": 0, "parameters": ["10pub hair"]}, {"code": 408, "indent": 0, "parameters": ["11arm pit hair"]}, {"code": 408, "indent": 0, "parameters": ["12 under wear 13tights"]}, {"code": 408, "indent": 0, "parameters": ["15arms0 (skin-pose)"]}, {"code": 408, "indent": 0, "parameters": ["16arms tights (cloth-pose)"]}, {"code": 108, "indent": 0, "parameters": ["17cloth main (cloth-armpose)"]}, {"code": 408, "indent": 0, "parameters": ["18ossan hand"]}, {"code": 408, "indent": 0, "parameters": ["19squirt"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-se<PERSON><PERSON>_from_back\""]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*******************************"]}, {"code": 408, "indent": 0, "parameters": ["背景の男表示"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの表示"]}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 0]}, {"code": 111, "indent": 2, "parameters": [1, 39, 0, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 3, "parameters": [""]}, {"code": 655, "indent": 3, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 3, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**消すかも**"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-harrasment_arm`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-human`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**消すかもここまで**"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-slime`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ケツ揉み手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-harrasment_arm`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-human`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ケツ揉み手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 21, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["    $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["    $picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["    $picture.show($no, 0, $gameVariables.value(21), $gameVariables.value(22), $s_w, $s_h, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["インモウの表示　12"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示 13"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameActors.actor(2).equips()[4].id;"]}, {"code": 655, "indent": 0, "parameters": ["    $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["    $picture = screen.pictures[$layer12];"]}, {"code": 655, "indent": 0, "parameters": ["    $picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["    $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["    $picture = screen.pictures[$layer13];"]}, {"code": 655, "indent": 0, "parameters": ["    $picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["アームの表示"]}, {"code": 355, "indent": 0, "parameters": ["skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${skin}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15];"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["    var num = $gameVariables.value(25);"]}, {"code": 655, "indent": 0, "parameters": ["    $no = `${$e_name}-cloth${id}-arms${num}`;"]}, {"code": 655, "indent": 0, "parameters": ["    $picture = screen.pictures[$layer16];"]}, {"code": 655, "indent": 0, "parameters": ["    $picture.show($no, 0, $gameVariables.value(21), $gameVariables.value(22), $s_h, $s_w, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameActors.actor(2).equips()[1].id;"]}, {"code": 655, "indent": 0, "parameters": ["    var num = $gameVariables.value(25);"]}, {"code": 655, "indent": 0, "parameters": ["    $no = `${$e_name}-cloth${id}-${num}`;"]}, {"code": 655, "indent": 0, "parameters": ["    $picture = screen.pictures[$layer18];"]}, {"code": 655, "indent": 0, "parameters": ["    $picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": ["セクハラする手とか指の表示"]}, {"code": 408, "indent": 0, "parameters": ["**********************************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーの手マン"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-yarizo_finger`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ケツ触り"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 2, 0]}, {"code": 235, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["乳揉み"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-groping_hands`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ70 1 1 背後からのセクハラグラフィック終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "背面フェラ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 17]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["3 ossan"]}, {"code": 408, "indent": 0, "parameters": ["4penis lower"]}, {"code": 408, "indent": 0, "parameters": ["5guy_arm_front"]}, {"code": 408, "indent": 0, "parameters": ["9body"]}, {"code": 408, "indent": 0, "parameters": ["7ph"]}, {"code": 408, "indent": 0, "parameters": ["8ah"]}, {"code": 108, "indent": 0, "parameters": ["10tights"]}, {"code": 408, "indent": 0, "parameters": ["11arms(skin-arms)"]}, {"code": 408, "indent": 0, "parameters": ["12arms tights(cloth-arms)"]}, {"code": 408, "indent": 0, "parameters": ["13cloth"]}, {"code": 408, "indent": 0, "parameters": ["14head(skin-head)"]}, {"code": 408, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>(hair-head)"]}, {"code": 108, "indent": 0, "parameters": ["16face(head-face) h0-f0"]}, {"code": 408, "indent": 0, "parameters": ["17hat(cloth-head)"]}, {"code": 408, "indent": 0, "parameters": ["18guy_arm_back"]}, {"code": 408, "indent": 0, "parameters": ["19penis upper"]}, {"code": 408, "indent": 0, "parameters": ["20shasei"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-bj_back\""]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 550]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 550]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["おっさんの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ペニスベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の腕手前表示"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドポーズに従ってヤリゾーの腕のポーズ変更させてる"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo-arm_front${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["脇毛の表示"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["アームの表示"]}, {"code": 355, "indent": 0, "parameters": ["skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arms${skin}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}-arms${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}-${num}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}-${head}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}-${head}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${head}-f${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": ["num= $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["おっさんの腕（奥側）表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo-arm_back${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ペニス上部レイヤーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}-upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "背面駅弁", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 18]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["5 ossan"]}, {"code": 408, "indent": 0, "parameters": ["6 head (skin-head)"]}, {"code": 408, "indent": 0, "parameters": ["7 hair (hair-head)"]}, {"code": 408, "indent": 0, "parameters": ["8 face (h-f)"]}, {"code": 408, "indent": 0, "parameters": ["9 hat (cloth-hed)"]}, {"code": 408, "indent": 0, "parameters": ["10 body"]}, {"code": 108, "indent": 0, "parameters": ["11 pussy"]}, {"code": 408, "indent": 0, "parameters": ["12 inmou"]}, {"code": 408, "indent": 0, "parameters": ["13 tights"]}, {"code": 408, "indent": 0, "parameters": ["14 under wear"]}, {"code": 408, "indent": 0, "parameters": ["15 cloth"]}, {"code": 408, "indent": 0, "parameters": ["16 shasei - sitaji"]}, {"code": 108, "indent": 0, "parameters": ["17 penis"]}, {"code": 408, "indent": 0, "parameters": ["18 shasei"]}, {"code": 408, "indent": 0, "parameters": ["19 yuge"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-ekiben_rev\""]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["おっさんの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-yarizo`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}-${head}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}-${head}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["head = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${head}-f${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハットの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["  num = $gameVariables.value(27);"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}-${num}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["まんこの表示"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-pussy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["アンダーウェア"]}, {"code": 108, "indent": 0, "parameters": ["クロース"]}, {"code": 108, "indent": 0, "parameters": ["射精　ベース　の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum_base${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ペニスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ゆげの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-yuge`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "壁バック", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-st_back2\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 456]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 19]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["1-背景"]}, {"code": 408, "indent": 0, "parameters": ["2-体"]}, {"code": 408, "indent": 0, "parameters": ["4-髪の毛"]}, {"code": 408, "indent": 0, "parameters": ["5-face"]}, {"code": 108, "indent": 0, "parameters": ["10-下着"]}, {"code": 408, "indent": 0, "parameters": ["11-ソックス"]}, {"code": 408, "indent": 0, "parameters": ["13-服"]}, {"code": 408, "indent": 0, "parameters": ["15-ヘッドアクセ"]}, {"code": 408, "indent": 0, "parameters": ["16-男"]}, {"code": 408, "indent": 0, "parameters": ["17-ちんぽ"]}, {"code": 108, "indent": 0, "parameters": ["20-射精"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-door`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-skin${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘアーの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["face = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${face}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ガイの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 235, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-finger`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 3, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(468)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cock2`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "ごろりんちょ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ70 5 1開始\")"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-laying1\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 20]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["1-背景"]}, {"code": 408, "indent": 0, "parameters": ["2-体"]}, {"code": 408, "indent": 0, "parameters": ["4-髪の毛"]}, {"code": 408, "indent": 0, "parameters": ["5-face"]}, {"code": 108, "indent": 0, "parameters": ["10-下着"]}, {"code": 408, "indent": 0, "parameters": ["11-ソックス"]}, {"code": 408, "indent": 0, "parameters": ["13-服"]}, {"code": 408, "indent": 0, "parameters": ["15-ヘッドアクセ"]}, {"code": 408, "indent": 0, "parameters": ["16-男"]}, {"code": 108, "indent": 0, "parameters": ["16-<PERSON><PERSON><PERSON>"]}, {"code": 408, "indent": 0, "parameters": ["17-<PERSON><PERSON><PERSON>"]}, {"code": 408, "indent": 0, "parameters": ["18-潮"]}, {"code": 108, "indent": 0, "parameters": ["19-射精"]}, {"code": 408, "indent": 0, "parameters": ["20-ちんぽ"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 550]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 550]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-bg${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-skin${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["プッシーの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-pussy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"1\")"]}, {"code": 355, "indent": 0, "parameters": ["if($gameActors.actor(2).equips()[4] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["screen.pictures[$layer10].erase"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if($gameActors.actor(2).equips()[3] != null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ミルクの表示"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"2\")"]}, {"code": 111, "indent": 0, "parameters": [12, "$milk == 1"]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-milk`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[16].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-hands`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[17].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-hands_teman`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture(17);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮の表示"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"3\")"]}, {"code": 111, "indent": 0, "parameters": [12, "$squirt == 1"]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[18].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ガイの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cock`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ70 5 1終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "大股開き蹲踞", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-<PERSON><PERSON><PERSON><PERSON><PERSON>\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 21]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["１ ポール"]}, {"code": 408, "indent": 0, "parameters": ["２　ボディ"]}, {"code": 408, "indent": 0, "parameters": ["３陰毛"]}, {"code": 408, "indent": 0, "parameters": ["５下着"]}, {"code": 408, "indent": 0, "parameters": ["６タイツ"]}, {"code": 408, "indent": 0, "parameters": ["７服"]}, {"code": 108, "indent": 0, "parameters": ["８腕"]}, {"code": 408, "indent": 0, "parameters": ["９腕タイツ"]}, {"code": 408, "indent": 0, "parameters": ["１０腕服"]}, {"code": 408, "indent": 0, "parameters": ["１１おっぱい"]}, {"code": 408, "indent": 0, "parameters": ["１２おっぱい下着"]}, {"code": 408, "indent": 0, "parameters": ["１３おっぱいタイツ"]}, {"code": 108, "indent": 0, "parameters": ["１４おっぱい服"]}, {"code": 408, "indent": 0, "parameters": ["１５頭"]}, {"code": 408, "indent": 0, "parameters": ["１６髪の毛"]}, {"code": 408, "indent": 0, "parameters": ["１７顔"]}, {"code": 408, "indent": 0, "parameters": ["１８ハット"]}, {"code": 408, "indent": 0, "parameters": ["２０液体"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -250]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 410]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-poll`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-skin${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-arm${no}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": ["$no = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}-${$no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["腕　服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}-${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["おっぱいの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tits${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["おっぱい下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}_tits`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["おっぱいタイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}_tits`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["おっぱい服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}_tits`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-head${no}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer16);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-h${no}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer17);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [1, 27, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["液体の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-liquid${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾーちんぽ"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [1, 39, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no =`${$e_name}-cock${num}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer21]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer21);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 39, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no =`${$e_name}-guy${num}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer21]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer21);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "詠唱後ろ姿", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-praying-back\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 22]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["２ 杖"]}, {"code": 408, "indent": 0, "parameters": ["３ボディ"]}, {"code": 408, "indent": 0, "parameters": ["４陰毛"]}, {"code": 408, "indent": 0, "parameters": ["５腋毛"]}, {"code": 408, "indent": 0, "parameters": ["６下着"]}, {"code": 408, "indent": 0, "parameters": ["７タイツ"]}, {"code": 108, "indent": 0, "parameters": ["８服"]}, {"code": 408, "indent": 0, "parameters": ["９ヘアー"]}, {"code": 408, "indent": 0, "parameters": ["１０ハット"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["１５オブジェクト"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 550]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["杖の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-weapon`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[4].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer6];"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer7];"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[1].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["  $picture = screen.pictures[$layer8];"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = $gameVariables.value(25).toString();"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-object${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "ガニ股アナル舐め", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-anal_licking\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 23]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 guy"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["5 body"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["19 squirting"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -300]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 300]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if($gameActors.actor(2).equips()[4] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] != null){"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["潮吹き"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirting`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 9, "name": "トワーク", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-twerk\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 24]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["3 body"]}, {"code": 408, "indent": 0, "parameters": ["5 ph"]}, {"code": 408, "indent": 0, "parameters": ["6 ah"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth sitagi"]}, {"code": 408, "indent": 0, "parameters": ["8 cloth tights"]}, {"code": 408, "indent": 0, "parameters": ["9 cloth huku"]}, {"code": 108, "indent": 0, "parameters": ["10 shake#{33}"]}, {"code": 408, "indent": 0, "parameters": ["15 hair"]}, {"code": 408, "indent": 0, "parameters": ["16 face"]}, {"code": 408, "indent": 0, "parameters": ["18 head dress"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 350]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no=`${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["ケツシェイク"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-shake${no}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 10, "name": "蹲踞フェラ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-sonkyo_bj\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 25]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 man"]}, {"code": 408, "indent": 0, "parameters": ["3 base"]}, {"code": 408, "indent": 0, "parameters": ["4 opend anus $33 #"]}, {"code": 408, "indent": 0, "parameters": ["5ph"]}, {"code": 408, "indent": 0, "parameters": ["6 ah"]}, {"code": 408, "indent": 0, "parameters": ["8 under wear"]}, {"code": 108, "indent": 0, "parameters": ["9 socks"]}, {"code": 408, "indent": 0, "parameters": ["10 outfit"]}, {"code": 408, "indent": 0, "parameters": ["12 hair"]}, {"code": 408, "indent": 0, "parameters": ["13 man arm"]}, {"code": 408, "indent": 0, "parameters": ["14 hat"]}, {"code": 408, "indent": 0, "parameters": ["15 man hand"]}, {"code": 108, "indent": 0, "parameters": ["19 kasokusen"]}, {"code": 408, "indent": 0, "parameters": ["20 yuge"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 450]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-guy${no}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オープンアヌス"]}, {"code": 111, "indent": 0, "parameters": [1, 24, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-anus_opend`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer4].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[4].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[3].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-guy_arm${no}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["id = $gameActors.actor(2).equips()[2].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["男の手の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 39, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-guy${no}_hand`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-kasoku`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 11, "name": "側面", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-side\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 26]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["5 cloth102 (tights)"]}, {"code": 408, "indent": 0, "parameters": ["6 cloth82 (outfit)"]}, {"code": 408, "indent": 0, "parameters": ["8 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["10 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["12 cloth92 (hat)"]}, {"code": 108, "indent": 0, "parameters": ["15 guy#{id(456)}"]}, {"code": 408, "indent": 0, "parameters": ["16 guy#{id(456)}_cloth"]}, {"code": 408, "indent": 0, "parameters": ["18 cock#{id(456)}-#{stage} if stage >= 1"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -25]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 350]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  let id = $gameActors.actor(2).equips()[4].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  let id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["  id = 102;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  let id = $gameActors.actor(2).equips()[1].id;"]}, {"code": 655, "indent": 0, "parameters": ["  id = 82;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  let id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["  id = 92;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = $gameVariables.value(25).toString();"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${no}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男服の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 1602, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-guy${no}_cloth`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男ちんぽカットインの表示"]}, {"code": 408, "indent": 0, "parameters": ["（射精含む）"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 2, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cock${no}-${num}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 12, "name": "種付けプレスgf", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-priestess-matting_press_gf\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 27]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 body#{skin}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["10 guy#{456}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["11 cock#{456}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["15 cum#{cum}-#{stage}"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 =  $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${no}-${num2}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${no}-${num2}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["反転させるかの処理"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 3}, {"id": 13, "name": "片足上げ乳揉み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-priestess-holding\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 28]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 guy_lower#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 body#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["6 underwear"]}, {"code": 408, "indent": 0, "parameters": ["7 socks"]}, {"code": 108, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["12 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["13 hat"]}, {"code": 408, "indent": 0, "parameters": ["15 guy_upper#{456}-#{28}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["19 squirting"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 500]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示 下レイヤー"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy_lower${no}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[4].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[1].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギアの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = $gameVariables.value(25).toString();"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["男の表示 上レイヤー"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy_upper${no}-${num2}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 456, 0, 21, 0]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-cum21`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["no = $gameVariables.value(40)"]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cum${no}`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-squirting`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["反転させるかの処理"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 3}, {"id": 14, "name": "リフトアップセックス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-lift_up_sex\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 29]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 guy_lower#{456}"]}, {"code": 408, "indent": 0, "parameters": ["3 body#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 ph"]}, {"code": 408, "indent": 0, "parameters": ["5 ah"]}, {"code": 408, "indent": 0, "parameters": ["6 underwear"]}, {"code": 408, "indent": 0, "parameters": ["7 socks"]}, {"code": 108, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["12 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["13 hat"]}, {"code": 408, "indent": 0, "parameters": ["15 guy_upper#{456}-#{28}"]}, {"code": 408, "indent": 0, "parameters": ["18 cum#{cum}"]}, {"code": 108, "indent": 0, "parameters": ["19 squirting"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示 下レイヤー"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-guy${no}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-body${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示 上レイヤー"]}, {"code": 111, "indent": 0, "parameters": [1, 456, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-arms${no}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 3}, {"id": 15, "name": "ガニ股フェラ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-BJGani<PERSON>\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["1 BG_under${BG}"]}, {"code": 408, "indent": 0, "parameters": ["3 guy${guy}"]}, {"code": 408, "indent": 0, "parameters": ["4 BG_top${BG}"]}, {"code": 408, "indent": 0, "parameters": ["8 base${skin}"]}, {"code": 408, "indent": 0, "parameters": ["9 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 ah"]}, {"code": 108, "indent": 0, "parameters": ["11 下着"]}, {"code": 408, "indent": 0, "parameters": ["12　衣装"]}, {"code": 408, "indent": 0, "parameters": ["13"]}, {"code": 408, "indent": 0, "parameters": ["14"]}, {"code": 408, "indent": 0, "parameters": ["15 hair${hair}"]}, {"code": 408, "indent": 0, "parameters": ["17 face${face}"]}, {"code": 108, "indent": 0, "parameters": ["18　ハット"]}, {"code": 408, "indent": 0, "parameters": ["19 cum${cum}"]}, {"code": 408, "indent": 0, "parameters": ["20 steam"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG_under${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${no}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["背景 上"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-BG_top${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${no}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["下着の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[4] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[4].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["タイツの表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[3] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[3].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[1] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[1].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["帽子の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(2).equips()[2] !== null) {"]}, {"code": 655, "indent": 0, "parameters": ["  id = $gameActors.actor(2).equips()[2].id;"]}, {"code": 655, "indent": 0, "parameters": ["  $no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 3}, {"id": 16, "name": "ちんぐり返し", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-chin<PERSON><PERSON>\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 31]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cock${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男の脚の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${no}_legs`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hands${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 3}, {"id": 17, "name": "うつ伏せ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-heroine-laying2\""]}, {"code": 122, "indent": 0, "parameters": [31, 31, 0, 0, 32]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -150]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["no = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair${no}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["アクメ"]}, {"code": 111, "indent": 0, "parameters": [12, "$squirt >= 1"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["OP"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 5}]}