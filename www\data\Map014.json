{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "PerituneMaterial_Taverne_loop", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "City_Ambi-Festival01-1", "pan": 0, "pitch": 100, "volume": 100}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "dark_night\nBGM固定\n王都\n拠点\n酒場\n公共の場", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 30, "data": [7428, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7452, 7462, 1613, 1614, 1615, 7464, 7452, 7449, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7814, 1613, 1614, 1615, 7811, 7810, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7820, 1613, 1614, 1615, 7817, 7816, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7810, 7814, 1613, 1614, 1615, 7811, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7810, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 7816, 7820, 1613, 1614, 1615, 7817, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7816, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1553, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1553, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1553, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 1591, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1553, 1553, 1553, 1553, 1553, 1553, 1554, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1554, 1553, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1554, 1554, 1554, 1554, 1553, 1554, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1554, 1553, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1554, 1553, 1553, 1554, 1553, 1553, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1553, 1554, 1554, 1553, 1553, 1554, 1553, 1553, 1553, 1553, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1553, 1554, 1554, 1553, 1553, 1554, 1554, 1554, 1553, 1553, 1553, 1554, 1553, 1553, 1553, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7448, 1554, 1553, 1553, 1554, 1554, 1553, 1553, 1554, 1554, 1553, 1554, 1553, 1553, 1554, 1553, 1553, 1553, 1553, 7456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7454, 7457, 7457, 7457, 7457, 7457, 7457, 7457, 7469, 1554, 1553, 7467, 7457, 7457, 7457, 7457, 7457, 7457, 7457, 7463, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3193, 3185, 3185, 3185, 3185, 3185, 3185, 3185, 3185, 3185, 3185, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3195, 3197, 0, 0, 3195, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3195, 3185, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 329, 330, 0, 0, 0, 328, 329, 329, 329, 329, 329, 329, 329, 329, 329, 329, 329, 329, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 89, 88, 89, 110, 111, 110, 111, 108, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 609, 0, 0, 0, 0, 0, 0, 23, 96, 97, 104, 105, 37, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 0, 0, 45, 45, 80, 81, 82, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 25, 32, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 40, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 17, 0, 0, 0, 0, 48, 49, 0, 0, 0, 8, 8, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 25, 0, 16, 17, 0, 56, 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 25, 0, 0, 0, 16, 17, 0, 8, 8, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 32, 33, 0, 0, 32, 33, 0, 24, 25, 0, 32, 33, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 57, 40, 41, 0, 0, 40, 41, 0, 0, 0, 0, 40, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 903, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエスト『地下下水の調査』　スライム戦後"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 203, "indent": 0, "parameters": [69, 0, 13, 12, 2]}, {"code": 203, "indent": 0, "parameters": [70, 0, 14, 14, 8]}, {"code": 203, "indent": 0, "parameters": [71, 0, 13, 14, 8]}, {"code": 203, "indent": 0, "parameters": [58, 0, 26, 27, 8]}, {"code": 205, "indent": 0, "parameters": [69, {"list": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [70, {"list": [{"code": 41, "parameters": ["h001", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["h001", 2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [71, {"list": [{"code": 41, "parameters": ["$sally", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$sally", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 1, 2, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 14, 1, 2, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 1, 2, 50, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 14, 1, 2, 60, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 14, 1, 2, 70, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-38"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_51"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_61"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_71"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-96"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 201, "indent": 0, "parameters": [0, 8, 23, 10, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 1, 2, 210, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 14, 1, 2, 220, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_211"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_221"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [903, 903, 0, 0, 98]}, {"code": 117, "indent": 0, "parameters": [46]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_250"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 903, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 9}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["クエスト『初心者訓練』　ラストシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 121, "indent": 0, "parameters": [19, 19, 0]}, {"code": 117, "indent": 0, "parameters": [946]}, {"code": 121, "indent": 0, "parameters": [19, 19, 1]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 203, "indent": 0, "parameters": [69, 0, 13, 12, 2]}, {"code": 203, "indent": 0, "parameters": [70, 0, 14, 14, 8]}, {"code": 203, "indent": 0, "parameters": [71, 0, 13, 14, 8]}, {"code": 203, "indent": 0, "parameters": [58, 0, 26, 27, 8]}, {"code": 205, "indent": 0, "parameters": [69, {"list": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [70, {"list": [{"code": 41, "parameters": ["h001", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["h001", 2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [71, {"list": [{"code": 41, "parameters": ["$sally", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$sally", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 1, 4, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 14, 1, 4, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-36"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["スクロール　他の客たちの会話"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 204, "indent": 0, "parameters": [4, 3, 4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-63"]}, {"code": 204, "indent": 0, "parameters": [6, 3, 4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-106"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 201, "indent": 0, "parameters": [0, 8, 23, 10, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 122, "indent": 0, "parameters": [904, 904, 0, 0, 98]}, {"code": 117, "indent": 0, "parameters": [46]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 904, "variableValid": true, "variableValue": 10}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 121, "indent": 1, "parameters": [8, 8, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 8, 22, 10, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 17}, {"id": 3, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_door_close\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 2}, {"id": 4, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_door_close\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1105, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameVariables.value(1105) === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["サブクエ：偉大な魔法使いを目指して"]}, {"code": 408, "indent": 0, "parameters": ["********************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 0, "parameters": ["key = [199, 1, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 122, "indent": 0, "parameters": [1105, 1105, 0, 0, 2]}, {"code": 201, "indent": 0, "parameters": [0, 199, 12, 14, 8, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 2}, {"id": 5, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_door_close\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 2}, {"id": 6, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_door_close\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 2}, {"id": 7, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 121, "indent": 1, "parameters": [8, 8, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 0, "parameters": [0, 8, 22, 10, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 17}, {"id": 8, "name": "マダム", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 6, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 6, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 6, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [53]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 3}, {"id": 9, "name": "司祭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_meal1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_meal2\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_meal3\")"]}, {"code": 122, "indent": 0, "parameters": [11, 11, 0, 3, 7, 2, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]", "\\v[80]"], 3, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 10]}, {"code": 111, "indent": 1, "parameters": [1, 11, 1, 13, 4]}, {"code": 119, "indent": 2, "parameters": ["所持金不足"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 30]}, {"code": 111, "indent": 1, "parameters": [1, 11, 1, 13, 4]}, {"code": 119, "indent": 2, "parameters": ["所持金不足"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 120]}, {"code": 111, "indent": 1, "parameters": [1, 11, 1, 13, 4]}, {"code": 118, "indent": 2, "parameters": ["所持金不足"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["C", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[80]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*食事処理*"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.hideFollowers();"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.refresh();"]}, {"code": 201, "indent": 0, "parameters": [0, 14, 18, 14, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["二人のとき"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 111, "indent": 0, "parameters": [1, 10, 0, 2, 1]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 34, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 50, 1]}, {"code": 111, "indent": 2, "parameters": [1, 83, 0, 50, 1]}, {"code": 122, "indent": 3, "parameters": [54, 54, 0, 4, "$gameActors.actor(1).tp"]}, {"code": 111, "indent": 3, "parameters": [1, 54, 0, 100, 4]}, {"code": 355, "indent": 4, "parameters": ["val_in_database(61, \"meal_play1\")"]}, {"code": 355, "indent": 4, "parameters": ["val_in_database(62, \"meal_play2\")"]}, {"code": 102, "indent": 4, "parameters": [["\\v[61]", "\\v[62] en(v[445]>=1)"], 1, 0, 2, 0]}, {"code": 402, "indent": 4, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 5, "parameters": ["*************************"]}, {"code": 408, "indent": 5, "parameters": ["机下パイズリ"]}, {"code": 408, "indent": 5, "parameters": ["*************************"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 9, 6)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "\\v[62] en(v[445]>=1)"]}, {"code": 108, "indent": 5, "parameters": ["*************************"]}, {"code": 408, "indent": 5, "parameters": ["机下足コキ"]}, {"code": 408, "indent": 5, "parameters": ["*************************"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 9, 7)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 102, "indent": 4, "parameters": [["\\v[80]"], 0, 0, 2, 0]}, {"code": 402, "indent": 4, "parameters": [0, "\\v[80]"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_110"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [4, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(1,20)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(2,20)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["食事終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_120"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [4, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(1,40)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(2,40)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["食事終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "C", 0]}, {"code": 117, "indent": 2, "parameters": [101]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_130-131"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [4, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(1,60)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(2,60)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 118, "indent": 1, "parameters": ["食事終了"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 6]}, {"code": 33, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 6]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["一人のとき"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 111, "indent": 0, "parameters": [1, 10, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["*************************"]}, {"code": 408, "indent": 1, "parameters": ["一人のとき　主人公"]}, {"code": 408, "indent": 1, "parameters": ["*************************"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 9, 4)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*************************"]}, {"code": 408, "indent": 1, "parameters": ["一人のとき　司祭"]}, {"code": 408, "indent": 1, "parameters": ["*************************"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 9, 5)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 118, "indent": 0, "parameters": ["食事終了"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.showFollowers();"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.refresh();"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Coin", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 125, "indent": 0, "parameters": [1, 1, 13]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 1, 0]}, {"code": 121, "indent": 1, "parameters": [9, 9, 0]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["飯　主人公ソロ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["飯　司祭ソロ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 203, "indent": 1, "parameters": [15, 0, 17, 14, 0]}, {"code": 205, "indent": 1, "parameters": [15, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["People5", 2]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["People5", 2]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-8"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Crash", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_9-11"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_12-13"]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_14-16"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [15, 0, 15, 16, 0]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["Damage2", 1]}, {"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Damage2", 1]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["エロイベ パイズリ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 9, 6, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 9, 6, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 14, 9, 6, 999, 0)"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[82]>=150)", "\\v[63]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["****************************************"]}, {"code": 408, "indent": 1, "parameters": ["パイズリ"]}, {"code": 408, "indent": 1, "parameters": ["****************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [468, 468, 0, 1, 453]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"胸\""]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [146]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110-111"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [146]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_112-124"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_130-133"]}, {"code": 108, "indent": 1, "parameters": ["********************"]}, {"code": 408, "indent": 1, "parameters": ["フェラチオ開始"]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [146]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_140-154"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [146]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_155-157"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_160-162"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_164-166"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_167"]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_168"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 117, "indent": 1, "parameters": [110]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_169"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 404]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[82]>=150)"]}, {"code": 108, "indent": 1, "parameters": ["****************************************"]}, {"code": 408, "indent": 1, "parameters": ["セックス"]}, {"code": 408, "indent": 1, "parameters": ["****************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [468, 468, 0, 1, 453]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [146]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "kuchu1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_202-211"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 14, 9, 6, 220, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 14, 9, 6, 230, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["*************************"]}, {"code": 408, "indent": 2, "parameters": ["もう少し静かに使う"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_221-223"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["*************************"]}, {"code": 408, "indent": 2, "parameters": ["ケツを叩いて更に激しく使う"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_231-234"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 444]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*************************"]}, {"code": 408, "indent": 1, "parameters": ["相席野郎"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_250"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_251-252"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_253"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_254"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 14, 9, 6, 260, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 14, 9, 6, 280, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["***********************************"]}, {"code": 408, "indent": 2, "parameters": ["ダメ"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_261-264"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 250, "indent": 2, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [146]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_266-268"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 442]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["***********************************"]}, {"code": 408, "indent": 2, "parameters": ["いいよ"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_281"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_282-284"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_285-287"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_288"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_289-290"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_291-292"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_293"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_294"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 14, 9, 6, 300, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 14, 9, 6, 350, 0)"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62] if(s[133]) en(v[83]>=150)"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 3, "parameters": ["***************************"]}, {"code": 408, "indent": 3, "parameters": ["気にしないで"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_301"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_302-303"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_304"]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 3, "parameters": [95]}, {"code": 250, "indent": 3, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [146]}, {"code": 117, "indent": 3, "parameters": [29]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_306-307"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_308"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62] if(s[133]) en(v[83]>=150)"]}, {"code": 108, "indent": 3, "parameters": ["***************************"]}, {"code": 408, "indent": 3, "parameters": ["司祭に見知らぬ男のちんぽをしゃぶらせる"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_351-353"]}, {"code": 108, "indent": 3, "parameters": ["両表示のグラフィック？"]}, {"code": 122, "indent": 3, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 99]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [146]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_355-360"]}, {"code": 122, "indent": 3, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 99]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 3, "parameters": [146]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_361-372"]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 3, "parameters": [95]}, {"code": 250, "indent": 3, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 99]}, {"code": 122, "indent": 3, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 3, "parameters": [146]}, {"code": 117, "indent": 3, "parameters": [29]}, {"code": 122, "indent": 3, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 3, "parameters": [465, 465, 0, 0, 3]}, {"code": 122, "indent": 3, "parameters": [466, 466, 0, 0, 3]}, {"code": 122, "indent": 3, "parameters": [467, 467, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 3, "parameters": [29]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_374-375"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_377-378"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["****************************************"]}, {"code": 408, "indent": 1, "parameters": ["何もしない"]}, {"code": 408, "indent": 1, "parameters": ["****************************************"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,-50)"]}, {"code": 655, "indent": 0, "parameters": ["tp_heal(2,-50)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 40, 0, 0, 0]}, {"code": 245, "indent": 0, "parameters": [{"name": "City_Ambi-Festival01-1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["エロイベ 机下足コキ"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["初期処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [468, 468, 0, 1, 453]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"足\""]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["開始"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["画像準備"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [147]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-53"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [147]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_54-85"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 9, 7, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 9, 7, 200, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["射精させてつかまつりそうろう"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 14, 9, 7, 102, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["***********************"]}, {"code": 408, "indent": 2, "parameters": ["射精させてつかまつりそうろう！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_103"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 14, 9, 7, 104, 0)"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 3, "parameters": ["***********************"]}, {"code": 408, "indent": 3, "parameters": ["射精させてつかまつりそうろう！！"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_105-107"]}, {"code": 117, "indent": 3, "parameters": [95]}, {"code": 250, "indent": 3, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [147]}, {"code": 117, "indent": 3, "parameters": [29]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_110-114"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 445]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 3, "parameters": ["……"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["……"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["……"]}, {"code": 118, "indent": 1, "parameters": ["……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,-50)"]}, {"code": 655, "indent": 0, "parameters": ["tp_heal(2,-50)"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 40, 0, 0, 0]}, {"code": 245, "indent": 0, "parameters": [{"name": "City_Ambi-Festival01-1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 12}, {"id": 10, "name": "モブ冒険者 10番", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 13"], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [463, 463, 0, 0, 561]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 0, 50]}, {"code": 122, "indent": 0, "parameters": [468, 468, 0, 0, 3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [515]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 14}, {"id": 11, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 8, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_1\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 15}, {"id": 12, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 12}, {"id": 13, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 13}, {"id": 14, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 12}, {"id": 15, "name": "NPC", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 2, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_9\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 16}, {"id": 16, "name": "NPC-町人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_7\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 15}, {"id": 17, "name": "NPC-町人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People5", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_7\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 16}, {"id": 18, "name": "モブおっさん１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_999"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"observe\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"ignore\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$test_content = \"kansatsu\""]}, {"code": 122, "indent": 0, "parameters": [170, 170, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Saint5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_101\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"ignore\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 250, "indent": 2, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2-4"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 14, 11, 12, 6, 0]}, {"code": 355, "indent": 2, "parameters": ["x = 16"]}, {"code": 655, "indent": 2, "parameters": ["y = 13"]}, {"code": 655, "indent": 2, "parameters": ["add_event = add_event(x, y, 2, 26, 6)"]}, {"code": 655, "indent": 2, "parameters": ["$temp_id = add_event.id"]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 205, "indent": 2, "parameters": [18, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [19, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 117, "indent": 2, "parameters": [61]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 204, "indent": 2, "parameters": [6, 3, 4]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 213, "indent": 2, "parameters": [18, 4, false]}, {"code": 213, "indent": 2, "parameters": [19, 4, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_5-8"]}, {"code": 250, "indent": 2, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_9"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 204, "indent": 2, "parameters": [4, 3, 4]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 201, "indent": 2, "parameters": [0, 14, 17, 12, 2, 0]}, {"code": 205, "indent": 2, "parameters": [9, {"list": [{"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 355, "indent": 2, "parameters": ["del_event($temp_id)"]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 250, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [18, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [19, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_201\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map14_ev18_p2_202\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_12"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_301\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_11"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_401\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_13"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_501\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_14"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_601\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_15-17"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["露出かセックスか"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_701\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map14_ev18_p2_702\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_18"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_19"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_20"]}, {"code": 119, "indent": 3, "parameters": ["露出かセックスか"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map14_ev18_p2_801\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21-23"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_24-25"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_26"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 85]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 84]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 122, "indent": 2, "parameters": [702, 702, 0, 0, 1]}, {"code": 118, "indent": 2, "parameters": ["終了"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 205, "indent": 2, "parameters": [9, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 33, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 6]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 33, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 6]}]}, {"code": 216, "indent": 2, "parameters": [0]}, {"code": 205, "indent": 2, "parameters": [18, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [19, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 245, "indent": 2, "parameters": [{"name": "City_Ambi-Festival01-1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_incompatibility\")"]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 702, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 29}, {"id": 19, "name": "ヤリゾー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": true, "switch2Id": 302, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [16, 16, 0, 4, "\"ヤリゾー\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 28, "y": 29}, {"id": 20, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor4", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_13\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 6, "y": 13}, {"id": 21, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_13\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 14}, {"id": 22, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_13\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 6, "y": 14}, {"id": 23, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 16}, {"id": 24, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 15}, {"id": 25, "name": "モブ冒険者とケツ揉みおじさん", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior3", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_2\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People2", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ケツ揉み"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**********************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [801]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4"]}, {"code": 108, "indent": 0, "parameters": ["**********************************************"]}, {"code": 408, "indent": 0, "parameters": ["以降『尻ぶっかけ状態』のときに発生"]}, {"code": 408, "indent": 0, "parameters": ["**********************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 72]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5-6"]}, {"code": 111, "indent": 1, "parameters": [0, 144, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_7-9"]}, {"code": 121, "indent": 2, "parameters": [144, 144, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 100, 4]}, {"code": 108, "indent": 2, "parameters": ["走って逃げ去る"]}, {"code": 118, "indent": 2, "parameters": ["ランナウェイ"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Run", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 201, "indent": 2, "parameters": [0, 8, 22, 10, 2, 0]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_12"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 82, 1, 83, 3]}, {"code": 108, "indent": 3, "parameters": ["エロイベ"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_20-23"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 25, 3)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["ランナウェイ"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["セクハラシーン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イニシャライズ"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 2, 1, 10]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 2, 1, 10]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"尻\""]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["射精"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 99]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["後ろからセクハラグラフィック"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [801]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["数値処理"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 15}, {"id": 26, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_13\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "berserker_fem", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 1"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [84]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map14_ev26_201\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_map14_ev26_202\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 125, "indent": 2, "parameters": [1, 0, 20]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3"]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"strength\""]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 5]}, {"code": 122, "indent": 2, "parameters": [15, 15, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 111, "indent": 2, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_4"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_100"]}, {"code": 122, "indent": 3, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 3, "parameters": [3]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 12}, {"id": 27, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor4", "direction": 8, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_13\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 13}, {"id": 28, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*******************"]}, {"code": 408, "indent": 0, "parameters": ["コハクソロの時"]}, {"code": 408, "indent": 0, "parameters": ["*******************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-7"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 8}, {"id": 29, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 13}, {"id": 30, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 9}, {"id": 31, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 16}, {"id": 32, "name": "NPC-町人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_7\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 15}, {"id": 33, "name": "渡り鳥", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 6, "y": 7}, {"id": 34, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor5", "direction": 8, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*******************"]}, {"code": 408, "indent": 0, "parameters": ["コハクソロの時"]}, {"code": 408, "indent": 0, "parameters": ["*******************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 10}, {"id": 35, "name": "NPC-町人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 2}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_7\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*******************"]}, {"code": 408, "indent": 0, "parameters": ["コハクソロの時"]}, {"code": 408, "indent": 0, "parameters": ["*******************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_avoid\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no_action\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 117, "indent": 1, "parameters": [801]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4-11"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_12"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_13"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_14-15"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 4, "y": 9}, {"id": 36, "name": "モブ冒険者&ぶっかけオヤジ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior3", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_2\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 1\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*******************"]}, {"code": 408, "indent": 0, "parameters": ["コハクソロの時"]}, {"code": 408, "indent": 0, "parameters": ["*******************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 117, "indent": 0, "parameters": [833]}, {"code": 121, "indent": 0, "parameters": [144, 144, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-8"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 10, "indent": null, "parameters": []}, {"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameSwitches.value(1) === true\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 15}, {"id": 37, "name": "モブ冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 6}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["下水はかつて自分の祖父の祖父のそのまた祖父が建造に参加した遺跡で"]}, {"code": 408, "indent": 0, "parameters": ["、それを下水に使うなんてとんでもない、ヒック。"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 11}, {"id": 38, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map8_2_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 0}, {"id": 39, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map8_2_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 0}, {"id": 40, "name": "扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_map8_2_1\")"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 0}, {"id": 41, "name": "料理人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People3", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 8}, {"id": 42, "name": "料理人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 6, "pattern": 1, "characterIndex": 5}, "moveFrequency": 5, "moveRoute": {"list": [{"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [120]}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [120]}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [120]}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 15, "indent": null, "parameters": [120]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": true, "wait": false}, "moveSpeed": 5, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 9}, {"id": 43, "name": "料理人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People3", "direction": 8, "pattern": 1, "characterIndex": 4}, "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 7}, {"id": 44, "name": "料理人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People1", "direction": 8, "pattern": 1, "characterIndex": 5}, "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 8}, {"id": 45, "name": "料理人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People1", "direction": 8, "pattern": 1, "characterIndex": 5}, "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 7}, {"id": 46, "name": "師範代", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "h001", "direction": 2, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 9"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"learn_skill_sword\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 117, "indent": 1, "parameters": [593]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 47, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 14}, {"id": 48, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 5}, {"id": 49, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 5}, {"id": 50, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 51, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 52, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 1}, {"id": 53, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 13}, {"id": 54, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 12}, {"id": 55, "name": "光", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["＠灯り1"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 6}, {"id": 56, "name": "トイレへの扉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 122, "indent": 0, "parameters": [55, 55, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [48]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 3, 7, 0, 0]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 3, 5, -1, 1]}, {"code": 201, "indent": 0, "parameters": [0, 7, 11, 14, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 6}, {"id": 57, "name": "ヤリゾー前の酒", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!_clutter", "direction": 4, "pattern": 2, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 8, "variableValid": true, "variableValue": 21}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 10}, {"id": 58, "name": "寝取らせイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_11\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 31, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["寝取らせ　隣座らせてフェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [974]}, {"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["起動判定"]}, {"code": 408, "indent": 0, "parameters": ["**************"]}, {"code": 111, "indent": 0, "parameters": [0, 157, 1]}, {"code": 119, "indent": 1, "parameters": ["末末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["**************"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map14_ev58_p2_1a\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]en(v[83]>=80)", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]en(v[83]>=80)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 201, "indent": 1, "parameters": [0, 14, 14, 14, 8, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 1, "parameters": [1, 0, 14, 12, 2]}, {"code": 205, "indent": 1, "parameters": [1, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 108, "indent": 1, "parameters": ["司祭座りグラフィック表示"]}, {"code": 122, "indent": 1, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [151]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-21"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [151]}, {"code": 111, "indent": 1, "parameters": [1, 542, 0, 100, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_23"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_25"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_26-27"]}, {"code": 250, "indent": 1, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_29"]}, {"code": 108, "indent": 1, "parameters": ["**********************"]}, {"code": 408, "indent": 1, "parameters": ["チェインつなげられるか判定"]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 0, 120]}, {"code": 111, "indent": 1, "parameters": [1, 83, 1, 54, 4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_31-32"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_require_chain_love\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**********************"]}, {"code": 408, "indent": 1, "parameters": ["ステージ２"]}, {"code": 408, "indent": 1, "parameters": ["**********************"]}, {"code": 118, "indent": 1, "parameters": ["ステージ２"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [151]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_35-48"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_49-52"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_53-54"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_56-62"]}, {"code": 108, "indent": 1, "parameters": ["**********************"]}, {"code": 408, "indent": 1, "parameters": ["チェインつなげられるか判定"]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 0, 140]}, {"code": 111, "indent": 1, "parameters": [1, 83, 1, 54, 4]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_64-65"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_require_chain_love\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**********************"]}, {"code": 408, "indent": 1, "parameters": ["ステージ３"]}, {"code": 408, "indent": 1, "parameters": ["**********************"]}, {"code": 118, "indent": 1, "parameters": ["ステージ３"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_68-70"]}, {"code": 108, "indent": 1, "parameters": ["フェラチオグラフィック準備"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [151]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_72-75"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_76-79"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_80-82"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_83-89"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90-98"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 1920]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 2, 5, 15]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 581]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 108, "indent": 1, "parameters": ["*　口の中に精子ためこんで口まわりに陰毛と精子ついてる差分"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [151]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_104-105"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 216, "indent": 1, "parameters": [0]}, {"code": 201, "indent": 1, "parameters": [0, 7, 11, 13, 8, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -2"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 14, 58, 2, 108, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 14, 58, 2, 111, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_107"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 14, 58, 2, 117, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 14, 58, 2, 144, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(63, 14, 58, 2, 114, 0)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_109"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62] if(s[33])", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 118, "indent": 3, "parameters": ["主人公にフェラチオ"]}, {"code": 108, "indent": 3, "parameters": ["フェラ"]}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 245, "indent": 3, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [39, 39, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [142]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_118-132"]}, {"code": 117, "indent": 3, "parameters": [95]}, {"code": 250, "indent": 3, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 2]}, {"code": 122, "indent": 3, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [142]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_133"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 1902]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 2, 5, 15]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_134-136"]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 3, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 3, "parameters": [24, 24, 0, 0, 3]}, {"code": 122, "indent": 3, "parameters": [40, 40, 0, 0, 2]}, {"code": 117, "indent": 3, "parameters": [142]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_137-138"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["var_from_sheet(61, 14, 58, 2, 140, 0)"]}, {"code": 102, "indent": 3, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_141-143"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 5, 1]}, {"code": 355, "indent": 4, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 404]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 101, "indent": 4, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 4, "parameters": [""]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62] if(s[33])"]}, {"code": 108, "indent": 3, "parameters": ["おしっこ浄化"]}, {"code": 250, "indent": 3, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [29, 29, 0, 0, 1]}, {"code": 122, "indent": 3, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [133]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_145-147"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [1901, 2000, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [89, 89, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 3, "parameters": ["疲れてるから～"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_115"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_112"]}, {"code": 119, "indent": 2, "parameters": ["主人公にフェラチオ"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[80]"]}, {"code": 119, "indent": 1, "parameters": ["末末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [1, 0, 0, 0, 2]}, {"code": 205, "indent": 0, "parameters": [1, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末末"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 12}, {"id": 59, "name": "酒場", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 2, "pattern": 1, "characterIndex": 5}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 302, "indent": 0, "parameters": [0, 180, 0, 0, true]}, {"code": 605, "indent": 0, "parameters": [0, 181, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 182, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 183, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 184, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 185, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 9}, {"id": 60, "name": "EV060", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [4]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 121, "indent": 0, "parameters": [87, 87, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [478, 478, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [475, 475, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [476, 476, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [477, 477, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [480, 480, 0, 0, 100]}, {"code": 117, "indent": 0, "parameters": [651]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 20}, {"id": 61, "name": "酒場ヤリゾーイベント", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": true, "switch2Id": 32, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 1 && $gameSwitches.value(503) === false &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameSwitches.value(32) === true &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameSwitches.value(643) === true\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["酒場　ヤリゾーイベント　Root"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント開始準備"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 870, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 61, 2)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 870, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(0, 61, 3)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 870, 0, 2, 0]}, {"code": 121, "indent": 1, "parameters": [5, 5, 0]}, {"code": 102, "indent": 1, "parameters": [["１", "２"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "１"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 61, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "２"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(0, 61, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [5, 5, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": false, "switch2Id": 32, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["Ya-ta-1-1"]}, {"code": 408, "indent": 0, "parameters": ["司祭ソロの時 ヤリゾーが司祭を見つけてケツに落書き"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント開始準備"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [463, 463, 0, 0, 561]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 0, 50]}, {"code": 122, "indent": 0, "parameters": [468, 468, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [464, 464, 0, 1, 465]}, {"code": 122, "indent": 0, "parameters": [464, 464, 1, 1, 466]}, {"code": 108, "indent": 0, "parameters": ["キャラチップのグラの名前とIDを取得"]}, {"code": 355, "indent": 0, "parameters": ["var id = this._eventId; // これは現在のイベントのIDを取得します。"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["var targetEvent = $gameMap.event(10);"]}, {"code": 655, "indent": 0, "parameters": ["if (targetEvent) {"]}, {"code": 655, "indent": 0, "parameters": ["    $sprite_name = targetEvent.characterName();"]}, {"code": 655, "indent": 0, "parameters": ["    $sprite_id = targetEvent.characterIndex();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 8, 11, 6]}, {"code": 201, "indent": 0, "parameters": [0, 14, 17, 12, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィック準備"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [111]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-8"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["スカートをめくる"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [111]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-15"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツに便女の落書き"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [111]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [111]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["立ち去る"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null, "parameters": []}, {"code": 7, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_31-32"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツ触られる"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [801]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-46"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 61, 2, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 61, 2, 200, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[82]>=200 && v[82]>v[83])"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["***************************************"]}, {"code": 408, "indent": 1, "parameters": ["無視して立ち去る"]}, {"code": 408, "indent": 1, "parameters": ["***************************************"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[82]>=200 && v[82]>v[83])"]}, {"code": 108, "indent": 1, "parameters": ["***************************************"]}, {"code": 408, "indent": 1, "parameters": ["相手をする"]}, {"code": 408, "indent": 1, "parameters": ["***************************************"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [801]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-202"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 201, "indent": 1, "parameters": [0, 7, 11, 12, 8, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["var cha = $gamePlayer; // If you want to move the player. If you want to move a specific event, use $gameMap.event(eventId)."]}, {"code": 655, "indent": 1, "parameters": ["cha.forceMoveRoute({"]}, {"code": 655, "indent": 1, "parameters": ["    repeat: false,"]}, {"code": 655, "indent": 1, "parameters": ["    skippable: false,"]}, {"code": 655, "indent": 1, "parameters": ["    wait: true,"]}, {"code": 655, "indent": 1, "parameters": ["    list: ["]}, {"code": 655, "indent": 1, "parameters": ["        {code: Game_Character.ROUTE_CHANGE_IMAGE, parameters: [$sprite_name, $sprite_id]},"]}, {"code": 655, "indent": 1, "parameters": ["        {code: 0} // End of the list"]}, {"code": 655, "indent": 1, "parameters": ["    ]"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 117, "indent": 1, "parameters": [70]}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_210-219"]}, {"code": 108, "indent": 1, "parameters": ["***************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [70]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-223"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [870, 870, 0, 0, 1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 8, 22, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 643, "switch1Valid": false, "switch2Id": 32, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 99999}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 4, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["Ya-ta-1-2"]}, {"code": 408, "indent": 0, "parameters": ["ザーメンドリンク"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベント開始準備"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["キャラチップのグラの名前とIDを取得"]}, {"code": 355, "indent": 0, "parameters": ["var id = this._eventId; // これは現在のイベントのIDを取得します。"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["var targetEvent = $gameMap.event(10);"]}, {"code": 655, "indent": 0, "parameters": ["if (targetEvent) {"]}, {"code": 655, "indent": 0, "parameters": ["    $sprite_name = targetEvent.characterName();"]}, {"code": 655, "indent": 0, "parameters": ["    $sprite_id = targetEvent.characterIndex();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 8, 11, 6]}, {"code": 201, "indent": 0, "parameters": [0, 14, 17, 12, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 34, "indent": null, "parameters": []}, {"code": 36, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-17"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 7, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["トイレいく"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 204, "indent": 0, "parameters": [4, 7, 4]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["トイレ入る"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 205, "indent": 0, "parameters": [56, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [56, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 36, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_extreme", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["出てくる"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 205, "indent": 0, "parameters": [56, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [56, {"list": [{"code": 44, "indent": null, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 36, "indent": null, "parameters": []}, {"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Open4", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 204, "indent": 0, "parameters": [6, 7, 3]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 6, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 3, "indent": null, "parameters": []}, {"code": 19, "indent": null, "parameters": []}, {"code": 38, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 6, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィック準備"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_51-58"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["いぶかしげな顔で見る"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-62"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_63"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_64-67"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["口をつける"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_70"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["うぷっ！"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_80-86"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラスから口離す　口回りにチン毛"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-92"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["口膨らませてうぷっ顔"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["げぇっぷ"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_110-111"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["恥ずかしそうな顔"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [112]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-126"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 122, "indent": 0, "parameters": [870, 870, 0, 0, 2]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 1904]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1000]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 2000]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1000]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 1, "parameters": [113, 113, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 8, 22, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 12}, {"id": 62, "name": "PMイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PMイベントご飯食べにいく"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["画像準備"]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 8)"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 62, 1, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 62, 1, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 14, 62, 1, 300, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["テーブルの上に足を置くのは～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 14, 62, 1, 110, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 14, 62, 1, 150, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["汚いから"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_111"]}, {"code": 111, "indent": 2, "parameters": [0, 5, 1]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, -5]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["勃起しちゃうから"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_111"]}, {"code": 111, "indent": 2, "parameters": [0, 5, 1]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["足裏はまんこって教えてあげなきゃ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-202"]}, {"code": 111, "indent": 1, "parameters": [0, 5, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["なんでいつもテーブルに足を～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301-303"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["帰ろう"]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_900-901"]}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["key = [14, 62, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 1117, 1]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 10, 10, 17, 2, 0]}, {"code": 121, "indent": 1, "parameters": [1117, 1117, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 4}, {"id": 63, "name": "妻の寝取らせ依頼の後", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["妻の寝取らせ依頼"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※判定に失敗してる場合でヤリゾーと従者契約、かつヤリゾー生存の場"]}, {"code": 408, "indent": 0, "parameters": ["合"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※フェードアウトインで酒場"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["ossan", 0]}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 34, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["※終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 0}, {"id": 64, "name": "サキュバスイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster2", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["V6(修道院からの引継ぎ）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [0, 0, 17, 14, 6]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [0, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 64, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 64, 1, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 14, 64, 1, 30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63] en(v[183]>=30)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["飲みすぎだ"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["乳揉む"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] en(v[183]>=30)"]}, {"code": 108, "indent": 1, "parameters": ["おしっこのみたい"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31"]}, {"code": 355, "indent": 1, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 183]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["セクション２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 64, 1, 40, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_41-43"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_50-51"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, -1]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 29, "indent": null, "parameters": [2]}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 44, "indent": null, "parameters": [{"name": "Blow4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 19, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["Damage4", 3]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "indent": null, "parameters": [2]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "indent": null, "parameters": [{"name": "Blow4", "pan": 0, "pitch": 100, "volume": 80}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["Damage4", 3]}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["key = [213, 1, \"A\"]"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 201, "indent": 0, "parameters": [0, 213, 13, 9, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 250, "variableValid": true, "variableValue": 80}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 29, "y": 28}, {"id": 65, "name": "休憩イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [945]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 15}, {"id": 66, "name": "酔いつぶれてる冒険者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 12, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage1", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 2 &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameVariables.value(5) === 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["             Math.min($gameVariables.value(12) === 2)\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 66, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 14, 66, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["key = [199, 3, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, true)"]}, {"code": 201, "indent": 1, "parameters": [0, 199, 12, 12, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 810, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 14}, {"id": 67, "name": "クエスト用ＮＰＣ『王都に迫る悪魔』証人２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1012, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(1012) >= 2"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 67, 1, 100, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 14, 67, 1, 200, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] if(s[133])", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] if(s[133])"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭にフェラチオさせる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 216, "indent": 1, "parameters": [1]}, {"code": 201, "indent": 1, "parameters": [0, 14, 2, 7, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110-111"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 216, "indent": 1, "parameters": [0]}, {"code": 119, "indent": 1, "parameters": ["成功"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["******************************************************"]}, {"code": 408, "indent": 1, "parameters": ["交渉"]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"koushou\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 118, "indent": 2, "parameters": ["成功"]}, {"code": 108, "indent": 2, "parameters": ["しゃぶらせて情報得た時用に判定成功スイッチをオン"]}, {"code": 121, "indent": 2, "parameters": [80, 80, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_210"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [1012, 1012, 1, 0, 1]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(8, 100, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_220"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(8, 100, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 15}, {"id": 68, "name": "クエスト『不死なる者』　ラストの食事シーン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1212, "variableValid": true, "variableValue": 27}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [946]}, {"code": 203, "indent": 0, "parameters": [0, 0, 17, 12, 2]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$heroine", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [63, 0, 18, 14, 8]}, {"code": 205, "indent": 0, "parameters": [63, {"list": [{"code": 41, "parameters": ["vampire", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["vampire", 0], "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 14, 68, 1, 10, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-27"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 122, "indent": 0, "parameters": [1212, 1212, 0, 0, 28]}, {"code": 201, "indent": 0, "parameters": [0, 137, 13, 7, 4, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1212, "variableValid": true, "variableValue": 99}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 2}, {"id": 69, "name": "配置用NPC1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 29}, {"id": 70, "name": "配置用NPC２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 29}, {"id": 71, "name": "配置用NPC３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 29}, null]}