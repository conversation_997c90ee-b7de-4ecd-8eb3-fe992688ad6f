function Access(t,e){"string"==typeof e&&(e=e.split(/[\s./]/));let n=t;for(let t=0;t<e.length;t++){if(null==n)return null;n=n[e[t]]}return null==n?null:n}function GetType(t){return t.__proto__.constructor.name}function StrMul(t,e){let n="";for(let i=0;i<e;i++)n+=t;return n}function Var(t){return $gameVariables._data[t]||0}function Vars(){let t=[];for(const e of arguments)t.push($gameVariables._data[e]||0);return t}function SetVar(t,e){$gameVariables.setValue(t,e)}function SetVarSilent(t,e){$gameVariables._data[t]=e}function Switch(t){return $gameSwitches._data[t]||0}function Switches(){let t=[];for(const e of arguments)t.push($gameSwitches._data[e]||0);return t}function SetSwitch(t,e){$gameSwitches.setValue(t,e)}function SetVarSilent(t,e){$gameSwitches._data[t]=!!e}function HexStringRandomFormat(t){let e="";for(let n=0;n<t.length;n++)e+="r"===t[n]?RandomInt(16).toString(16):t[n];return e}function RandomFloat(t=1){return Math.random()*t}function RandomInt(t=1){return Math.floor(Math.random()*t)}function RandomRange(t=1,e=t){return Math.random()*(e-t)+t}function RandomRangeInt(t=1,e=t){return Math.floor(Math.random()*(e+1-t))+t}function QuickCache(){this.Map=new Map}function TypeObj(t={}){let e=Object.keys(t);for(let n=0;n<e.length;n++){let i=e[n],o=t[i];"string"==typeof o&&(o=o.split(/[ ]+/));for(let t of o)this[t]=i}}function LoadFileUTF8Escaped(t){return $Node.fs.readFileSync(t,"utf8").replace(/\r/g,"")}Window_Base.prototype.drawBitmap=function(t,e,n,i=0,o=0,r=t.width,a=t.height){this.contents.blt(t,i,o,r,a,e,n,t.width,t.height)},Window_Base.prototype.drawPicture=function(t,e=0,n=0){let i=ImageManager.loadPicture(t);return this.contents.blt(i,0,0,i.width,i.height,e,n,i.width,i.height),i},Window_Base.prototype.drawBitmapCut=function(t,e=0,n=0,i=0,o=0,r,a){r=r||t.width,a=a||t.height,this.contents.blt(t,i,o,r,a,e,n,r,a)},Window_Base.prototype.drawPictureCut=function(t,e=0,n=0,i=0,o=0,r,a){let h=ImageManager.loadPicture(t);return r=r||h.width,a=a||h.height,this.contents.blt(h,i,o,r,a,e,n,r,a),h},Window_Base.prototype.drawLine=function(t,e,n=this.contents.width,i=2){},Window_Base.prototype.drawHistoryTextEx=function(t,e,n){if(t){let i={index:0,x:e,y:n,left:e};for(i.text=this.convertEscapeCharacters(t),i.height=this.calcTextHeight(i,!1);i.index<i.text.length;)this.processCharacter(i);return i.x-e}return 0},Window_Base.prototype.drawTextWrap=function(t,e,n,i=this.contents.width,o=0,r="left"){let a,h=[],s=[],c=[/^\n+/,/^[ ]+/,/^[^ |^\n]+/],u=t.length;for(let e=0;e<u;e++)for(let n=0;n<c.length;n++)(a=c[n].exec(t))&&(t=t.slice(a[0].length),e+=a[0].length-1,h.push(a[0]),s.push(n));let l=0,d=e,p=n,f=!1,g=this.contents.fontSize;for(let t=0;t<h.length;t++){let n=h[t],a=this.contents.measureTextWidth(n);((l+=a)>i||0===s[t])&&(d=e,p+=g+o,l=a,1!==s[t]&&0!==s[t]||(f=!0)),f||(this.contents.drawText(n,d,p,i,g,r),d+=a),f=!1}},Window_Base.prototype.drawTextWrapMeasured=function(){let t,e=[],n=[],i=[/^\n+/,/^[ ]+/,/^[^ |^\n]+/],o=text.length;for(let r=0;r<o;r++)for(let o=0;o<i.length;o++)(t=i[o].exec(text))&&(text=text.slice(t[0].length),r+=t[0].length-1,e.push(t[0]),n.push(o));let r=0,a=x,h=y,s=!1,c=this.contents.fontSize;for(let t=0;t<e.length;t++){let i=e[t],o=this.contents.measureTextWidth(i);((r+=o)>width||0===n[t])&&(a=x,h+=c+margin,r=o,1!==n[t]&&0!==n[t]||(s=!0)),s||(this.contents.drawText(i,a,h,width,c,align),a+=o),s=!1}},Game_Event.prototype.jumpTo=function(t,e){let n=this._x-t,i=this._y-e;this.jump(-n,-i)},Game_Event.prototype.restore=function(){this._erased=!1,this.refresh()},Game_Map.prototype.setTileId=function(t,e,n,i){let o=$dataMap.width,r=(n*$dataMap.height+e)*o+t;$dataMap.data[r]=i,SceneManager._scene.children[0]._tilemap.refresh()},Rectangle.prototype.Add=function(t){this.x+=t.x,this.y+=t.y,this.width+=t.width,this.height+=t.height},Rectangle.prototype.AddPositon=function(t){this.x+=t.x,this.y+=t.y},Rectangle.prototype.AddSize=function(t){this.width+=t.width,this.height+=t.height},Rectangle.prototype.AddSizeToPositon=function(t){this.x+=t.width,this.y+=t.height},Rectangle.prototype.AddWidthToPositon=function(t){this.x+=t.width},Rectangle.prototype.AddHeightToPositon=function(t){this.y+=t.height},QuickCache.prototype.Add=function(t,e=[]){let n;(n=this.Map.get(t))?this.Map.set(t,n.concat(e)):this.Map.set(t,e)},QuickCache.prototype.Set=function(t,e=[]){return this.Map.set(t,e)},QuickCache.prototype.Get=function(t){return this.Map.get(t)},QuickCache.prototype.ForEeach=function(t){let e,n=[];for(const i of this.Map.keys(this))(e=t(i,this.Map.get(i)))&&n.push(e);return n};