{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "harmonic-worldmap", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "BlueSky", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 3, "width": 20, "data": [1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 1544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "初期処理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 126, "indent": 0, "parameters": [886, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameActors.actor(1).changeEquip(5, $dataArmors[78]);"]}, {"code": 108, "indent": 0, "parameters": ["******************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ダンジョンテスト版"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 121, "indent": 0, "parameters": [702, 702, 0]}, {"code": 122, "indent": 0, "parameters": [4, 4, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [5, 5, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [6, 6, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [4, 4, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [160, 160, 0, 0, 6]}, {"code": 122, "indent": 0, "parameters": [49, 49, 0, 4, "\"俺\""]}, {"code": 122, "indent": 0, "parameters": [82, 82, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [84, 84, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [83, 83, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [9, 9, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [98, 98, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [187, 187, 0, 0, 50]}, {"code": 122, "indent": 0, "parameters": [188, 188, 0, 0, 100]}, {"code": 121, "indent": 0, "parameters": [38, 38, 0]}, {"code": 108, "indent": 0, "parameters": ["ステータス設定"]}, {"code": 117, "indent": 0, "parameters": [25]}, {"code": 118, "indent": 0, "parameters": ["言語選択"]}, {"code": 102, "indent": 0, "parameters": [["日本語", "English", "Spanish if(v[1]==999)"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "日本語"]}, {"code": 122, "indent": 1, "parameters": [1, 1, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [396, 396, 0, 0, 4]}, {"code": 122, "indent": 1, "parameters": [80, 80, 0, 4, "\"キャンセル\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "English"]}, {"code": 122, "indent": 1, "parameters": [1, 1, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [396, 396, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [80, 80, 0, 4, "\"Cancel\""]}, {"code": 320, "indent": 1, "parameters": [1, "<PERSON>"]}, {"code": 320, "indent": 1, "parameters": [2, "<PERSON><PERSON><PERSON>"]}, {"code": 320, "indent": 1, "parameters": [21, "Ya<PERSON><PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Spanish if(v[1]==999)"]}, {"code": 122, "indent": 1, "parameters": [1, 1, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [80, 80, 0, 4, "\"Cancel\""]}, {"code": 320, "indent": 1, "parameters": [1, "<PERSON>"]}, {"code": 320, "indent": 1, "parameters": [2, "<PERSON><PERSON><PERSON>"]}, {"code": 320, "indent": 1, "parameters": [21, "Ya<PERSON><PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [948]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の設定"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["ON", "OFF"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "ON"]}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "OFF"]}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の設定"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 0, "parameters": [["ON", "OFF"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "ON"]}, {"code": 122, "indent": 1, "parameters": [36, 36, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "OFF"]}, {"code": 122, "indent": 1, "parameters": [36, 36, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 108, "indent": 0, "parameters": ["寝取らせの設定"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7"]}, {"code": 102, "indent": 0, "parameters": [["ON", "OFF"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "ON"]}, {"code": 121, "indent": 1, "parameters": [31, 31, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "OFF"]}, {"code": 121, "indent": 1, "parameters": [31, 31, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["寝取られの設定"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8"]}, {"code": 102, "indent": 0, "parameters": [["ON", "OFF"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "ON"]}, {"code": 121, "indent": 1, "parameters": [32, 32, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "OFF"]}, {"code": 121, "indent": 1, "parameters": [32, 32, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["スカトロの設定"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101"]}, {"code": 102, "indent": 0, "parameters": [["ON", "OFF"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "ON"]}, {"code": 121, "indent": 1, "parameters": [33, 33, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "OFF"]}, {"code": 121, "indent": 1, "parameters": [33, 33, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["一人称変更は一旦封印"]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 99, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 250, "indent": 1, "parameters": [{"name": "Skill2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 102, "indent": 1, "parameters": [["俺", "僕", "私"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "俺"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 0, 4, "\"俺\""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "僕"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 0, 4, "\"僕\""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "私"]}, {"code": 122, "indent": 2, "parameters": [49, 49, 0, 4, "\"私\""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_on\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_off\")"]}, {"code": 111, "indent": 0, "parameters": [1, 19, 0, 99999, 0]}, {"code": 355, "indent": 1, "parameters": ["SceneManager.call(MEO_SceneStartSettings)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 108, "indent": 0, "parameters": ["難易度の設定"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_difficulty1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_difficulty2\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_difficulty3\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(64, \"select_difficulty4\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9"]}, {"code": 102, "indent": 0, "parameters": [["\\c[24]\\v[61]", "\\v[63]", "\\c[18]\\v[64]"], -1, 1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[24]\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["FAP"]}, {"code": 122, "indent": 1, "parameters": [3, 3, 0, 0, 0]}, {"code": 127, "indent": 1, "parameters": [99, 0, 0, 1, false]}, {"code": 128, "indent": 1, "parameters": [60, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["Normal"]}, {"code": 122, "indent": 1, "parameters": [3, 3, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[18]\\v[64]"]}, {"code": 108, "indent": 1, "parameters": ["Hard"]}, {"code": 122, "indent": 1, "parameters": [3, 3, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 118, "indent": 0, "parameters": ["スキップ選択"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_no\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_yes\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 129, "indent": 1, "parameters": [1, 0, 0]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 121, "indent": 1, "parameters": [6, 6, 0]}, {"code": 201, "indent": 1, "parameters": [0, 333, 17, 11, 8, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["名前入力"]}, {"code": 356, "indent": 1, "parameters": ["InputNamePrompt 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1002"]}, {"code": 108, "indent": 1, "parameters": ["司祭名前入力"]}, {"code": 356, "indent": 1, "parameters": ["InputNamePrompt 2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1003"]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 129, "indent": 1, "parameters": [1, 0, 0]}, {"code": 129, "indent": 1, "parameters": [2, 0, 0]}, {"code": 318, "indent": 1, "parameters": [0, 1, 0, 31]}, {"code": 355, "indent": 1, "parameters": ["$gameActors.actor(1).changeEquipById(5, 78); "]}, {"code": 655, "indent": 1, "parameters": ["$gameActors.actor(2).changeEquipById(5, 79);"]}, {"code": 125, "indent": 1, "parameters": [0, 0, 100]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 122, "indent": 1, "parameters": [4, 4, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [5, 5, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [6, 6, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [442, 442, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [443, 443, 0, 1, 442]}, {"code": 122, "indent": 1, "parameters": [542, 542, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [402, 402, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [404, 404, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [6, 6, 0]}, {"code": 111, "indent": 1, "parameters": [0, 31, 0]}, {"code": 121, "indent": 2, "parameters": [133, 133, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 17, 9, 13, 2, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 241, "indent": 1, "parameters": [{"name": "funenotabi", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 99999999, 0]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_story_navi_ch1_1\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [902, 902, 0, 0, 99]}, {"code": 121, "indent": 1, "parameters": [202, 202, 0]}, {"code": 121, "indent": 1, "parameters": [207, 207, 0]}, {"code": 121, "indent": 1, "parameters": [209, 209, 0]}, {"code": 121, "indent": 1, "parameters": [211, 211, 0]}, {"code": 121, "indent": 1, "parameters": [212, 212, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2000"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2001"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2002"]}, {"code": 108, "indent": 1, "parameters": ["Ver0.80で下記の処理は一時封印"]}, {"code": 111, "indent": 1, "parameters": [0, 620, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2003-2006"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [35, 35, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [36, 36, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [36, 36, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ケツ毛も一旦封印"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 999999, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [37, 37, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 2, "parameters": [37, 37, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_101"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 121, "indent": 1, "parameters": [33, 33, 0]}, {"code": 122, "indent": 1, "parameters": [38, 38, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [38, 38, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 108, "indent": 0, "parameters": ["セクハラは強制オンに"]}, {"code": 408, "indent": 0, "parameters": ["（選択を一旦封印）"]}, {"code": 121, "indent": 0, "parameters": [30, 30, 0]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 9999999, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_6"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 121, "indent": 2, "parameters": [30, 30, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 121, "indent": 2, "parameters": [30, 30, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 121, "indent": 1, "parameters": [31, 31, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 121, "indent": 1, "parameters": [31, 31, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 121, "indent": 1, "parameters": [32, 32, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 121, "indent": 1, "parameters": [32, 32, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 19, "y": 0}, null]}