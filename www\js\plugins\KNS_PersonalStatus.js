/*:
 * @plugindesc ver.1.1.2 人物ごとのステータスウィンドウを実装します
 * <AUTHOR>
 * 
 * @param CharaList
 * @text キャラ情報
 * @type struct<CharaData>[]
 * @default ["{\"CharaIconImage\":\"chara1\",\"CharaImage\":\"{\\\"VarId\\\":\\\"4090\\\",\\\"Images\\\":\\\"[]\\\"}\",\"CharaImageVarId\":\"4090\",\"ProfileBlocks\":\"[\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"4090\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Images\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"[]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProfileBlockItems\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"[\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"3\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"]\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"]\"}","{\"CharaIconImage\":\"chara2\",\"CharaImage\":\"{\\\"VarId\\\":\\\"4091\\\",\\\"Images\\\":\\\"[\\\\\\\"stand-pri0\\\\\\\",\\\\\\\"stand-pri0\\\\\\\",\\\\\\\"stand-pri1\\\\\\\"]\\\"}\",\"CharaImageVarId\":\"4091\",\"ProfileBlocks\":\"[\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"4091\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Images\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"[\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"cutin-womb0\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"cutin-womb0\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"cutin-womb2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProfileBlockItems\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"[\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Flavor text for pussy\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"3\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Flavor text for anus\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"4\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Flavor text for mouth\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"]\\\\\\\\\\\\\\\"}\\\\\\\"]\\\",\\\"[\\\\\\\"{\\\\\\\\\\\\\\\"Image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"4091\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Images\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"[]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProfileBlockItems\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"[\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Flavor text for pussy\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"]\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"{\\\\\\\\\\\\\\\"Image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"4091\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Images\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"[]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ProfileBlockItems\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"[\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"VarId\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"ProfileCsvKey\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Flavor text for pussy\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"]\\\\\\\\\\\\\\\"}\\\\\\\"]\\\"]\"}"]
 * 
 * @param ImgUnavailableScene
 * @text 未開放シーン画像
 * @type file
 * @dir img/pictures
 * @default unavailable
 * 
 * @param PictureScale
 * @text 立ち絵表示倍率
 * @type number
 * @decimals 3
 * @default 0.350
 * 
 * @param CharaWindow
 * @text ■キャラウィンドウ
 * @type struct<WindowInfo>
 * @default {"x":"0","y":"0","width":"240","height":"960","maxCols":"1","spacing":"8","spacingVertical":"16"}
 * 
 * @param PageWindow
 * @text ■ページウィンドウ
 * @type struct<WindowInfo>
 * @default {"x":"240","y":"0","width":"1040","height":"72"}
 * 
 * @param DetailWindow
 * @text ■詳細ウィンドウ
 * @type struct<WindowInfo>
 * @default {"x":"240","y":"72","width":"1040","height":"888"}
 * 
 * 
 * @help
 * ■ver.1.1.2 更新内容(2023-11-24)
 * - 詳細ウィンドウを十字キー・マウスホイールでスクロール可能に。
 * 　詳細ウィンドウは文字の高さが均等なとき56行まで入力可能。
 * （ツクールの仕様上2048pxまでしか安定的に画像の高さを指定できないため）
 * 
 * ■ver.1.1.1 更新内容(2023-11-18)
 * - \nがアクター名表示の制御文字と被っていたため、
 * 　CSVエディタ上で直接改行することで改行とみなすよう限定
 * - メニューコマンド上でアクター選択を挟む仕様ミスがあったため修正
 * - 値用の変数名表示に半角スペース1つ分のスペースを挿入
 * 
 * ■ver.1.1.0 更新内容(2023-11-17)
 * - profile.csvに制御文字を追加(\c[N], \v[N], \n（改行）等)
 * - profile.csvに$1と記入すると変数名に置換されるように対応
 * - 文字列キーが適応された文字列からデフォルトの変数名表示を撤廃、
 * 　変数値のみの表示の変数には以前のまま表示される
 * 
 * ■パラメータ「キャラ情報」の階層
 * キャラ情報にキャラの表示設定やページ、ブロックの設定などが
 * 集約されているためパラメータ設定が少々複雑になっています。
 * 以下をご参考ください
 * 
 * ●キャラ情報
 * Ｌ＊サムネ
 * Ｌ＊立ち絵
 * Ｌ＊キャラリスト表示フラグ変数
 * Ｌ　ページ集
 * 　Ｌ　ページ１
 * 　　Ｌ　ブロック１
 * 　　　Ｌ＊ブロック画像
 * 　　　Ｌ項目１
 * 　　　　Ｌ＊項目変数
 * 　　　　Ｌ＊項目文字列キー
 * 　　　Ｌ　項目２
 * 　　　　Ｌ＊項目変数
 * 　　　　Ｌ＊項目文字列キー
 * 　　Ｌ　ブロック２
 * 　　　Ｌ＊ブロック画像
 * 　　　Ｌ……
 * 　Ｌ　ページ２
 * 　　Ｌ……
 * 
 * ■メニューコマンドの文言追加
 * csv/logsheet.csvに以下の行を追加することでこの
 * シーンを呼び出すコマンド名を指定します
 * Menu Personal Command,人物リスト,Persona,,,,
 * 
 * ■profile.csvについて
 * キャラの項目テキストを多言語化するためcsv/profile.csvを
 * 追加しました。
 * 
 * 当該CSVの列形式は以下のように設定してください。
 * ・文言キーワード
 * ・変数条件値
 * ・日本語
 * ・英語
 * ……
 * 
 * 項目の変数IDと変数条件値を比較し、項目の変数IDが
 * 変数条件値以下であればそのテキストを表示します。
 * （ツクールのイベントのページ条件と同様の挙動で、
 * 　同じキーワードで変数を参照する場合は変数条件値が
 * 　若い順にCSVに記述してください）
 * 
 */
/*~struct~WindowInfo:
 * @param x
 * @text X座標
 * @type number
 * @default 0
 * @min -65536
 * @max 65535
 * 
 * @param y
 * @text Y座標
 * @type number
 * @default 0
 * @min -65536
 * @max 65535
 * 
 * @param width
 * @text ウィンドウ横幅
 * @type number
 * @default 1
 * @min 1
 * @max 65535
 * 
 * @param height
 * @text ウィンドウ縦幅
 * @type number
 * @default 1
 * @min 1
 * @max 65535
 */
/*~struct~CharaData:
 * @param CharaIconImage
 * @text キャラサムネ名
 * @type file
 * @dir img/Stats_Mode
 * @default CharacterIcon_1
 * 
 * @param CharaImage
 * @text キャラ立ち絵名+変数
 * @type struct<VariableImage>
 * @default 
 * 
 * @param CharaImageVarId
 * @text キャラ表示フラグ変数ID
 * @type variable
 * @default 1
 * @desc 変数値が0のときはキャラを選択できず、サムネも未開放のものになります。
 * 
 * @param ProfileBlocks
 * @text ページ集＆ブロック指定
 * @type struct<ProfileBlock>[][]
 * @default []
 * @desc ページ・ブロックの情報を入力します。
 * 
 */
/*~struct~ProfileBlock:
 * @param Image
 * @text ブロック画像
 * @type struct<VariableImage>
 * @default 
 * 
 * @param ProfileBlockItems
 * @text ブロック情報
 * @type struct<ProfileBlockItem>[]
 * @default []
 * @desc ブロック上に表示する項目を指定します。
 */
/*~struct~ProfileBlockItem:
 * @param VarId
 * @text 変数ID
 * @type variable
 * @default 1
 * 
 * @param ProfileCsvKey
 * @text テキスト用キー
 * @type string
 * @default 
 * @desc 文言キーワードを指定します。カラにした場合は変数値がそのまま表示されます。
 */
/*~struct~VariableImage:
 * @param VarId
 * @text 変数ID
 * @type variable
 * @default 4090
 * 
 * @param Images
 * @text 変数値に対応する画像群
 * @type file[]
 * @dir img/Stats_Mode
 * @default 
 * @desc 変数値と対応するIDの画像が選ばれます。
 */

//===============================================
// new KNS_PersonalStatus
//===============================================
const KNS_PersonalStatus = {
    name: 'KNS_PersonalStatus',
    param: null,
    parseNumber: function(obj, key){
        return obj[key] = Number(obj[key]);
    },
    parseWindowInfo: function(parent, key){
        const obj = JsonEx.parse(parent[key]);
        this.parseNumber(obj, 'x');
        this.parseNumber(obj, 'y');
        this.parseNumber(obj, 'width');
        this.parseNumber(obj, 'height');
        return parent[key] = obj;
    },
    parseVariableImage: function(parent, key){
        const obj = JsonEx.parse(parent[key]);
        obj.VarId = Number(obj.VarId || 0);
        obj.Images = JsonEx.parse(obj.Images);
        return parent[key] = obj;
    },
    getVariableImage: function(info){
        let bmp = info.Images[Math.max(0,
            Math.min(info.Images.length - 1, $gameVariables.value(info.VarId))
        )];
        return bmp ? ImageManager.knsLoadStatsMode(bmp) : null;
    },
    loadAllImages: function(){
        ImageManager.knsLoadStatsMode(this.param.ImgUnavailableScene);
        this.param.CharaList.forEach(function(chara){
            ImageManager.knsLoadStatsMode(chara.CharaIconImage);
            this.getVariableImage(chara.CharaImage);
            chara.ProfileBlocks.forEach(function(page){
                page.forEach(function(block){
                    this.getVariableImage(block.Image);
                }, this);
            }, this);
        }, this);
    },
    blockImageWidth: 144,
    characterWidth: 256
};
(function(){
    this.param = PluginManager.parameters(this.name);

    this.param.ImgUnavailableScene = String(this.param.ImgUnavailableScene);
    this.param.PictureScale = Number(this.param.PictureScale) || 0;

    this.param.CharaList = JsonEx.parse(this.param.CharaList).map(function(json){
        const obj = JsonEx.parse(json);
        obj.CharaImage = this.parseVariableImage(obj, "CharaImage");
        obj.CharaIconImage = String(obj.CharaIconImage) || "";
        obj.CharaImageVarId = Number(obj.CharaImageVarId) || 0;
        obj.ProfileBlocks = JsonEx.parse(obj.ProfileBlocks).map(function(json){
            return JsonEx.parse(json).map(function(json){
                const obj = JsonEx.parse(json);
                obj.Image = this.parseVariableImage(obj, "Image");
                obj.ProfileBlockItems = JsonEx.parse(obj.ProfileBlockItems).map(function(json){
                    const obj = JsonEx.parse(json);
                    obj.VarId = Number(obj.VarId) || 0;
                    obj.ProfileCsvKey = String(obj.ProfileCsvKey) || "";
                    return obj;
                }, this);
                return obj;
            }, this);
        }, this);
        return obj;
    }, this);

    this.parseWindowInfo(this.param, "CharaWindow");
    this.parseWindowInfo(this.param, "PageWindow");
    this.parseWindowInfo(this.param, "DetailWindow");

    //===============================================
    // alias ImageManager
    //===============================================
    ImageManager.knsLoadStatsMode = function(filename, hue) {
        return this.loadBitmap('img/Stats_Mode/', filename, hue, true);
    };

    //===============================================
    // alias Window_MenuCommand
    //===============================================
    const _Window_MenuCommand_addMainCommands = Window_MenuCommand.prototype.addMainCommands;
    Window_MenuCommand.prototype.addMainCommands = function() {
        _Window_MenuCommand_addMainCommands.apply(this, arguments);
        this.addCommand($LogSheetCSV.Get('Menu Personal Command'), 'personalStatus');
    };

    //===============================================
    // alias Scene_Menu
    //===============================================
    const _Scene_Menu_createCommandWindow = Scene_Menu.prototype.createCommandWindow;
    Scene_Menu.prototype.createCommandWindow = function() {
        _Scene_Menu_createCommandWindow.apply(this, arguments);
        this._commandWindow.setHandler('personalStatus', this.knsCommandPersonalStatus.bind(this));
    };

    Scene_Menu.prototype.knsCommandPersonalStatus = function() {
        SceneManager.push(Scene_KnsPersonalStatus);
    };
    
}).call(KNS_PersonalStatus);

//===============================================
// new Window_KnsPersonalStatusBase
//===============================================
class Window_KnsPersonalStatusBase extends Window_Base{
    initialize(rect){
        super.initialize(rect.x, rect.y, rect.width, rect.height);
    }
}

//===============================================
// new Window_KnsPersonalStatusSelectable
//===============================================
class Window_KnsPersonalStatusSelectable extends Window_Selectable{
    initialize(rect, selectableInfo){
        this._knsSelectableInfo = selectableInfo;
        super.initialize(rect.x, rect.y, rect.width, rect.height);
        this.deactivate();
    }
    makeItemList(){
        this._knsData = [];
    }
    maxItems(){
        return this._knsData ? this._knsData.length : 0;
    }
}

//===============================================
// new Scene_KnsPersonalStatus
//===============================================
class Scene_KnsPersonalStatus extends Scene_MenuBase{
    create(){
        super.create();
        this.createCharaWindow();
        this.createPageWindow();
        this.createDetailWindow();
        this._knsCharaWindow.setHelpWindow(this._knsPageWindow);
        this._knsPageWindow.setHelpWindow(this._knsDetailWindow);
        KNS_PersonalStatus.loadAllImages();
    }
    start(){
        super.start();
        this._knsCharaWindow.refresh();
        this._knsPageWindow.refresh();
        this._knsDetailWindow.refresh();
    }
    createCharaWindow(){
        this._knsCharaWindow = new Window_KnsPersonalStatusCharacter(KNS_PersonalStatus.param.CharaWindow);
        this._knsCharaWindow.setHandler('ok', this.knsOnCharaOk.bind(this));
        this._knsCharaWindow.setHandler('cancel', this.popScene.bind(this));
        this._knsCharaWindow.refresh();
        this._knsCharaWindow.activate();
        this._knsCharaWindow.select(0);
        this.addWindow(this._knsCharaWindow);
    }
    createPageWindow(){
        this._knsPageWindow = new Window_KnsPersonalStatusPage(KNS_PersonalStatus.param.PageWindow);
        this._knsPageWindow.setHandler('cancel', this.knsOnPageCancel.bind(this));
        this.addWindow(this._knsPageWindow);
    }
    createDetailWindow(){
        this._knsDetailWindow = new Window_KnsPersonalStatusDetail(KNS_PersonalStatus.param.DetailWindow);
        this.addWindow(this._knsDetailWindow);
    }

    knsOnCharaOk(){
        this._knsPageWindow.activate();
        this._knsPageWindow.select(0);
    }
    knsOnPageCancel(){
        this._knsCharaWindow.activate();
        this._knsPageWindow.deselect();
    }
}


//===============================================
// new Window_KnsPersonalStatusCharacter < Window_KnsPersonalStatusSelectable
//===============================================
class Window_KnsPersonalStatusCharacter extends Window_KnsPersonalStatusSelectable{
    itemTextAlign(){ return 'center'; }
    maxCols(){ return 1; }
    spacing(){ return 24; }
    updateHelp(){
        const index = this.index();
        this._helpWindow.knsSetInfo(
            this.knsIsEnabled(index) ? this.knsGetItem(index) :  null
        );
    }
    refresh(){
        this._knsData = KNS_PersonalStatus.param.CharaList;
        super.refresh();
    }

    knsGetItem(index){
        return this._knsData ? this._knsData[index] : null;
    }
    knsGetImage(index){
        const item = this.knsGetItem(index);
        if (this.knsIsEnabled(index) && item.CharaImage){
            return ImageManager.knsLoadStatsMode(item.CharaIconImage);
        }else{
            return ImageManager.knsLoadStatsMode(KNS_PersonalStatus.param.ImgUnavailableScene);
        }
    }
    itemHeight(){ return this.contents.width * 0.75; }
    drawItem(index){
        const img = this.knsGetImage(index);
        if (img){
            const rect = this.itemRect(index);
            const wid = 8;
            const hei = 8;
            this.contents.blt(img, 0, 0, img.width, img.height,
                rect.x + wid / 2, rect.y + hei / 2,
                rect.width - wid, rect.height - hei
            );
        }
    }
    isCurrentItemEnabled(){
        return this.knsIsEnabled(this.index());
    }
    knsIsEnabled(index){
        const item = this.knsGetItem(index);
        return item && $gameVariables.value(item.CharaImageVarId) > 0;
    }
}

//===============================================
// new Window_KnsPersonalStatusPage < Window_KnsPersonalStatusSelectable
//===============================================
class Window_KnsPersonalStatusPage extends Window_KnsPersonalStatusSelectable{
    itemTextAlign(){ return 'center'; }
    maxCols(){ return 10; }
    spacing(){ return 24; }
    knsSetInfo(charaInfo){
        this._knsData = charaInfo || [];
        this.updateHelp();
    }
    maxItems(){
        return this._knsData && this._knsData.ProfileBlocks ? this._knsData.ProfileBlocks.length : 0;
    }
    updateHelp(){
        if (!this._helpWindow || !this._knsData){ return; }
        this._helpWindow.knsSetInfo(
            this._knsData || null,
            this.index()
        );
        this.refresh();
    }
    drawItem(index){
        const rect = this.itemRectForText(index);
        this.drawText(index + 1,
            rect.x, rect.y, rect.width, 
            this.itemTextAlign()
        );
    }
    processWheel() {
        if (this.isOpenAndActive()) {
            const threshold = 20;
            if (Math.abs(TouchInput.wheelY) >= threshold){
                this._helpWindow.knsScroll(TouchInput.wheelY);
            }else if (Input.isPressed('down')){
                this._helpWindow.knsScroll(threshold);
            }else if (Input.isPressed('up')){
                this._helpWindow.knsScroll(-threshold);
            }
        }
    }
}

//===============================================
// new Window_KnsPersonalStatusDetail < Window_KnsPersonalStatusBase
//===============================================
class Window_KnsPersonalStatusDetail extends Window_KnsPersonalStatusBase{
    initialize(rect){
        this._knsTextBitmap = new Bitmap(rect.width - KNS_PersonalStatus.characterWidth, 2040);
        super.initialize(rect);
        this._knsCharacterSprite = new Sprite();
        this._knsCharacterSprite.position.set(
            this.width - KNS_PersonalStatus.characterWidth * 0.5 - 8,
            this.height - 8
        );
        this._knsCharacterSprite.anchor.set(0.5, 1);
        this._knsCharacterSprite.scale.set(
            KNS_PersonalStatus.param.PictureScale,
            KNS_PersonalStatus.param.PictureScale
        );
        this.addChild(this._knsCharacterSprite);
    }
    knsSetInfo(chara, index){
        this._knsData = [chara, index];
        this.refresh();
    }
    knsGetBottomY(){
        return Math.max(this.contents.height - this.height, 0);
    }
    knsScroll(y){
        this.origin.y = Math.max(0, Math.min(Math.floor(this.origin.y + y), this.knsGetBottomY()));
        this.knsUpdateArrows();
    }
    knsUpdateArrows(){
        this.downArrowVisible = this.origin.y < this.knsGetBottomY();
        this.upArrowVisible = this.origin.y > 0;
    }
    refresh(){
        this.contents.clear();
        this._knsCharacterSprite.bitmap = null;
        this.downArrowVisible = false;
        this.upArrowVisible = false;
        if (!this._knsData){ return; }
        const chara = this._knsData[0];
        const index = this._knsData[1];
        if (!chara || index === -1){ return; }
        const page = chara.ProfileBlocks[index];
        if (!page){ return; }
        const bmp = KNS_PersonalStatus.getVariableImage(chara.CharaImage);
        if (bmp){ this._knsCharacterSprite.bitmap = bmp; }

        this.contents = this._knsTextBitmap;
        this.resetFontSettings();

        let y = 0;
        const allWidth = this.contents.width;
        page.forEach(function(blocks, i){
            if (i !== 0){
                this.contents.fillRect(0, y + 3, allWidth, 2, "#ffffff88");
                y += 8;
            }
            let imgHeight = 0;
            const bmp = KNS_PersonalStatus.getVariableImage(blocks.Image);
            if (bmp){
                const w = KNS_PersonalStatus.blockImageWidth;
                const h = w * bmp.height / bmp.width;
                this.contents.blt(bmp,
                    0, 0, bmp.width, bmp.height,
                    allWidth - w, y, w, h
                );
                imgHeight = h;
            }

            let textHeight = 0;
            let textY = y;
            blocks.ProfileBlockItems.forEach(function(block){
                let lineHeight = this.drawItem(block, textY);
                textY += lineHeight;
                textHeight += lineHeight;
            }, this);
            y += Math.max(textHeight, imgHeight);
        }, this);
        
        this.contents = new Bitmap(this._knsTextBitmap.width, y + this.lineHeight());
        this.contents.blt(this._knsTextBitmap,
            0, 0, this._knsTextBitmap.width,
            Math.min(this.contents.height, this._knsTextBitmap.height),
            0, 0
        );
        this._knsTextBitmap.clear();
        this.origin.y = 0;
        this.knsUpdateArrows();
    }
    drawItem(block, y){
        this.changeTextColor(this.systemColor());
        let titleWidth = 0;
        let text;
        const varName = $dataSystem.variables[block.VarId];
        const value = $gameVariables.value(block.VarId);
        if (block.ProfileCsvKey){
            text = String(
                KNS_CsvReader.getProfileTerm(block.ProfileCsvKey, value)
            ).replace(
                /\$1/g, varName
            );
        }else{
            if (block.VarId !== 0){
                titleWidth = this.drawTextEx("\\c[16][" + varName + "]\\c[0] ", 0, y);
            }
            this.changeTextColor(this.normalColor());
            text = String(value);
        }
        return this.knsDrawTextAutoline(
            text,
            titleWidth,
            y,
            this.contents.width - KNS_PersonalStatus.blockImageWidth
        ) - y;
    }
}