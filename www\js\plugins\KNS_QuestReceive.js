/*:
 * @plugindesc ver.1.0.0 KNSクエスト受注シーンの移植です
 * <AUTHOR>
 *
 * @param BASE_TERMS
 * @text 言語別用語集
 * @type string[][]
 * @default ["[\"%1を手に入れた！\",\"%1を%2個手に入れた！\",\"%1\\\\g手に入れた！\",\"『%1』を\\\\n受注しますか？\",\"『%1』を\\\\n受注した！\",\"はい\",\"いいえ\"]","[\"Obtained %1!\",\"Obtained %2 of %1!\",\"Obtained %1\\\\g!\",\"Will you receive\\\\n\\\"%1\\\"?\",\"You received\\\\n\\\"%1\\\"!\",\"Yes\",\"No\"]","[\"¡Obtuve %1!\",\"¡Obtuve %2 de %1!\",\"¡Obtuve %1\\\\g!\",\"¿Recibirás\\\\n\\\"%1\\\"?\",\"¡Recibiste\\\\n\\\"%1\\\"!\",\"Si\",\"No\"]","[\"获得%1了！\",\"获得%2%1了！\",\"获得%1\\\\g了！\",\"您要订购\\\\n『%1』吗？\",\"您收到\\\\n『%1』吗！\",\"是\",\"不\"]"]
 * @desc [道具入手（単数）, 道具入手（複数）, お金入手, 受注確認, 受注確定, はい, いいえ]
 *
 * @param SE_RECEIVED
 * @text 【効果音】クエスト受注決定
 * @type struct<AudioSe>
 * @default {"name":"Open5","volume":"80","pitch":"100","pan":"0"}
 *
 * @help
 * RGSSとJSの互換性の問題で変更できないスクリプトを
 * 置き換えるため、イベントコマンド内の以下の
 * スクリプトを実行前に書き替えます
 * （KNS_QuestBaseの下に設置してください）
 * 
 * 
 * ■ver.1.0.2 追加
 * 全クエストボタンを追加しました。
 * コマンド名はlogsheet.csvに以下のキーで指定してください。
 * 
 * quest_receive_all_items
 *
 * 
 * SceneManager.call(Scene_KNSReceiveQuest)
 * => SceneManager.push(Scene_KNSReceiveQuest);
 *
 * KNS::QuestList.get_reward(クエストID)
 * => KNS_QuestReceive.get_reward(クエストID);
 *
 * $game_variables[2].include?(クエストID)
 * => KNS_QuestReceive.isQuestReceived(クエストID);
 *
 * ver.1.0.0(2023-05-18)
 * - デモ
 * ver.1.0.1(2023-07-30)
 * - クエスト受注シーンの継承元を変更
 * ver.1.0.2(2024-08-26)
 * - 全難易度ボタンを追加
 */
/*~struct~AudioSe:
 * @param name
 * @type se
 * @default Open5
 *
 * @param volume
 * @type number
 * @min 0
 * @max 100
 * @default 80
 *
 * @param pitch
 * @type number
 * @min 0
 * @max 200
 * @default 100
 *
 * @param pan
 * @type number
 * @min -100
 * @max 100
 * @default 0
 *
 */

if (typeof KNS_RgssTranslator !== "undefined"){
	[
		[/KNS::QuestList\.get_reward/g, "KNS_QuestReceive.get_reward"],
		[/\$game_variables\[2\]\.include\?/g, "KNS_QuestReceive.isQuestReceived"],
	].forEach(function(pat){ KNS_RgssTranslator.addScriptPattern(...pat); });
}

//====================================================
// new KNS_QuestReceive
//====================================================
const KNS_QuestReceive = {
	name: "KNS_QuestReceive",
	// 以下受注シーン
	seReceived: {name: "Open5", volume: 80, pitch: 100, pan: 0},
	receiveVarId: 2,

	BASE_TERMS: null, // single item, plural items, gold, check, ok, yes, no
	getTerm(index){ return this.BASE_TERMS[KNS_CsvReader.getLanguageId()][index]; },
	getItemText: function(item, size){
		return this.getTerm(size === 1 ? 0 : 1).format(item.name, size);
	},
	getGoldText: function(gold){
		return this.getTerm(2).format(gold);
	},
	isQuestReceived: function(questId){
		const list = $gameVariables.value(this.receiveVarId);
		return Array.isArray(list) && list.contains(questId);
	},
	get_reward: function(id, show=true){
		$csvQuestList[id].rewards.forEach(function(item){
			if (typeof item[0] === 'number'){
				$gameParty.gainGold(item[0]);
				if (show){ $LogWindow.Add(this.getGoldText(item[0])); }
			}else{
				$gameParty.gainItem(item[0], item[1]);
				if (show){ $LogWindow.Add(this.getItemText(item[0], item[1])); }
			}
		}, this);
	},
	evalCondition: function(item){
		for (let line of item.release_conds.replace(
			/\$v/g, "$gameVariables._data"
		).replace(
			/\$s/g, "$gameSwitches._data"
		).split("\n")){
			if (eval(line) === false){ return false; };
		}
		return true;
	},
};

(function(){
	//====================================================
	// new KNS_QuestBase
	//====================================================
	this.param = PluginManager.parameters(this.name);
	this.BASE_TERMS = JsonEx.parse(this.param.BASE_TERMS).map(function(obj){
		return JsonEx.parse(obj).map(function(str){ return str.replace(/\\n/g, "\n")});
	});
	this.SE_RECEIVED = JsonEx.parse(this.param.SE_RECEIVED);
}).call(KNS_QuestReceive);

//====================================================
// new Window_KNSQuestSelect
//====================================================
class Window_KNSQuestSelect extends Window_Selectable{
	initialize(){
		const w = this.windowWidth();
		const h = this.windowHeight();
		super.initialize((Graphics.boxWidth - w) / 2, (Graphics.boxHeight - h) / 2, w, h);
		this.openness = 0;
	}
	windowWidth(){ return 800; }
	windowHeight(){ return 256; }
	maxItems(){ return 2; }
	maxCols(){ return 2; }
	itemRect(i){
		const rect = super.itemRect(i);
		rect.y += 160;
		return rect;
	}
	knsFormatWithTitle(index){
		return KNS_QuestReceive.getTerm(index).format(
			KNS_QuestBase.parseAsPlaneText(this._data.title)
		).split("\n");
	}
	knsSetData(data){
		this._data = data;
		this.knsCheckReceived();
	}
	knsCheckReceived(){
		this.contents.clear();
		this.contents.fontSize = 32;
		this.knsFormatWithTitle(3).forEach(function(str, i){
			this.drawText(str, 0, i * 44 + 64, this.contents.width, 'center');
		}, this);
		this.contents.fontSize = this.standardFontSize();
		this.drawAllItems();
		this.activate();
		this.open();
		this.select(0);
	}
	knsDoneReceived(){
		this.deselect();
		this.contents.clear();
		this.contents.fontSize = 32;
		this.knsFormatWithTitle(4).forEach(function(str, i){
			this.drawText(str, 0, i * 44 + 64, this.contents.width, 'center');
		}, this);
		this.contents.fontSize = this.standardFontSize();
	}
	drawItem(index){
		const rect = this.itemRectForText(index);
		this.drawText(KNS_QuestReceive.getTerm(5 + index), rect.x, rect.y, rect.width, "center");
	}
}

//====================================================
// new Window_KNSReceiveCategory
//====================================================
class Window_KNSReceiveCategory extends Window_KNSQuestCategory{
	maxItems(){ return KNS_QuestBase.MaxStarSize + 2; }
	drawItem(index){
		const rect = this.itemRectForText(index);
		if (index === 0) {
			this.drawText($LogSheetCSV.Get('quest_receive_all_items'), rect.x, rect.y, rect.width, 'center');
		} else {
			const bmp = KNS_QuestBase.generateStarBitmap(index, this.standardFontSize());
			this.contents.blt(bmp, 0, 0, bmp.width, bmp.height,
				rect.x + Math.floor((rect.width - bmp.width) / 2),
				rect.y + Math.floor((rect.height - bmp.height) / 2),
				bmp.width, bmp.height
			);
		}
	}
}

//====================================================
// new Window_KNSReceiveList
//====================================================
class Window_KNSReceiveList extends Window_KNSQuestList{
	knsGetAllData(){
		super.knsGetAllData();
		const mapId = $gameMap.mapId();
		this._allData = this._allData.filter(function(item){
			return item.id !== 0 && (
				item.map_id === 0 || item.map_id === mapId
			) && KNS_QuestReceive.evalCondition(item);
		});
	}
	makeItemList(){
		const level = this._categoryId;
		this._data = this._allData.filter(function(item){
			return (level === 0 || item.difficulty === level) && KNS_QuestBase.is_quest_unreleased(item.id);
		}, this);
	}
}

//====================================================
// new Window_KNSQuestSelect
//====================================================
class Window_KNSReceiveDetail extends Window_KNSQuestDetail{
	knsDrawAllProgress(y, headerWidth, valueWidth){ return y; }
}

//====================================================
// new Window_KNSQuestSelect
//====================================================
class Scene_KNSReceiveQuest extends Scene_KNSQuestBase{
	knsGetCategoryWindow(){ return new Window_KNSReceiveCategory() }
	knsGetListWindow(x, y, w, h){ return new Window_KNSReceiveList(x, y, w, h); }
	knsGetDetailWindow(x, y, w, h){ return new Window_KNSReceiveDetail(x, y, w, h); }
	create(){
		$gameVariables.setValue(KNS_QuestReceive.receiveVarId, []);
		super.create();
		this.knsCreateSelectWindow();
	}
	knsCreateSelectWindow(){
		this._knsSelectWindow = new Window_KNSQuestSelect();
		this._knsSelectWindow.setHandler("ok", this.knsOnSelectOk.bind(this));
		this._knsSelectWindow.setHandler("cancel", this.knsOnSelectCancel.bind(this));
		this.addChild(this._knsSelectWindow);
	}
	knsOnListWindowOk(){
		this._knsSelectWindow.knsSetData(this.knsGetItem());
	}
	knsOnSelectOk(){
		if (this._knsSelectWindow.index() === 0){
			this._knsSelectWindow.knsDoneReceived();
			const item = this.knsGetItem();
			$gameVariables.value(KNS_QuestReceive.receiveVarId).push(item.id);
			$gameVariables.setValue(item.var_id, 1);
			AudioManager.playSe(KNS_QuestReceive.seReceived);
			Input.clear();
		}else{
			this.knsOnSelectCancel();
		}
	}
	knsOnSelectCancel(){
		this._knsSelectWindow.close();
		this._knsListWindow.activate();
	}
	update(){
		super.update();
		if (
			this._knsSelectWindow.active === false &&
			this._knsSelectWindow.openness === 255 &&
			this._knsListWindow.deactivate && (
				Input.isTriggered('ok') || Input.isTriggered('cancel') ||
				TouchInput.isTriggered()
			)
		){
			this._knsSelectWindow.close();
			this._knsListWindow.refresh();
			this._knsListWindow.activate();
			this._knsDetailWindow.refresh();
		}
	}
}