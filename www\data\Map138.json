{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "キャンプ関係（追加分）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["キャンプ-主人公がテントからちんぽ出す"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-tent_bj\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 1, 1)"]}, {"code": 108, "indent": 0, "parameters": ["主人公脚　奥"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-protag_leg`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["PM"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-PM_base`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 1, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-PM_ph`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-PM_ah`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-PM_hair`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-PM_face0`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-PM_steam`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-protag${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["cum = $gameVariables.value(40);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["テント"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-tent`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer21]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ギガダウン"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-GigaDown\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 1, 2)"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-${num}ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ギガチャド持ち上げセックス"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-GigaLiftSex\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 1, 3)"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-${num}ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["潮"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-squirt1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 1}, {"id": 2, "name": "露出系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["露出系　お散歩"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-walking\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 2, 1)"]}, {"code": 108, "indent": 0, "parameters": ["モブ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-mob`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-protag`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["首輪"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-collar`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["目隠し"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-blind`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子漏れ"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer16)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}, {"id": 3, "name": "各リージョン系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イチャラブ-青の洞窟"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-BlueCave\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 3, 1)"]}, {"code": 108, "indent": 0, "parameters": ["ＢＧ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num}-cock`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num}-hat`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num1}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28);"]}, {"code": 655, "indent": 1, "parameters": ["cum = $gameVariables.value(40);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-s${num}-cum${cum}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 355, "indent": 0, "parameters": ["num1 = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-s${num1}-guy${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボート"]}, {"code": 355, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-boat${alpha}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オール"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-oar`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer16);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["水"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-water`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["モブ"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-mob`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イチャラブ(寝取らせ） - PoVフェラチオ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-slumPOV\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 3, 1)"]}, {"code": 108, "indent": 0, "parameters": ["ＢＧ"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer1]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["間男"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy21_lower`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 355, "indent": 0, "parameters": ["stage = $gameVariables.value(28)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy1_cock${stage}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-pri_arm${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["間男 上レイヤー"]}, {"code": 111, "indent": 0, "parameters": [1, 1616, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy21_upper`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["Xray"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1619)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 1}, {"id": 4, "name": "Aキー", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["イチャラブ？ - 退屈セックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 117, "indent": 0, "parameters": [98]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"event-pri-boring\""]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["レイヤー設定"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 108, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["表示処理"]}, {"code": 408, "indent": 0, "parameters": ["***************"]}, {"code": 408, "indent": 0, "parameters": ["MapEvent.call(138, 4, 1)"]}, {"code": 108, "indent": 0, "parameters": ["ＢＧ"]}, {"code": 108, "indent": 0, "parameters": ["ベース"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情"]}, {"code": 355, "indent": 0, "parameters": ["num2 = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ハット"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth92`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-guy1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["Xray"]}, {"code": 111, "indent": 0, "parameters": [1, 1619, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1619)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-xray${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 1}]}