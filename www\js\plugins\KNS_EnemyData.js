/*:
 * @plugindesc csv/EnemyData.csvをプラグインコマンドから読み取ります。
 * <AUTHOR>
 * 
 * @help
 * ※KNS_CsvReaderの下に設置してください。
 * csv/EnemyData.csvをプラグインコマンドから読み取り、
 * 変数への代入・スクリプトの実行を行います。
 * 
 * ■プラグインコマンド
 * ・引数「敵ID」「代入変数」を\v[変数ID]の書式で記述することで
 * 　変数の値を指定することが可能です。
 * ・引数「列」の指定はExcelの列表記と同様にアルファベットを用いて
 * 　AやZZのように指定します。
 * 　連番で指定する場合はB-Qのようにハイフン区切りで指定してください。
 * 
 * 
 * KNS_EnemyData search 敵ID 代入先スイッチ "検索文字列" 列|"all"(省略時はall) 検索条件("contain"|"complete"、省略時はcontain)
 * 　指定された敵IDに指定の文字列が含まれているか判定し、結果を代入先スイッチに代入します。
 * 　検索文字列は"で囲み、文字列内に"が含まれている場合は\"のようにスラッシュをつけて表記してください。
 * 　改行を含む場合は\nと表記してください。
 * 
 * 　列名がallのときはすべての列が検索対象になり、一つ以上の列に含まれていればtrueを返します。
 * 
 * 　検索条件がcontainのときは部分一致で、検索文字列が対象列に含まれていればtrueとし、
 * 　completeのときはすべて一致していればtrueとします。
 * 
 * 例）
 * KNS_EnemyData search 103 109 "$attack_animation_type = \"spear\"" X contains
 * 　敵ID103のX列に""内の文字列が含まれているか調べ、
 * 　結果をスイッチ109番に代入する
 * 
 * KNS_EnemyData search \v[40] 109 "$attack_animation_type = \"blow\";\n$trait_penis = 1" all complete
 * 　変数ID40で指定した敵IDの全ての列のうちいずれかが""の文字列と一致しているか調べ、
 * 　結果をスイッチ109番に代入する
 * 
 * 
 * KNS_EnemyData set 敵ID 列 代入変数
 * 　敵IDと一致する行の指定の列から値を取得し変数に代入します。
 * 　存在しない場合は0が代入されエラーとしてコンソールに表示されます。
 * 　B列のみ名前格納用のキーとし、logsheet.csvと一致するテキストが
 * 　代入されます。
 * 　NdN形式で表記されているセルはダイスロールで展開しランダムな結果を返します。
 * 
 * 例）KNS_EnemyData set \v[40] B 10
 * 　　　変数40番と一致する敵IDの行からB列を取得し変数10番に代入
 * 　　KNS_EnemyData set \v[40] B-D 12
 * 　　　変数40番と一致する敵IDの行からB-D列を取得し変数12～14番に代入
 * 
 * 
 * KNS_EnemyData setString 敵ID 列 代入変数
 * 　セルの値を文字列で代入します。
 * 
 * 
 * KNS_EnemyData eval 敵ID 列
 * 　敵IDと一致する行の指定の列に記述されたスクリプトを実行します。
 * 　s[n]でスイッチ、v[n]で変数を指定することができます。
 * 
 * 例）KNS_EnemyData eval \v[40] W-X
 * 　　　変数40番と一致する敵IDの行のW-X列のスクリプトを実行する。
 * 
 * if (条件文) 実行内容;
 * 　指定された条件文を満たしているとき、実行内容を実行します。
 * 　条件文が不要な場合は実行内容のみを指定してください
 * 
 * v[N]
 * 　操作する変数N番を指定します。
 * 
 * s[N]
 * 　操作するスイッチN番を指定します。
 * 　スイッチの場合は比較値をtrue, falseで指定します
 * 
 * <, >, <=, >=, ==, !=
 * 　条件文に使用できる比較演算子です。
 * 　順に「より小さい」「より大きい」「以下」「以上」「一致」「以外」を表します。
 * 
 * =, +=, -=, *=, /=
 * 　実行内容に使用できる代入演算子です。
 * 　順に「代入」「加算」「減算」「乗算」「除算」を表します。
 * 
 * 
 * // 変数４が２以上のとき、変数５に０を代入
 * if (v[4] >= 2) v[5] = 0;
 * 
 * // 変数４が２以上か０のとき、変数５に１を代入
 * if (v[4] >= 2 || v[4] == 0) v[5] = 1;
 * 
 * // 変数４が２以上かつ４以下のとき、変数５に２を代入
 * if (v[4] >= 2 && v[4] <= 4) v[5] = 2;
 * 
 * 
 * 
 * KNS_EnemyData varCondition 敵ID 列 比較値 代入変数
 * 　「X1:Y1,X2:Y2...」の形式の記述のある列を取得し、
 * 　比較値がX1以上であればY1を、X1未満X2以上であればY2を代入します。
 * 　すべての値にマッチしない場合は末尾のYが代入されます
 * 
 * 例）KNS_EnemyData varcondition \v[40] Y \v[10] 100
 * 　　　変数40番と一致する敵IDの行のY列の値を変数10番と比較し
 * 　　　結果を変数100番に代入する
 * 
 * 
 * KNS_EnemyData dropItem 敵ID 列 (N1-N2 比較乱数) (true|false ログ表示)
 * 　列に指定されたドロップアイテムの確率を比較変数と比較し、
 * 比較変数が確率未満の値であれば入手します。
 * 
 * 　列の記載方法は以下の通りです（改行で複数指定可能です）
 * I1:1:25
 * 　アイテム１番を１個、２５％の確率で入手
 * 
 * W10:2:50
 * 　武器１０番を１個、５０％の確率で入手
 * 
 * A20:3:75
 * 　防具２０番を１個、７５％の確率で入手
 * 
 * G:300:100
 * 　３００Gを１００％の確率で入手
 * 
 * 　プラグインコマンドは以下の通りに記載します。
 * KNS_EnemyData dropItem \v[40] Z 0-100 true
 */
const KNS_EnemyData = {
    name: 'KNS_EnemyData',
    parseSheetRowIndex: function(row){
        row = row.toUpperCase();
        let num = 0;
        for (let i = 0; i < row.length; i++){
            let code = row.charCodeAt(i) - 64;
            num = num * 26 + code;
        }
        return num - 1;
    },
    reVariable: /\\v\[(\d+)\]/,
    parseVariable(arg){
        if (this.reVariable.test(arg)){
            return $gameVariables.value(Number(RegExp.$1));
        }else{
            return Number(arg);
        }
    },
    iterateBasicParameter(enemyId, rows, callBack){
        enemyId = KNS_EnemyData.parseVariable(enemyId);
        rows = rows.split('-');
        let rowA = KNS_EnemyData.parseSheetRowIndex(rows[0]);
        let rowB = rows[1] ? KNS_EnemyData.parseSheetRowIndex(rows[1]) : rowA;
        if (rowA > rowB){
            let temp = rowA;
            rowA = rowB;
            rowB = temp;
        }
        const enemyData = $csv_EnemyData[enemyId];
        if (!enemyData){
            console.error("KNS_EnemyData 無効な敵ID: " + enemyId);
        }
        for (let row = rowA; row <= rowB; row++){ callBack(row, enemyId, enemyData); }
    },
    reDropItem: /([IWA](\d+)|G):(\d+):(\d+)/gmi,
    parseDropItem(dropItem){
        let result = [];
        let match = null;
        while (match = this.reDropItem.exec(dropItem)) {
            let type = match[1][0].toUpperCase();
            let num  = Math.floor(match[3]) || 0;
            let rate = Math.floor(match[4]) || 0;
            if (type === 'G'){
                result.push([type, num, rate]);
            }else{
                let list;
                switch (type){
                    case 'I': list = $dataItems; break;
                    case 'W': list = $dataWeapons; break;
                    case 'A': list = $dataArmors; break;
                    default: break;
                }
                result.push([list[Math.floor(match[2])], num, rate]);
            }
        }
        this.reDropItem.lastIndex = 0;
        return result;
    },
    formatText(key, params){
        if (!$LogSheetCSV.Exist(key)){ return ""; }
        return $LogSheetCSV.Get(key).format(...params);
    }
};
(function(){
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_EnemyData.name){ return; }
        const comName = args[0].toLowerCase();
        switch (comName){
            case 'set':
            case 'setstring':{
                let varId = KNS_EnemyData.parseVariable(args[3]);
                KNS_EnemyData.iterateBasicParameter(args[1], args[2], function(
                    row, enemyId, enemyData
                ){
                    let value;
                    if (!enemyData){
                        value = 0;
                    }else if (comName === 'setstring'){
                        value = String(enemyData[row] || "");
                    }else if (row === 1){
                        if ($LogSheetCSV.Exist(enemyData[row])){
                            value = $LogSheetCSV.Get(enemyData[row]) || "";
                        }else{
                            console.error("KNS_EnemyData ログに名前の登録がないため0とします " + enemyId);
                            value = "0";
                        }
                    }else{
                        const text = enemyData[row] || "";
                        const values = [];
                        value = text.split('+').reduce(function(r, formula){
                            if (/^(\d+)d(\d+)$/i.test(formula)) {
                                const base = Number(RegExp.$1);
                                const dice = Number(RegExp.$2);
                                for (let i = 0; i < base; i++) {
                                    const random = Math.randomInt(dice) + 1;
                                    r += random;
                                    values.push(random);
                                }
                            } else {
                                r += Number(formula);
                                values.push(Number(formula));
                            }
                            return r;
                        }, 0);

                        console.log(
                            `%c[${enemyId}]${enemyData[1]}(row: ${row}):`,
                            "color:white; background-color:purple; padding:2px; border-radius:4px;",
                            `\n[${text}]`,
                            `\n${values.join('+')} => Total: ${value}`
                        );
                    }
                    $gameVariables.setValue(varId++, value);
                });
                break;
            }
            case 'eval':{
                KNS_EnemyData.iterateBasicParameter(args[1], args[2], function(
                    row, enemyId, enemyData
                ){
                    if (!enemyData){ return; }
                    var s = $gameSwitches._data;
                    var v = $gameVariables._data;
                    eval((enemyData[row] || ""));
                });
                break;
            }
            case 'varcondition':{
                console.log(args);
                let inputValue = KNS_EnemyData.parseVariable(args[3]);
                let outputVar = KNS_EnemyData.parseVariable(args[4]);
                KNS_EnemyData.iterateBasicParameter(args[1], args[2], function(
                    row, enemyId, enemyData
                ){
                    if (!enemyData || typeof(enemyData[row]) !== 'string'){
                        console.error("KNS_EnemyData 指定された列にデータがありません！")
                        return;
                    }
                    const compares = enemyData[row].split(/\s*,\s*/g).map(function(comp){
                        const comps = comp.split(/\s*:\s*/g);
                        comps[0] = Number(comps[0] || 0);
                        comps[1] = Number(comps[1] || 0);
                        return comps;
                    });
                    let value = compares[compares.length - 1][1];
                    for (let i = 0; i < compares.length; i++){
                        if (inputValue >= compares[i][0]){
                            value = compares[i][1];
                            break;
                        }
                    }
                    $gameVariables.setValue(outputVar, value);
                });
                break;
            }
            case 'dropitem':{
                KNS_EnemyData.iterateBasicParameter(args[1], args[2], function(
                    row, enemyId, enemyData
                ){
                    if (!enemyData){ return; }
                    const random = (args[3] || "0").split('-');
                    let min = KNS_EnemyData.parseVariable(random[0]);
                    let max = KNS_EnemyData.parseVariable(random[1]) || min;
                    KNS_EnemyData.parseDropItem(enemyData[row]).forEach(function(info){
                        let randInt = Math.randomInt(max - min) + min;
                        if (info[2] >= randInt){
                            let text;
                            if (info[0] === 'G'){
                                $gameParty.gainGold(info[1]);
                                text = KNS_EnemyData.formatText("Gold Add manage", [info[1]]);
                            }else{
                                $gameParty.gainItem(info[0], info[1]);
                                text = KNS_EnemyData.formatText(
                                    "Item Add manage",
                                    [`\\i[${info[0].iconIndex}]${info[0].name}`, info[1]]
                                );
                            }
                            if (text.length > 0 && args[4] === 'true'){
                                $LogWindow.Add(text);
                            }
                        }
                    });
                });
                break;
            }
            case 'search':{
                let [_, id, switchId] = args;
                id = KNS_EnemyData.parseVariable(id);
                switchId = KNS_EnemyData.parseVariable(switchId);

                const str = args.slice(3).join(' ');
                let search, i = 1;
                for (; i < str.length; i++) {
                    if (str[i] === '"' && str[i - 1] !== "\\") break;
                }
                try {
                    search = JsonEx.parse(str.slice(0, i + 1));
                } catch (e) {
                    search = str;
                    console.error(`KNS_EnemyData 検索文字列の指定に誤りがあります！\n"や改行等の表記を確認してください。\n（${args.join(" ")}）`)
                }

                let [rows = "all", cond = "contains"] = str.slice(i + 2).split(' ');
                if (rows === "all") rows = "A-FF"

                let isFound = false;
                KNS_EnemyData.iterateBasicParameter(id, rows, function(
                    row, enemyId, enemyData
                ){
                    if (!enemyData || typeof(enemyData[row]) !== 'string'){
                        console.error("KNS_EnemyData 指定された列にデータがありません！")
                        return;
                    }
                    if (isFound) return;
                    const sRow = String(enemyData[row]);
                    isFound = cond === "contains" ? sRow.contains(search) : sRow === search;
                });
                $gameSwitches.setValue(switchId, isFound);
                break;
            }
        }
    };

}).call(KNS_EnemyData);