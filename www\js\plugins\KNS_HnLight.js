/*:
 * @plugindesc ver.1.0.0 【RGSS3】HN_Lightを移植します
 * <AUTHOR>　移植元：半生 氏
 *
 * @param LIGHTS
 * @text ライト設定
 * @type struct<LightType>[]
 * @default ["{\"fileName\":\"light5\",\"cells\":\"1\",\"zoom\":\"1.500\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light2\",\"cells\":\"1\",\"zoom\":\"1.000\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light3\",\"cells\":\"1\",\"zoom\":\"0.800\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light4\",\"cells\":\"1\",\"zoom\":\"1.000\",\"offset_y\":\"-16\",\"hue\":\"0\"}","{\"fileName\":\"light5\",\"cells\":\"1\",\"zoom\":\"2.000\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light6\",\"cells\":\"1\",\"zoom\":\"1.000\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light7\",\"cells\":\"1\",\"zoom\":\"3.000\",\"offset_y\":\"-16\",\"hue\":\"0\"}","{\"fileName\":\"light1\",\"cells\":\"1\",\"zoom\":\"0.500\",\"offset_y\":\"0\",\"hue\":\"0\"}","{\"fileName\":\"light6\",\"cells\":\"1\",\"zoom\":\"2.000\",\"offset_y\":\"0\",\"hue\":\"0\"}"]
 *
 * @param UPDATE_FPS
 * @text 何フレーム間隔で更新する？
 * @type number
 * @min 1
 * @default 2
 * @desc 数値が低いほどスムーズに表示されるが重くなります。
 *
 * @param DARK_COLOR
 * @text 暗闇カラーコード
 * @type string
 * @default #000000
 *
 * @param PLAYER_LIGHT_TYPE
 * @text 【変数】プレイヤーの灯りタイプ
 * @type variable
 * @default 12
 *
 * @param FOLLOWER_LIGHT_TYPE
 * @text 【変数】隊列メンバーの灯りタイプ
 * @type variable
 * @default 12
 *
 * @param DARK_SWITCH
 * @text 【スイッチ】暗闇判定
 * @type switch
 * @default 11
 *
 * @help
 * ※このプラグインは半生氏のRGSS3スクリプト『HN_Light』の移植版です
 *
 * 【仕様】
 * ・イベントリスト先頭に注釈があり『＠灯りN』の記述が
 * 　あるものに対しN番目の灯りを表示します（Nは半角数字のみ）。
 * ・ライト設定のアニメ数に2以上の設定を入れると、アニメ数の数だけ
 * 　ライト画像が横に分割され4フレームごとに画像が切り替わります。
 * ・RGSS版と異なり明かりエフェクトは乗算で表示されます。
 *
 * ■更新履歴
 * ver.1.0.0(2023-06-17)
 * - デモ
 */
/*~struct~LightType:
 * @param fileName
 * @text ファイル名
 * @type file
 * @dir img/pictures
 * @default light1
 *
 * @param cells
 * @text アニメ数
 * @type number
 * @default 1
 *
 * @param zoom
 * @text 拡大率
 * @type number
 * @decimals 3
 * @default 1
 *
 * @param offset_y
 * @text Y座標調整
 * @type number
 * @default 0
 *
 * @param hue
 * @text 色調（0～180）
 * @type number
 * @default 0
 */

const KNS_HnLight = {
	name: "KNS_HnLight",
	param: null,
	reLightAnnotation: /[@＠]灯り(\d+)/
};
(function(){
	this.param = PluginManager.parameters(this.name);
	["UPDATE_FPS", "PLAYER_LIGHT_TYPE", "FOLLOWER_LIGHT_TYPE", "DARK_SWITCH"].forEach(function(key){
		this.param[key] = Math.floor(this.param[key]);
	}, this);
	this.param.LIGHTS = JsonEx.parse(this.param.LIGHTS).map(function(json){
		const obj = JsonEx.parse(json);
		["cells", "zoom", "offset_y", "hue"].forEach(function(key){
			obj[key] = Number(obj[key]);
		}, this);
		obj.bitmap = ImageManager.reservePicture(obj.fileName, obj.hue);
		return obj;
	});

	//==========================================================
	// alias Game_Character
	//==========================================================
	Game_Character.prototype.knsGetLightId = function(){
		return 0;
	}

	Game_Character.prototype.knsGetLightData = function(){
		const id = this.knsGetLightId();
		return id === 0 ? null : KNS_HnLight.param.LIGHTS[id - 1];
	}

	//==========================================================
	// alias Game_Player
	//==========================================================
	Game_Player.prototype.knsGetLightId = function(){
		return this.isTransparent() ? 0 : $gameVariables.value(KNS_HnLight.param.PLAYER_LIGHT_TYPE);
	}

	//==========================================================
	// alias Game_Follower
	//==========================================================
	Game_Follower.prototype.knsGetLightId = function(){
		return this.isVisible() ? $gameVariables.value(KNS_HnLight.param.FOLLOWER_LIGHT_TYPE) : 0;
	}

	//==========================================================
	// alias Game_Event
	//==========================================================
	Game_Event.prototype.knsGetLightId = function(){
		return this.isTransparent() ? 0 : (this._knsLightId || 0);
	}

	const _Game_Event_clearPageSettings = Game_Event.prototype.clearPageSettings;
	Game_Event.prototype.clearPageSettings = function() {
		_Game_Event_clearPageSettings.apply(this, arguments);
		this._knsLightId = 0;
	};

	const _Game_Event_setupPageSettings = Game_Event.prototype.setupPageSettings;
	Game_Event.prototype.setupPageSettings = function() {
		_Game_Event_setupPageSettings.apply(this, arguments);
		this._knsLightId = this.knsReadLightIdFromAnnotation();
	};

	Game_Event.prototype.knsReadLightIdFromAnnotation = function(){
		const page = this.page();
		if (page && page.list[0].code === 108){
			let i = 0;
			do {
				if (KNS_HnLight.reLightAnnotation.test(page.list[i].parameters[0])){
					return Math.floor(RegExp.$1);
				}
			}while (page.list[++i].code === 408);
		}
		return 0;
	}

	//==========================================================
	// alias Spriteset_Map
	//==========================================================
	const _Spriteset_Map_createTilemap = Spriteset_Map.prototype.createTilemap;
	Spriteset_Map.prototype.createTilemap = function() {
		_Spriteset_Map_createTilemap.apply(this, arguments);
		this.knsCreateSpritesetLight();
	};

	Spriteset_Map.prototype.knsCreateSpritesetLight = function(){
		this._knsSpritesetLight = new Spriteset_KnsLight();
		this.addChild(this._knsSpritesetLight);
	}
}).call(KNS_HnLight);

class Spriteset_KnsLight extends Sprite{
	constructor(){
		super(new Bitmap(Graphics.width, Graphics.height));
		this.blendMode = Graphics.BLEND_MULTIPLY;
		this._knsFrame = 0;
		this.updateBitmap();
	}
	update(){
		super.update();
		const result = $gameSwitches.value(KNS_HnLight.param.DARK_SWITCH);
		if (result){
			if (this.visible !== result || this.canUpdateBitmap()){
				this.updateBitmap();
			}
		}
		this.visible = result;
	}
	canUpdateBitmap(){
		return KNS_HnLight.param.HIGH_QUALITY || Graphics.frameCount % KNS_HnLight.param.UPDATE_FPS === 0;
	}
	updateBitmap(){
		const ctx = this.bitmap._context;
		// clear canvas
		ctx.fillStyle = KNS_HnLight.param.DARK_COLOR;
		ctx.globalCompositeOperation = "source-over";
		ctx.fillRect(0, 0, this.bitmap.width, this.bitmap.height);
		// character lights
		ctx.globalCompositeOperation = "screen";
		this._knsFrame = Graphics.frameCount >> 2;
		this.drawLight($gamePlayer);
		$gamePlayer.followers().forEach(this.drawLight, this);
		$gameMap.events().forEach(this.drawLight, this);
		this.bitmap._setDirty();
	}
	drawLight(ev){
		const light = ev.knsGetLightData();
		if (light){
			const bmp = light.bitmap;
			// Y軸を計算
			const dh = Math.floor(bmp.height * light.zoom);
			const dy = Math.floor(ev.screenY() - dh * 0.5) + light.offset_y;
			if (dy > this.bitmap.height || dy + dh < 0){ return; }
			// X軸を計算
			const sw = Math.floor(bmp.width / light.cells);
			const dw = Math.floor(sw * light.zoom);
			const dx = Math.floor(ev.screenX() - dw * 0.5);
			if (dx > this.bitmap.width || dx + dw < 0){ return; }
			this.bitmap._context.drawImage(bmp._canvas,
				(this._knsFrame % light.cells) * sw, 0, sw, bmp.height, dx, dy, dw, dh
			);
		}
	}
};