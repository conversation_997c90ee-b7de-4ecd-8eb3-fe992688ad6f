/*:
* @plugindesc Controls the character set available during name input based on the value of variable 396
* <AUTHOR> Name
*
* @help This plugin allows you to control the character set available during name input based on the value of a variable.
*/

(function() {

    var _Window_NameInput_prototype_table = Window_NameInput.prototype.table;
    Window_NameInput.prototype.table = function() {
        var variableValue = $gameVariables.value(396);
        if (variableValue === 0) {
            // 全角ひらがなとカタカナ
            return [
                [
                    'あ', 'い', 'う', 'え', 'お',
                    'か', 'き', 'く', 'け', 'こ',
                    // ... (他の文字を追加)
                ],
                [
                    'ア', 'イ', 'ウ', 'エ', 'オ',
                    'カ', 'キ', 'ク', 'ケ', 'コ',
                    // ... (他の文字を追加)
                ]
            ];
        } else if (variableValue === 1) {
            // 半角英数
            return [
                [
                    'A', 'B', 'C', 'D', 'E',
                    'F', 'G', 'H', 'I', 'J',
                    // ... (他の文字を追加)
                ],
                [
                    'K', 'L', 'M', 'N', 'O',
                    'P', 'Q', 'R', 'S', 'T',
                    // ... (他の文字を追加)
                ]
            ];
        } else {
            // デフォルトのテーブルを使用
            return _Window_NameInput_prototype_table.call(this);
        }
    };

})();
