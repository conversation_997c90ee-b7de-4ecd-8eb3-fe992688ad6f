{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "アイリス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス - 背面"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$iris_pose = 1"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-iris-back\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 monster"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 200]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-base0\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["武器の表示"]}, {"code": 111, "indent": 0, "parameters": [0, 86, 0]}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer5].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$no =\"#{$e_name}-cane\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-arm0\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 29, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = \"#{$e_name}-cloth\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-hair0\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =\"#{$e_name}-face#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギア"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-ribon\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス - ガニ股"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$iris_pose = 2"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-iris-ganimata\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 monster"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 1600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -120]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-hair_back0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-base0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-hair0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["リボンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1642, 0, 1, 4]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-headgear0`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(1642)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["耳の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-ear0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["オプション表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer17);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス - 戦闘"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$iris_pose = 3"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-iris-battle\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["2 hair_back#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["3 base{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 inmon"]}, {"code": 408, "indent": 0, "parameters": ["5 ph"]}, {"code": 408, "indent": 0, "parameters": ["6 ah"]}, {"code": 408, "indent": 0, "parameters": ["10 cloth"]}, {"code": 108, "indent": 0, "parameters": ["11 weapon"]}, {"code": 408, "indent": 0, "parameters": ["13 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["15 head_acc"]}, {"code": 408, "indent": 0, "parameters": ["18 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["20 effect"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -120]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-hair_back${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["淫紋の表示"]}, {"code": 111, "indent": 0, "parameters": [0, 93, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-inmon`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-cloth`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["武器"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-weapon`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair0`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["リボンの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1642, 0, 1, 4]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-head_acc`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["魔法エフェクト"]}, {"code": 111, "indent": 0, "parameters": [1, 374, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-effect`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["screen.pictures[$layer20].erase"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス - 拘束"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$iris_pose = 4"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-iris-bind\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["2 hair_back#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 inmon"]}, {"code": 408, "indent": 0, "parameters": ["5 ph"]}, {"code": 408, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["10 arms#{skin}-#{arm pose}"]}, {"code": 108, "indent": 0, "parameters": ["12 cape"]}, {"code": 408, "indent": 0, "parameters": ["13 hair"]}, {"code": 408, "indent": 0, "parameters": ["15 hair_acc"]}, {"code": 408, "indent": 0, "parameters": ["18 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["20 enemy_#{enemy_id}-#{enemy amount}"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 200]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -120]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =\"#{$e_name}-hair_back#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =\"#{$e_name}-base#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["淫紋の表示"]}, {"code": 111, "indent": 0, "parameters": [0, 93, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =\"#{$e_name}-inmon\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-ph\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-cloth0\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(25)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =\"#{$e_name}-arms#{num}-#{num2}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-cape\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =\"#{$e_name}-hair0\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["リボンの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-hair_acc\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =\"#{$e_name}-face#{num}\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["敵"]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(121)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(378)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =\"#{$e_name}-enemy#{num}-#{num2}\""]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["グラフィックの反転処理"]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["i = 0"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["while i < 21"]}, {"code": 655, "indent": 1, "parameters": ["layer = eval(\"$layer#{i} = i\")"]}, {"code": 655, "indent": 1, "parameters": ["screen.pictures[layer].mirror = false"]}, {"code": 655, "indent": 1, "parameters": ["i+=1"]}, {"code": 655, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["i =21"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["while i < 41"]}, {"code": 655, "indent": 1, "parameters": ["layer = eval(\"$layer#{i} = i\")"]}, {"code": 655, "indent": 1, "parameters": ["screen.pictures[layer].mirror = true"]}, {"code": 655, "indent": 1, "parameters": ["i+=1"]}, {"code": 655, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$irs", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アイリス - I字バランス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$iris_pose = 5"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-iris-Ibalance\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["2 hair_back#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["3 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["4 inmon"]}, {"code": 408, "indent": 0, "parameters": ["5 ph"]}, {"code": 408, "indent": 0, "parameters": ["8 cloth"]}, {"code": 408, "indent": 0, "parameters": ["10 arms#{skin}-#{arm pose}"]}, {"code": 108, "indent": 0, "parameters": ["12 cape"]}, {"code": 408, "indent": 0, "parameters": ["13 hair"]}, {"code": 408, "indent": 0, "parameters": ["15 hair_acc"]}, {"code": 408, "indent": 0, "parameters": ["18 face#{face}"]}, {"code": 408, "indent": 0, "parameters": ["20 enemy_#{enemy_id}-#{enemy amount}"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ベースの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-ah`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-arm${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["OP3の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(1604)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-op3-${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer11]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["顔の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽとか男とか"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["1の場合はcock"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cock${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 1}, {"id": 8, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 9, "name": "-", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 10, "name": "ID10:PM", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["<PERSON> <PERSON> <PERSON><PERSON><PERSON>"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-down\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["10 monster"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["モンスターの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-guy${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["オプションの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - 拘束"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-bind\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"PMバインド立ち絵開始\")"]}, {"code": 655, "indent": 0, "parameters": ["console.log(`プレイ相手種別番号:#{$gameVariables.value(39)}`)"]}, {"code": 655, "indent": 0, "parameters": ["console.log(`敵ＩＤ:${$gameVariables.value(121)}`)"]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth"]}, {"code": 408, "indent": 0, "parameters": ["8 face"]}, {"code": 408, "indent": 0, "parameters": ["10 guy"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["モンスターの表示(背面レイヤー）"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}_back`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["if ($gameActors.actor(10).equips()[1] != null) {"]}, {"code": 655, "indent": 1, "parameters": ["id = $gameActors.actor(10).equips()[1].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer7]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer7);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["モンスターの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}_front`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ214 10 3開始\")"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - 種付けプレス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$ene_opa = 80"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-matting_press\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth"]}, {"code": 408, "indent": 0, "parameters": ["8 face"]}, {"code": 408, "indent": 0, "parameters": ["10 guy"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -150]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-base`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["モンスターの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(39)"]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,$ene_opa,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ214 10 3終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - 靴を脱ぐ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"214 10 4表示開始\")"]}, {"code": 355, "indent": 0, "parameters": ["$ene_opa = 80"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-temptation\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["5 base"]}, {"code": 408, "indent": 0, "parameters": ["6 ph"]}, {"code": 408, "indent": 0, "parameters": ["7 cloth"]}, {"code": 408, "indent": 0, "parameters": ["8 face"]}, {"code": 408, "indent": 0, "parameters": ["10 guy"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(10).equips()[1] && $gameActors.actor(10).equips()[1].note.includes(\"ビキニ\")"]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-baseX`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["if ($gameActors.actor(10).equips()[1] != null) {"]}, {"code": 655, "indent": 1, "parameters": ["  $no =`${$e_name}-base`"]}, {"code": 655, "indent": 1, "parameters": ["}else{"]}, {"code": 655, "indent": 1, "parameters": ["  $no =`${$e_name}-baseX`"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 1646, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["服の表示"]}, {"code": 111, "indent": 1, "parameters": [4, 10, 5, 141]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-cloth`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["if ($gameActors.actor(10).equips()[1] != null) {"]}, {"code": 655, "indent": 2, "parameters": ["id = $gameActors.actor(10).equips()[1].id"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-hair`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ヘッドギア"]}, {"code": 111, "indent": 0, "parameters": [1, 1646, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [4, 10, 5, 141]}, {"code": 355, "indent": 2, "parameters": ["$no = `${$e_name}-clothHead141`"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["if ($gameActors.actor(10).equips()[1] != null) {"]}, {"code": 655, "indent": 2, "parameters": ["id = $gameActors.actor(10).equips()[1].id"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$no = `${$e_name}-clothHead${id}`"]}, {"code": 655, "indent": 2, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 2, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"214 10 4表示終了\")"]}, {"code": 108, "indent": 0, "parameters": ["ケツ揉む手とか"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `stand-unique-groping_hand`"]}, {"code": 655, "indent": 1, "parameters": ["x = $gameVariables.value(21) +350"]}, {"code": 655, "indent": 1, "parameters": ["y = $gameVariables.value(22) -80"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,x,y,$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - <PERSON><PERSON>股"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ214 10 5 PMガニ股開始\")"]}, {"code": 355, "indent": 0, "parameters": ["$ene_opa = 80"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-sukebe_dance\""]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 ph"]}, {"code": 408, "indent": 0, "parameters": ["4 ah"]}, {"code": 408, "indent": 0, "parameters": ["5 head#{skin}"]}, {"code": 108, "indent": 0, "parameters": ["6 cloth0"]}, {"code": 408, "indent": 0, "parameters": ["9 clothHead"]}, {"code": 408, "indent": 0, "parameters": ["10 hair#{hair}"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["12 face#{face}"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -220]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": [" $no = `${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-head${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 111, "indent": 0, "parameters": [1, 1646, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["服の表示"]}, {"code": 355, "indent": 1, "parameters": ["if ($gameActors.actor(10).equips()[1] != null) {"]}, {"code": 655, "indent": 1, "parameters": ["id = $gameActors.actor(10).equips()[1].id"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-cloth${id}`"]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no = `${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["op"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer15);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ミルク"]}, {"code": 111, "indent": 0, "parameters": [12, "$milk == 1"]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-milk`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.erasePicture($layer18);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ214 10 5 PMガニ股終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["PM - 立ちバック"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-pm-st_back\""]}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [39, 39, 0, 1, 121]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 108, "indent": 0, "parameters": ["2 base#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["3 hair_lower#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["4 arm#{skin}"]}, {"code": 408, "indent": 0, "parameters": ["5 hair_upper#{hair}"]}, {"code": 408, "indent": 0, "parameters": ["8 face#{face}"]}, {"code": 108, "indent": 0, "parameters": ["15 guy#{456}"]}, {"code": 408, "indent": 0, "parameters": ["16 cock#{stage}"]}, {"code": 408, "indent": 0, "parameters": ["17 cum#{cum}-#{stage}"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["20 bg_upper#{bg}"]}, {"code": 355, "indent": 0, "parameters": ["$skin = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": ["$hair = $gameVariables.value(34)"]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -25]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 800]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-base${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-hair${$hair}_lower`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["腕の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-arm${$skin}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-hair${$hair}_upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["表情の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["おとこ"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-guy1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ちんぽ"]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cock${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer16]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子の表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num1}-${num2}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer17]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["精子EXの表示"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 2, 1]}, {"code": 355, "indent": 1, "parameters": ["num1 = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": ["num2 = $gameVariables.value(28)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum_ex`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["背景上部レイヤー"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(18)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-bg${num}_upper`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 3}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 3}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 3}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 3}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 3}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 3}, {"id": 17, "name": "ID17:湯女", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "yume", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["湯女 - アナルナメ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-yusame-rj\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -300]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 400]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 800]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["anus"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-anus`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer4]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-guy1`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 5}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 5}, {"id": 19, "name": "ID19:吸血姫", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヴァンパイア - 拘束"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-VP-bind\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 700]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["敵ＩＤを４５６に代入する処理"]}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 1, 121]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["敵"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-enemy_lower${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer5]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腋毛"]}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ah`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(19).equips()[1] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(19).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-outfit${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["敵　上レイヤー"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-enemy_upper${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer18]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["OP"]}, {"code": 111, "indent": 0, "parameters": [1, 1602, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-op${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer19]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["液体"]}, {"code": 111, "indent": 0, "parameters": [1, 1603, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(1603)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-liquid${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヴァンパイア - 後ろ姿"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-VP-back\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -50]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, -250]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛　バック"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-hair_back${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["髪の毛"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(34)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-hair${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(19).equips()[1] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(19).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["(従者の戦闘エロの画像はページ３で固定化させる）"]}, {"code": 408, "indent": 0, "parameters": ["ヴァンパイア - 種付けプレス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-VP-matting\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 700]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["敵ＩＤを４５６に代入する処理"]}, {"code": 111, "indent": 0, "parameters": [0, 14, 0]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 1, 121]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(19).equips()[1] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(19).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-outfit_lower${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["射精"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer9]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["イき潮"]}, {"code": 408, "indent": 0, "parameters": ["（1616に変更するかも）"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-squirt`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["敵"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(456)"]}, {"code": 655, "indent": 0, "parameters": ["alpha = $gameVariables.value(1620)"]}, {"code": 655, "indent": 0, "parameters": ["layer = $layer11"]}, {"code": 655, "indent": 0, "parameters": ["if($gameVariables.value(456) == 301 || $gameVariables.value(456) == 211){"]}, {"code": 655, "indent": 0, "parameters": ["layer = $layer18;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-enemy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[layer]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,alpha,0)"]}, {"code": 108, "indent": 0, "parameters": ["脚の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-legs${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(19).equips()[1] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(19).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-outfit_upper${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヴァンパイア - エロ漫画で見たアレ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-VP-are\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 700]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(33)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-base${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["服の表示"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameActors.actor(19).equips()[1] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["num = $gameActors.actor(19).equips()[1].id"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-cloth${num}`"]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer8]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["フェイスの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(23)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-face${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気の表示"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 5}, {"id": 20, "name": "モブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["モブ - B<PERSON>"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["$e_name = \"stand-mob-bj\""]}, {"code": 117, "indent": 0, "parameters": [51]}, {"code": 122, "indent": 0, "parameters": [22, 22, 0, 0, -100]}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 600]}, {"code": 117, "indent": 1, "parameters": [640]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 30, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["$s_h = -50"]}, {"code": 122, "indent": 1, "parameters": [21, 21, 0, 0, 700]}, {"code": 117, "indent": 1, "parameters": [641]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["男"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(1602)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-guy${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer2]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["精子"]}, {"code": 111, "indent": 0, "parameters": [1, 40, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(40)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$no =`${$e_name}-cum${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer6]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヘッドの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(479)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-head${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer10]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["ボディの表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(479)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-body${num}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer12]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["陰毛"]}, {"code": 111, "indent": 0, "parameters": [1, 35, 0, 1, 1]}, {"code": 355, "indent": 1, "parameters": ["$no =`${$e_name}-ph`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer13]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["髪の毛の表示"]}, {"code": 355, "indent": 0, "parameters": ["num = $gameVariables.value(479)"]}, {"code": 655, "indent": 0, "parameters": ["num2 = $gameVariables.value(478)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$no =`${$e_name}-hair${num}-${num2}`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer15]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 108, "indent": 0, "parameters": ["湯気"]}, {"code": 355, "indent": 0, "parameters": ["$no =`${$e_name}-steam`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 11}]}