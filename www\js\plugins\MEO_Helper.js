let ArrayHelper={CreateArray:function(){let e=[],t=(e,r)=>{for(let n=0;n<arguments[r];n++)r+1<arguments.length?(e.push([]),t(e[n],r+1)):e.push(0)};return t(e,0),e}},FunctionHelper={Empty:function(){}},BitmapHelper={DrawNumber:function(e,t){let r=(t.number||0).toString();t.pad&&(r=r.padStart(t.pad,t.padValue||""));let n=t.x||0,l=t.y||0,a=e.measureTextWidth(r);return e.drawText(r,n,l,a,t.lineHeight||0,t.align||"left"),new Rectangle(n,l,a,0)},DrawFraction:function(e,t){let r,n=t.value||0,l=t.max||0,a=t.x||0,i=t.y||0,o=t.padValue||" ",u=t.align||"left",f=t.seperator||"/",p=l.toString(),d=new Rectangle(a,i,0,0);return r=BitmapHelper.DrawNumber(e,{number:n,x:d.right,y:d.y,pad:p.length,padValue:o,align:u,lineHeight:36}),d.AddSize(r),r=BitmapHelper.DrawText(e,{x:d.right,y:d.y,text:f}),d.AddSize(r),r=BitmapHelper.DrawNumber(e,{number:l,x:d.right,y:d.y,pad:p.length,padValue:o,align:u,lineHeight:36}),d.AddSize(r),d},DrawText:function(e,t){let r=t.x||0,n=t.y||0,l=e.measureTextWidth(t.text),a=t.lineHeight||36,i=t.align||"left";return e.drawText(t.text,r,n,l,a,i),new Rectangle(r,n,l,0)},DrawIcon:function(e,t){let r=t.x||0,n=t.y||0,l=t.iconIndex||0,a=ImageManager.loadSystem("IconSet"),i=Window_Base._iconWidth,o=Window_Base._iconHeight,u=l%16*i,f=Math.floor(l/16)*o,p=t.dw||i,d=t.dh||o;return e.blt(a,u,f,i,o,r,n,p,d),new Rectangle(r,n,p,d)},IsLoaded:function(e){return"loaded"==e._loadingState},OnceIsLoadedAll:function(e,t,r){MiscHelper.Once(t,function(){let t=!0;for(let r=0;r<e.length;r++)if(!(t&="loaded"==e[r]._loadingState))return!1;return t},r)}},AlgoHelper={Pathfind2d:function(e=[[]],t,r,n,l){let a=[[t,r]];if(t===n&&r===l)return a;let i=e.length,o=e[0].length,u=ArrayHelper.CreateArray(i,o);if(!(t>=0&&t<o&&r>=0&&r<i&&n>=0&&n<o&&l>=0&&l<i))return console.warn("Warning: start or end point are out of bounce!"),a;let f,p=[[1,0],[0,1],[-1,0],[0,-1]],d=[[t,r,a]],c=!1;for(;d.length>0&&!c;){f=[];for(let t of d){let r=t[0],d=t[1],g=t[2];u[d][r]=1;for(let t of p){let p=[r+t[0],d+t[1]];if(p[0]>=0&&p[0]<o&&p[1]>=0&&p[1]<i&&0===u[p[1]][p[0]]&&e[p[1]][p[0]]){let e=g.slice(0);if(e.push([p[0],p[1]]),p.push(e),f.push(p),p[0]===n&&p[1]===l){c=!0,a=e;break}}}}d=f}return a},PathfindReversed2d:function(e=[[]],t,r,n,l){let a=[[t,r]];if(t===n&&r===l)return a;let i=e.length,o=e[0].length,u=ArrayHelper.CreateArray(i,o);if(!(t>=0&&t<o&&r>=0&&r<i&&n>=0&&n<o&&l>=0&&l<i))return console.warn("Warning: start or end point are out of bounce!"),a;let f,p=[[1,0],[0,1],[-1,0],[0,-1]],d=[[t,r]],c=!1,g=1;for(;d.length>0&&!c;){f=[];for(let t of d){let r=t[0],a=t[1];0===u[a][r]&&(u[a][r]=g);for(let t of p){let p=[r+t[0],a+t[1]];if(p[0]>=0&&p[0]<o&&p[1]>=0&&p[1]<i&&0===u[p[1]][p[0]]&&e[p[1]][p[0]]&&(f.push(p),p[0]===n&&p[1]===l)){c=!0,u[l][n]=g+1;break}}}d=f,g++}if(!c)return[];let h=n,s=l,H=g,y=[[n,l]];for(let e=0;e<g-1;e++)for(let e of p){let t=[h+e[0],s+e[1]];if(t[0]>=0&&t[0]<o&&t[1]>=0&&t[1]<i){let e=u[t[1]][t[0]];if(e<H){H=e,h=t[0],s=t[1],y.push(t);break}}}return a=y}},MiscHelper={Once:function(e,t,r){let n=()=>{t.call(e)?r.call(e):requestAnimationFrame(n)};n()}},Helper={ArrayHelper:ArrayHelper,FunctionHelper:FunctionHelper,AlgoHelper:AlgoHelper,MiscHelper:MiscHelper};