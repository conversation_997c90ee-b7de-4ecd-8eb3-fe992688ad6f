let $children=[];{function BirthChild(){$children.push({MotherId:$gameVariables._data[21]||10,Name:$gameActors._data[9]._name,Birthday:{day:Var(10),month:Var(9)},Gender:<PERSON><PERSON><PERSON>(<PERSON><PERSON>(60))})}function FillData(){$children.push({MotherId:1,Name:"a",Birthday:{day:12,month:1},Gender:!0}),$children.push({MotherId:1,Name:"b",Birthday:{day:12,month:8},Gender:!0}),$children.push({MotherId:2,Name:"c",Birthday:{day:1,month:5},Gender:!1}),$children.push({MotherId:1,Name:"d",Birthday:{day:2,month:4},Gender:!0}),$children.push({MotherId:3,Name:"e",Birthday:{day:4,month:2},Gender:!1}),$children.push({MotherId:1,Name:"f",Birthday:{day:6,month:2},Gender:!1}),$children.push({MotherId:3,Name:"g",Birthday:{day:21,month:5},Gender:!0}),$children.push({MotherId:1,Name:"h",Birthday:{day:12,month:3},Gender:!1}),$children.push({MotherId:2,Name:"i",Birthday:{day:5,month:5},Gender:!0}),$children.push({MotherId:3,Name:"j",Birthday:{day:9,month:2},Gender:!0}),$children.push({MotherId:2,Name:"k",Birthday:{day:8,month:4},Gender:!0}),$children.push({MotherId:2,Name:"l",Birthday:{day:14,month:2},Gender:!0})}function AddData(){$children.push({MotherId:Math.randomInt(20)+10,Name:"Testname"+Math.randomInt(100),Birthday:{day:Math.randomInt(30)+1,month:Math.randomInt(11)+1},Gender:Boolean(Math.randomInt(2))})}function AddData2(e=10,t="test"){$children.push({MotherId:e,Name:t,Birthday:{day:Math.randomInt(30)+1,month:Math.randomInt(11)+1},Gender:Boolean(Math.randomInt(2))})}function mass(){for(let e=0;e<1e4;e++)AddData()}function GetChildrenCount(){return $children.length}function GetMaleChildrenCount(){return $children.filter(e=>e.Gender).length}function GetFemaleChildrenCount(){return $children.filter(e=>!e.Gender).length}function GetChildrenFrom(e){return $children.filter(t=>t.MotherId===e)}function GetMaleChildrenFrom(e){return $children.filter(t=>t.MotherId===e&&t.Gender)}function GetFemaleChildrenFrom(e){return $children.filter(t=>t.MotherId===e&&!t.Gender)}function GetChildrenCountFrom(e){return GetChildrenFrom(e).length}function GetMaleChildrenCountFrom(e){return GetMaleChildrenFrom(e).length}function GetFemaleChildrenCountFrom(e){return GetFemaleChildrenFrom(e).length}function Var(e){return $gameVariables._data[e]||0}function Window_Children(){this.initialize.apply(this,arguments)}function Scene_Children(){this.initialize.apply(this,arguments)}Window_Children.prototype=Object.create(Window_Base.prototype),Window_Children.prototype.constructor=Window_Children,Window_Children.prototype.initialize=function(e,t,n){Window_Base.prototype.initialize.call(this,0,0,Graphics.width,Graphics.height),this.PageIndex=0,this.Pages=Math.floor(($children.length-1)/8),this.Redraw=!1,this.draw()},Window_Children.prototype.draw=function(){this.contents.clear(),this.contents.fontSize=28;for(let e=1;e<5;e++)this.contents.fillRect(208*e,0,2,Graphics.height-96,"#FFFFFF");this.contents.fillRect(0,32,Graphics.width-32,2,"#FFFFFF");for(let e=1;e<9;e++)this.contents.fillRect(0,100*e+32,Graphics.width-32,2,"#FFFFFF");let e=ImageManager.loadPicture("mother_faces");this.contents.drawText("Mother",208,0,208,24,"center"),this.contents.drawText("Child",416,0,208,24,"center"),this.contents.drawText("Gender",624,0,208,24,"center"),this.contents.drawText("Birthday",832,0,330,24,"center");for(let t=0;t<8;t++){let n=$children[t+8*this.PageIndex];if(n){this.drawBitmapCut(e,14,100*t+32+8,0,86*(n.MotherId-10),180,86);let r=Var(1),i=[`${n.Birthday.month}月${n.Birthday.day}日`,`${n.Birthday.month}/${n.Birthday.day}`],a=["",$dataActors[n.MotherId].name,n.Name,["female","male"][n.Gender+0],i[r]];for(let e=1;e<5;e++)this.contents.drawText(a[e],208*e,100*t+32+40,e>=4?330:208,24,"center")}}this.contents.drawText(`${this.PageIndex+1}/${Math.max(this.Pages+1,1)}`,0,Graphics.height-32-48,Graphics.width-32,24,"center"),this.contents.fontSize=18,this.contents.drawText("Page up: [^] or [pageUp], Page down: [v] or [pageDown]",Graphics.width-(Graphics.width/2.25+48),Graphics.height-32-48,Graphics.width/2.25,32,"center"),this.contents.fontSize=28,this.Redraw="requesting"===e._loadingState},Window_Children.prototype.update=function(){this.Redraw&&this.draw()},Window_Children.prototype.pageUp=function(){this.PageIndex<this.Pages&&(this.PageIndex++,this.draw())},Window_Children.prototype.pageDown=function(){this.PageIndex>0&&(this.PageIndex--,this.draw())},Scene_Children.prototype=Object.create(Scene_MenuBase.prototype),Scene_Children.prototype.constructor=Scene_Children,Scene_Children.prototype.initialize=function(){Scene_MenuBase.prototype.initialize.call(this)},Scene_Children.prototype.create=function(){Scene_MenuBase.prototype.create.call(this),this.ChildrenWindow=new Window_Children,this.addWindow(this.ChildrenWindow)},Scene_Children.prototype.update=function(){this.ChildrenWindow.update(),Input.isTriggered("cancel")&&(AudioManager.playSe({name:"Cancel2",pan:0,pitch:100,volume:100}),SceneManager.pop()),(Input.isTriggered("right")||Input.isTriggered("up")||Input.isTriggered("pageup"))&&(AudioManager.playSe({name:"Cursor2",pan:0,pitch:100,volume:100}),this.ChildrenWindow.pageUp(),this.ChildrenWindow.draw()),(Input.isTriggered("left")||Input.isTriggered("down")||Input.isTriggered("pagedown"))&&(AudioManager.playSe({name:"Cursor2",pan:0,pitch:100,volume:100}),this.ChildrenWindow.pageDown(),this.ChildrenWindow.draw())};let e=Scene_Load.prototype.onLoadSuccess;Scene_Load.prototype.onLoadSuccess=function(){e.call(this),$gameSystem.children&&($children=$gameSystem.children)};let t=DataManager.createGameObjects;DataManager.createGameObjects=function(){t.call(this),$children=[]};let n=DataManager.saveGame;DataManager.saveGame=function(){return $gameSystem.children=$children,n.apply(DataManager,arguments)}}