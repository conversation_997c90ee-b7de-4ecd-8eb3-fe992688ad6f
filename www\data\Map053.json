{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 30, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "デスブロウ射精", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****　交尾中に敵が死んだら最後の大射精 *****"]}, {"code": 408, "indent": 0, "parameters": ["*****　デスブロウ射精 *****"]}, {"code": 355, "indent": 0, "parameters": ["$death_blow_cumming = 1"]}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow1\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 98, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow2_lewd\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow2_normal\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow3\")"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 111, "indent": 1, "parameters": [1, 53, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "stand-heroine-doggy-cum_tamari", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [30, "stand-heroine-doggy-cum_goblin", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 53, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [13, "stand-heroine-taneduke_press-cum-pussy", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_sound_cum1\")"]}, {"code": 117, "indent": 1, "parameters": [42]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow4\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow5\")"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_sound_cum1\")"]}, {"code": 117, "indent": 1, "parameters": [42]}, {"code": 111, "indent": 1, "parameters": [1, 82, 0, 50, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow6_lewd\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow6_normal\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow5\")"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_sound_cum1\")"]}, {"code": 117, "indent": 1, "parameters": [42]}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 98, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow7_lewd\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow8_lewd\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow9_lewd\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_deathblow7_normal\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_deathblow99\")"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 111, "indent": 1, "parameters": [1, 87, 0, 100, 1]}, {"code": 121, "indent": 2, "parameters": [43, 43, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$death_blow_cumming = 0"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "状態異常系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["処理内容をコモン３８０へ移動した"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"処理内容をコモン380へ移動した\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["* 暗闇時の攻撃処理 *"]}, {"code": 408, "indent": 0, "parameters": ["**************************"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_battle_attack_in_blind\")"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "Slash1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_blind_f_fire1\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_blind_f_fire2\")"]}, {"code": 311, "indent": 2, "parameters": [0, 2, 1, 0, 1, false]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 355, "indent": 2, "parameters": ["$hit = false"]}, {"code": 121, "indent": 2, "parameters": [83, 83, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["ミス"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 2, 1]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 8, 2]}, {"code": 118, "indent": 2, "parameters": ["ミス"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Evasion1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_blind_atk_miss\")"]}, {"code": 355, "indent": 2, "parameters": ["$hit = false"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 9, 1]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 10, 2]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_blind_atk_hit\")"]}, {"code": 355, "indent": 2, "parameters": ["$hit = true"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "敗北時のイベント処理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マッ<PERSON> 53 3 1開始\")"]}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["戦闘敗北処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["敵グラ消去"]}, {"code": 355, "indent": 0, "parameters": ["screen.pictures[0].erase"]}, {"code": 355, "indent": 0, "parameters": ["screen.pictures[95].erase"]}, {"code": 355, "indent": 0, "parameters": ["screen.pictures[96].erase"]}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["チュートリアル敗北処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 222, 1]}, {"code": 111, "indent": 1, "parameters": [1, 902, 0, 6, 1]}, {"code": 111, "indent": 2, "parameters": [1, 902, 0, 8, 4]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [902, 902, 0, 0, 5]}, {"code": 201, "indent": 3, "parameters": [0, 332, 6, 5, 4, 0]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 201, "indent": 3, "parameters": [0, 333, 63, 11, 4, 0]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 242, "indent": 3, "parameters": [2]}, {"code": 245, "indent": 3, "parameters": [{"name": "<PERSON><PERSON>s", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [120]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ストーリークエスト敗北処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(53, 3, 4)"]}, {"code": 111, "indent": 0, "parameters": [0, 178, 0]}, {"code": 121, "indent": 1, "parameters": [178, 178, 1]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["サイドクエスト等"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["歓楽街のサキュバスを助ける"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.mapId() === 137"]}, {"code": 111, "indent": 1, "parameters": [1, 14, 0, 31, 0]}, {"code": 108, "indent": 2, "parameters": ["コモンじゃないからここでセルフスイッチ操作してもこの処理イベント"]}, {"code": 408, "indent": 2, "parameters": ["でのセルフがオンオフになるだけ。"]}, {"code": 408, "indent": 2, "parameters": ["なのでＨＰ０でトリガーするようにしてる"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエスト『荷物運び２』で盗賊に敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 1011, 0, 3, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Down4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_quest_failed\")"]}, {"code": 126, "indent": 1, "parameters": [502, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [1011, 1011, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 322]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -2]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["key = [103, 13, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエスト『王都に迫る悪魔の陰』で女子学生に敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 1012, 0, 10, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Down4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_quest_failed\")"]}, {"code": 122, "indent": 1, "parameters": [1012, 1012, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 322]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -2]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["key = [14, 67, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 355, "indent": 1, "parameters": ["key = [8, 35, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 355, "indent": 1, "parameters": ["key = [96, 42, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 355, "indent": 1, "parameters": ["key = [11, 42, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 355, "indent": 1, "parameters": ["key = [244, 23, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue(key, false)"]}, {"code": 108, "indent": 1, "parameters": ["一時的にダイスを９９９にすることで敗北後、大元のイベントをスキップさせる"]}, {"code": 408, "indent": 1, "parameters": ["大元のイベント= マップ137 イベント34　（路地裏）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエスト『不死なる者』の研究所で敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 1012, 0, 13, 1]}, {"code": 111, "indent": 1, "parameters": [1, 1012, 0, 25, 4]}, {"code": 122, "indent": 2, "parameters": [1212, 1212, 0, 0, 12]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["従者敗北イベントの処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["***ゴブリン***"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 8, 0, 21, 0]}, {"code": 119, "indent": 2, "parameters": ["特殊エネミー敗北処理"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 121, 0, 201, 0]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["司祭のダウングラフィックが戦闘中オンだとＸの位置がズレる設定なので、"]}, {"code": 408, "indent": 2, "parameters": ["ここで戦闘中スイッチをオフにしている"]}, {"code": 121, "indent": 2, "parameters": [14, 14, 1]}, {"code": 242, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"SelectLoseEvent1\")"]}, {"code": 655, "indent": 2, "parameters": ["val_in_database(62, \"SelectLoseEvent2\")"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], -1, -1, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 111, "indent": 3, "parameters": [4, 10, 0]}, {"code": 201, "indent": 4, "parameters": [0, 330, 23, 14, 0, 0]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(330, 9, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [4, 19, 0]}, {"code": 201, "indent": 4, "parameters": [0, 330, 23, 14, 0, 0]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(330, 12, 1)"]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["特殊エネミー敗北処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["特殊エネミー敗北処理"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["オーガ敗北時の処理"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 221, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 201, "indent": 1, "parameters": [0, 90, 8, 3, 0, 0]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["スライム敗北時の処理(未完成なのでダンジョン入り口に飛ばす）"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 601, 0]}, {"code": 119, "indent": 1, "parameters": ["街に戻る"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 201, "indent": 2, "parameters": [0, 44, 12, 42, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 8, 13, 16, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ゴブリンライダーに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 202, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 8, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["オーク豚に敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 219, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["key = [200, 3, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 1, 2)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ワーウルフに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 226, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 2, 2)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["アケパロイに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 281, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 3, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケンちゃんに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 255, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 4, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ドライアドに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 263, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 5, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ラミアに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 261, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 6, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["メデューサに敗北する"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 262, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 7, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["リザードマン"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 251, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 9, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["レッドキャップ"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 282, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(201, 10, 1)"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["通常処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"通常敗北処理開始\")"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["console.log(\"司祭はパーティにいます\")"]}, {"code": 111, "indent": 1, "parameters": [0, 61, 0]}, {"code": 355, "indent": 2, "parameters": ["console.log(\"司祭がいてかつ敵がエロ攻撃所持\")"]}, {"code": 108, "indent": 2, "parameters": ["****************************************"]}, {"code": 408, "indent": 2, "parameters": ["エロ敵の場合は敗北後レイプ。更にネストがある敵は連れ去られる。"]}, {"code": 408, "indent": 2, "parameters": ["****************************************"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [0, 65, 1]}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 3, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 3, "parameters": [60]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 221, "indent": 2, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-dungeon3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [65]}, {"code": 231, "indent": 2, "parameters": [50, "event-0-hand", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_3"]}, {"code": 111, "indent": 2, "parameters": [0, 44, 1]}, {"code": 117, "indent": 3, "parameters": [23]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 3, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 3, "parameters": [65]}, {"code": 250, "indent": 3, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_4"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_5"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_6"]}, {"code": 242, "indent": 2, "parameters": [3]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_7"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_8"]}, {"code": 108, "indent": 2, "parameters": ["視界悪くなる"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [200]}, {"code": 245, "indent": 2, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 30}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_8-11"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 2, "parameters": [62]}, {"code": 355, "indent": 2, "parameters": ["$gameScreen.erasePicture($layer5);"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.erasePicture($layer6);"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.erasePicture($layer7);"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.erasePicture($layer8);"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.erasePicture($layer15);"]}, {"code": 231, "indent": 2, "parameters": [70, "closing_eye", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 245, "indent": 2, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-dungeon3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_12-16"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 2, "parameters": [73]}, {"code": 231, "indent": 2, "parameters": [16, "stand-heroine-a<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-cum", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 231, "indent": 2, "parameters": [70, "closing_eye", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_16-19"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [26, 26, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [39, 39, 0, 1, 121]}, {"code": 117, "indent": 2, "parameters": [74]}, {"code": 235, "indent": 2, "parameters": [3]}, {"code": 235, "indent": 2, "parameters": [18]}, {"code": 231, "indent": 2, "parameters": [50, "event-0-hand", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 231, "indent": 2, "parameters": [70, "closing_eye", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_20"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_21"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(0,100)"]}, {"code": 129, "indent": 2, "parameters": [2, 1, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["街に戻る処理"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 1, "parameters": ["街に戻る"]}, {"code": 355, "indent": 1, "parameters": ["console.log(\"街に戻る処理\")"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾーがパーティに居て且つ通常敵に敗北した場合"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 1, "parameters": [0, 32, 0]}, {"code": 111, "indent": 2, "parameters": [0, 61, 1]}, {"code": 111, "indent": 3, "parameters": [4, 21, 0]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 230, "indent": 4, "parameters": [120]}, {"code": 201, "indent": 4, "parameters": [0, 315, 13, 10, 0, 0]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["通常の敵の場合は敗北後に街へと戻るだけ。"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(53, 3, 2)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 108, "indent": 1, "parameters": ["****************************************"]}, {"code": 408, "indent": 1, "parameters": ["通常敗北時はコハクと会話"]}, {"code": 408, "indent": 1, "parameters": ["****************************************"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map53_ev3_p1_1a\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map53_ev3_p1_1b\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_111"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_112"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_113"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_114"]}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_115"]}, {"code": 354, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["******************************************************"]}, {"code": 408, "indent": 2, "parameters": ["コハクが連れ去られた場合"]}, {"code": 408, "indent": 2, "parameters": ["******************************************************"]}, {"code": 243, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 241, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 249, "indent": 2, "parameters": [{"name": "Shock2", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 2, "parameters": [{"name": "heart", "pan": 0, "pitch": 130, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_120-122"]}, {"code": 246, "indent": 2, "parameters": [1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [284]}, {"code": 122, "indent": 2, "parameters": [261, 261, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [263, 263, 0, 1, 83]}, {"code": 111, "indent": 2, "parameters": [1, 263, 0, 0, 2]}, {"code": 122, "indent": 3, "parameters": [263, 263, 0, 0, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 200, 0]}, {"code": 201, "indent": 3, "parameters": [0, 59, 11, 8, 0, 0]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_190"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [23, 30, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**********************************************************"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["主人公がソロのとき"]}, {"code": 408, "indent": 1, "parameters": [""]}, {"code": 408, "indent": 1, "parameters": ["**********************************************************"]}, {"code": 355, "indent": 1, "parameters": ["console.log(\"主人公がソロの時の敗北処理を開始します\")"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 249, "indent": 1, "parameters": [{"name": "Gameover2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_200"]}, {"code": 117, "indent": 1, "parameters": [286]}, {"code": 111, "indent": 1, "parameters": [0, 200, 0]}, {"code": 108, "indent": 2, "parameters": ["****"]}, {"code": 408, "indent": 2, "parameters": ["ダンジョンテスト中の場合"]}, {"code": 408, "indent": 2, "parameters": ["****"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(61, \"select_map53_ev3_p1_2a\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(62, \"select_map53_ev3_p1_2b\")"]}, {"code": 355, "indent": 2, "parameters": ["val_in_database(63, \"select_map53_ev3_p1_2c\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_201"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 201, "indent": 3, "parameters": [0, 59, 11, 8, 0, 0]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 249, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["clear_mlog"]}, {"code": 655, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 121, "indent": 3, "parameters": [58, 58, 1]}, {"code": 122, "indent": 3, "parameters": [261, 264, 0, 0, 0]}, {"code": 129, "indent": 3, "parameters": [2, 0, 0]}, {"code": 319, "indent": 3, "parameters": [2, 1, 101]}, {"code": 319, "indent": 3, "parameters": [2, 2, 82]}, {"code": 319, "indent": 3, "parameters": [2, 3, 92]}, {"code": 319, "indent": 3, "parameters": [2, 4, 102]}, {"code": 319, "indent": 3, "parameters": [2, 5, 0]}, {"code": 121, "indent": 3, "parameters": [44, 44, 1]}, {"code": 201, "indent": 3, "parameters": [0, 44, 12, 41, 0, 0]}, {"code": 249, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [25]}, {"code": 313, "indent": 3, "parameters": [0, 0, 1, 1]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 326, "indent": 3, "parameters": [0, 0, 1, 0, 100]}, {"code": 355, "indent": 3, "parameters": ["clear_mlog"]}, {"code": 655, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [2, "\\v[63]"]}, {"code": 354, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["****"]}, {"code": 408, "indent": 2, "parameters": ["通常処理"]}, {"code": 408, "indent": 2, "parameters": ["****"]}, {"code": 111, "indent": 2, "parameters": [0, 222, 0]}, {"code": 108, "indent": 3, "parameters": ["本編が開始されている場合は宿屋に戻す"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 326, "indent": 3, "parameters": [0, 0, 1, 0, 100]}, {"code": 201, "indent": 3, "parameters": [0, 22, 4, 4, 2, 0]}, {"code": 249, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["clear_mlog"]}, {"code": 655, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["本編が開始されていない場合はゲームオーバー画面へ"]}, {"code": 354, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マッ<PERSON> 53 3 1終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["パーティをリフレッシュして王都へと戻す処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["従者を外す処理"]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 0; i < $gameParty.members().length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["  var x = $gameParty.members()[i].actorId();"]}, {"code": 655, "indent": 0, "parameters": ["  if ($dataActors[x].note.indexOf(\"固定メンバー\") == -1) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.removeActor(x);"]}, {"code": 655, "indent": 0, "parameters": ["    i--; // アクターが削除された場合、次のループで同じインデックスを確認する"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [8, 8, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["救助者を外す処理と名声を下げる処理"]}, {"code": 355, "indent": 0, "parameters": ["var i = 0;"]}, {"code": 655, "indent": 0, "parameters": ["while (i < $gameParty.members().length) {"]}, {"code": 655, "indent": 0, "parameters": ["  if ($gameParty.members()[i] != null) {"]}, {"code": 655, "indent": 0, "parameters": ["    var id = $gameParty.members()[i].actorId();"]}, {"code": 655, "indent": 0, "parameters": ["    if ($dataActors[id].note.indexOf(\"救助者\") !== -1) {"]}, {"code": 655, "indent": 0, "parameters": ["      $gameSwitches.setValue(89, true);"]}, {"code": 655, "indent": 0, "parameters": ["      break; // \"救助者\"が見つかったらループを終了"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["  i += 1;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var i = 0;"]}, {"code": 655, "indent": 0, "parameters": ["while (i < $gameParty.members().length) {"]}, {"code": 655, "indent": 0, "parameters": ["  var id = $gameParty.members()[i].actorId();"]}, {"code": 655, "indent": 0, "parameters": ["  if ($dataActors[id].note.indexOf(\"救助者\") !== -1) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.removeActor(id);"]}, {"code": 655, "indent": 0, "parameters": ["  } else {"]}, {"code": 655, "indent": 0, "parameters": ["    i += 1;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 89, 0]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_failure_to_rescue\")"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 322]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["街に戻る処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 314, "indent": 0, "parameters": [0, 0]}, {"code": 326, "indent": 0, "parameters": [0, 0, 1, 0, 100]}, {"code": 122, "indent": 0, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 0, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 0, 1, 1522]}, {"code": 122, "indent": 0, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 0, 17]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 0, 8]}, {"code": 122, "indent": 0, "parameters": [335, 335, 0, 1, 42]}, {"code": 122, "indent": 0, "parameters": [336, 336, 0, 1, 43]}, {"code": 355, "indent": 0, "parameters": ["$WMTS.X = Var(335);"]}, {"code": 655, "indent": 0, "parameters": ["$WMTS.Y = Var(336);"]}, {"code": 201, "indent": 0, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["旧ストーリー処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ここから下は旧イベ　全部消す"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『街道警備』中にゴブリンに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 903, 0, 3, 0]}, {"code": 122, "indent": 1, "parameters": [903, 903, 0, 0, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 100, 6, 6, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『地下下水の異変』中にアンノウンに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 904, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 121, 0, 901, 0]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 8, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$gameSelfSwitches.setValue(key,false);"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 19, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 19, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 20, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 20, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 21, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 21, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 22, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 22, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 23, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 23, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 24, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [152, 24, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 203, "indent": 2, "parameters": [1, 0, 0, 0, 2]}, {"code": 205, "indent": 2, "parameters": [1, {"list": [{"code": 34, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 34, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 201, "indent": 2, "parameters": [0, 151, 37, 1, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_tips1\")"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["Ch.1ストーリークエスト『内密な調査依頼』中に追い剥ぎに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 41, 0, 155, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["key = [155, 5, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 1, "parameters": ["key = [155, 6, \"A\"]"]}, {"code": 655, "indent": 1, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 1, "parameters": ["key = [155, 5, \"B\"]"]}, {"code": 655, "indent": 1, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 1, "parameters": ["key = [155, 6, \"B\"]"]}, {"code": 655, "indent": 1, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 122, "indent": 1, "parameters": [335, 335, 0, 0, 13]}, {"code": 122, "indent": 1, "parameters": [336, 336, 0, 0, 10]}, {"code": 201, "indent": 1, "parameters": [0, 50, 15, 7, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["Ch.1ストーリークエスト『アンノウン』の調査"]}, {"code": 111, "indent": 0, "parameters": [1, 906, 0, 11, 4]}, {"code": 111, "indent": 1, "parameters": [1, 906, 0, 7, 1]}, {"code": 111, "indent": 2, "parameters": [1, 121, 0, 901, 0]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 122, "indent": 3, "parameters": [906, 906, 0, 0, 6]}, {"code": 129, "indent": 3, "parameters": [96, 0, 0]}, {"code": 201, "indent": 3, "parameters": [0, 162, 33, 38, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_tips1\")"]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 906, 0, 11, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_restart1\")"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(62, \"select_restart2\")"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 2, "parameters": [906, 906, 0, 0, 9]}, {"code": 201, "indent": 2, "parameters": [0, 162, 40, 43, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_tips1\")"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 2, "parameters": [906, 906, 0, 0, 3]}, {"code": 201, "indent": 2, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_tips1\")"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["Ch.2ストーリークエスト『昇級試験』で？？？に敗北"]}, {"code": 111, "indent": 0, "parameters": [0, 297, 1]}, {"code": 111, "indent": 1, "parameters": [1, 121, 0, 985, 0]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 111, "indent": 2, "parameters": [1, 3, 0, 1, 2]}, {"code": 111, "indent": 3, "parameters": [12, "$skippable == null"]}, {"code": 355, "indent": 4, "parameters": ["$skippable = 1"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 355, "indent": 4, "parameters": ["$skippable += 1"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$skippable == 3"]}, {"code": 355, "indent": 3, "parameters": ["val_in_database(61, \"select_no\")"]}, {"code": 355, "indent": 3, "parameters": ["val_in_database(62, \"select_yes\")"]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_9999"]}, {"code": 102, "indent": 3, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "\\v[62]"]}, {"code": 216, "indent": 4, "parameters": [0]}, {"code": 121, "indent": 4, "parameters": [56, 56, 1]}, {"code": 117, "indent": 4, "parameters": [58]}, {"code": 314, "indent": 4, "parameters": [0, 0]}, {"code": 201, "indent": 4, "parameters": [0, 179, 12, 23, 8, 0]}, {"code": 355, "indent": 4, "parameters": ["clear_mlog"]}, {"code": 241, "indent": 4, "parameters": [{"name": "harmonic-evil2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 4, "parameters": ["$skippable = 0"]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["key = [178, 4, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [178, 5, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [178, 5, \"B\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 355, "indent": 2, "parameters": ["key = [178, 8, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key] = false"]}, {"code": 216, "indent": 2, "parameters": [0]}, {"code": 121, "indent": 2, "parameters": [56, 56, 1]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 201, "indent": 2, "parameters": [0, 177, 45, 36, 2, 0]}, {"code": 135, "indent": 2, "parameters": [1]}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 241, "indent": 2, "parameters": [{"name": "harmonic-forest", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリーイベント『孤児院の安全』でゴブリン・アルケミストに敗北"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 204, 0]}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.mapId() === 80"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["key = [80, 5, \"A\"]"]}, {"code": 655, "indent": 2, "parameters": ["$game_self_switches[key, false)"]}, {"code": 201, "indent": 2, "parameters": [0, 77, 7, 11, 2, 0]}, {"code": 313, "indent": 2, "parameters": [0, 0, 1, 1]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(0,100)"]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリーイベント『孤児院の安全』で仮面の騎士に敗北"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 950, 0]}, {"code": 111, "indent": 1, "parameters": [1, 914, 0, 9, 0]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリーイベント『あしながおじさん』でノールに敗北"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 915, 0, 3, 1]}, {"code": 111, "indent": 1, "parameters": [1, 915, 0, 99, 4]}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(121) == 241 or $gameVariables.value(121) == 242"]}, {"code": 242, "indent": 3, "parameters": [2]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 223, "indent": 3, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 129, "indent": 3, "parameters": [2, 0, 0]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 122, "indent": 3, "parameters": [915, 915, 0, 0, 2]}, {"code": 314, "indent": 3, "parameters": [0, 0]}, {"code": 355, "indent": 3, "parameters": ["tp_heal(0,100)"]}, {"code": 117, "indent": 3, "parameters": [454]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『悪しき狩人たち』中にノールに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 916, 0, 6, 0]}, {"code": 122, "indent": 1, "parameters": [916, 916, 0, 0, 3]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『悪しき狩人たち』中に４Fで敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 916, 0, 16, 1]}, {"code": 111, "indent": 1, "parameters": [1, 916, 0, 17, 2]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 207, 36, 7, 2, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『悪しき狩人たち』中にトゥーヘッドに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 916, 0, 18, 0]}, {"code": 122, "indent": 1, "parameters": [916, 916, 0, 0, 17]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 207, 6, 7, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『ポイズン・ピンク』"]}, {"code": 408, "indent": 0, "parameters": ["遺跡入口のノール達に敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 923, 0, 5, 0]}, {"code": 122, "indent": 1, "parameters": [923, 923, 0, 0, 4]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(1502, \"location_name7\")"]}, {"code": 122, "indent": 1, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 13]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [335, 335, 0, 1, 42]}, {"code": 122, "indent": 1, "parameters": [336, 336, 0, 1, 43]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.X = Var(335);"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Y = Var(336);"]}, {"code": 201, "indent": 1, "parameters": [0, 85, 9, 23, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『ポイズン・ピンク』"]}, {"code": 408, "indent": 0, "parameters": ["遺跡奥のノール虐殺者に敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 923, 0, 9, 0]}, {"code": 122, "indent": 1, "parameters": [923, 923, 0, 0, 7]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(1502, \"location_name7\")"]}, {"code": 122, "indent": 1, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 13]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [335, 335, 0, 1, 42]}, {"code": 122, "indent": 1, "parameters": [336, 336, 0, 1, 43]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.X = Var(335);"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Y = Var(336);"]}, {"code": 201, "indent": 1, "parameters": [0, 85, 9, 23, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『ポイズン・ピンク』"]}, {"code": 408, "indent": 0, "parameters": ["デーモン・ケルベロスに敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 923, 0, 13, 0]}, {"code": 122, "indent": 1, "parameters": [923, 923, 0, 0, 7]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["clear_mlog"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(0,100)"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(1502, \"location_name7\")"]}, {"code": 122, "indent": 1, "parameters": [1510, 1510, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [1503, 1503, 0, 4, "\"city\""]}, {"code": 122, "indent": 1, "parameters": [1504, 1504, 0, 4, "\"平和\""]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 0, 1, 1528]}, {"code": 122, "indent": 1, "parameters": [1506, 1506, 1, 1, 322]}, {"code": 122, "indent": 1, "parameters": [41, 41, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [42, 42, 0, 0, 13]}, {"code": 122, "indent": 1, "parameters": [43, 43, 0, 0, 20]}, {"code": 122, "indent": 1, "parameters": [335, 335, 0, 1, 42]}, {"code": 122, "indent": 1, "parameters": [336, 336, 0, 1, 43]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$WMTS.X = Var(335);"]}, {"code": 655, "indent": 1, "parameters": ["$WMTS.Y = Var(336);"]}, {"code": 201, "indent": 1, "parameters": [0, 85, 9, 23, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ストーリイベント『ポイズン・ピンク』"]}, {"code": 408, "indent": 0, "parameters": ["スラム街のデーモン達に敗北する"]}, {"code": 111, "indent": 0, "parameters": [1, 923, 0, 20, 1]}, {"code": 111, "indent": 1, "parameters": [1, 923, 0, 99, 4]}, {"code": 122, "indent": 2, "parameters": [923, 923, 0, 0, 18]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 236, "indent": 2, "parameters": ["none", 9, 0, true]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_game_over\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(0,100)"]}, {"code": 201, "indent": 2, "parameters": [0, 8, 13, 16, 2, 0]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["新ストーリー処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["クエストID900102"]}, {"code": 408, "indent": 0, "parameters": ["地下下水の調査"]}, {"code": 111, "indent": 0, "parameters": [1, 903, 0, 3, 1]}, {"code": 111, "indent": 1, "parameters": [1, 903, 0, 6, 4]}, {"code": 122, "indent": 2, "parameters": [903, 903, 0, 0, 3]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["clear_mlog"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 201, "indent": 2, "parameters": [0, 98, 17, 24, 8, 0]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [178, 178, 0]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエストID900103"]}, {"code": 408, "indent": 0, "parameters": ["冒険者訓練"]}, {"code": 111, "indent": 0, "parameters": [1, 904, 0, 6, 0]}, {"code": 111, "indent": 1, "parameters": [1, 121, 0, 10003, 0]}, {"code": 122, "indent": 2, "parameters": [904, 904, 0, 0, 7]}, {"code": 121, "indent": 2, "parameters": [178, 178, 0]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエストID900103"]}, {"code": 408, "indent": 0, "parameters": ["冒険者訓練"]}, {"code": 111, "indent": 0, "parameters": [1, 906, 0, 2, 1]}, {"code": 111, "indent": 1, "parameters": [1, 906, 0, 99, 4]}, {"code": 111, "indent": 2, "parameters": [12, "$gameMap.mapId() === 339"]}, {"code": 122, "indent": 3, "parameters": [906, 906, 0, 0, 1]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエストID900107"]}, {"code": 408, "indent": 0, "parameters": ["ニンフ教団"]}, {"code": 111, "indent": 0, "parameters": [1, 908, 0, 3, 1]}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.mapId() === 341"]}, {"code": 108, "indent": 2, "parameters": ["あやつられた冒険者戦で敗北"]}, {"code": 111, "indent": 2, "parameters": [1, 908, 0, 6, 1]}, {"code": 111, "indent": 3, "parameters": [1, 908, 0, 12, 4]}, {"code": 122, "indent": 4, "parameters": [908, 908, 0, 0, 2]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(341, 54, 1)"]}, {"code": 121, "indent": 4, "parameters": [178, 178, 0]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["書庫"]}, {"code": 111, "indent": 2, "parameters": [1, 908, 0, 12, 1]}, {"code": 111, "indent": 3, "parameters": [1, 908, 0, 13, 4]}, {"code": 122, "indent": 4, "parameters": [908, 908, 0, 0, 12]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["アンノウン戦で敗北"]}, {"code": 111, "indent": 2, "parameters": [1, 908, 0, 14, 1]}, {"code": 111, "indent": 3, "parameters": [1, 908, 0, 17, 4]}, {"code": 122, "indent": 4, "parameters": [908, 908, 0, 0, 14]}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["クエストID900110"]}, {"code": 408, "indent": 0, "parameters": ["神の意思"]}, {"code": 111, "indent": 0, "parameters": [1, 911, 0, 4, 1]}, {"code": 111, "indent": 1, "parameters": [1, 911, 0, 99, 4]}, {"code": 122, "indent": 2, "parameters": [911, 911, 0, 0, 3]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["********************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 1}, {"id": 4, "name": "戦闘終了後イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 4 1開始\")"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 108, "indent": 1, "parameters": ["********************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘中にセクハラックスをされた"]}, {"code": 408, "indent": 1, "parameters": ["********************"]}, {"code": 111, "indent": 1, "parameters": [12, "$ev_ntr_in_combat >= 1"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 10, 1)"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["司祭が感度増加状態"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 15]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 7)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘中に司祭が魅了された"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 22]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 6)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘中に司祭を誤って攻撃した"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [0, 83, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘中に中出しされた場合イベント"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [0, 81, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["精液ボテ時のイベント"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [0, 43, 0]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 4)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘終了時に毒状態のときの処理"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 6, 2]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 4, 5)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["戦闘終了時にヤリゾーが毒状態のときの処理"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 111, "indent": 1, "parameters": [4, 21, 0]}, {"code": 111, "indent": 2, "parameters": [4, 21, 6, 2]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(53, 35, 2)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["*************"]}, {"code": 118, "indent": 1, "parameters": ["終了"]}, {"code": 111, "indent": 1, "parameters": [0, 44, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 319, "indent": 2, "parameters": [2, 2, 82]}, {"code": 319, "indent": 2, "parameters": [2, 3, 92]}, {"code": 319, "indent": 2, "parameters": [2, 4, 102]}, {"code": 121, "indent": 2, "parameters": [44, 44, 1]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 4 1終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["戦闘中に司祭を誤って攻撃した"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map53_ev4_p2_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map53_ev4_p2_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_6"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_7"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 86]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_999"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["戦闘中に中出しされた場合イベント"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 111, "indent": 0, "parameters": [0, 43, 0]}, {"code": 355, "indent": 1, "parameters": ["clear_equip(2)"]}, {"code": 121, "indent": 1, "parameters": [44, 44, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 12]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map53_ev4_p3_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map53_ev4_p3_1b\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_map53_ev4_p3_1c\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_4"]}, {"code": 119, "indent": 1, "parameters": ["中出しイベント終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["選択"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map53_ev4_p3_2a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map53_ev4_p3_2b\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(63, \"select_map53_ev4_p3_2c\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_6"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(61, \"select_map53_ev4_p3_3a\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_7"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_8"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["※こっから先は未実装"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["中出しイベント終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["精液ボテ時のイベント"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["※精液排出処理は調整中で未実装"]}, {"code": 121, "indent": 0, "parameters": [43, 43, 1]}, {"code": 122, "indent": 0, "parameters": [87, 87, 0, 0, 0]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["戦闘終了時に毒状態のときの処理"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 121, "indent": 0, "parameters": [1, 1, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 11]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_map53_ev4_p5_1a\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_map53_ev4_p5_1b\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 98, 1]}, {"code": 122, "indent": 2, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [39, 39, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 1, "parameters": [68]}, {"code": 108, "indent": 1, "parameters": ["舌"]}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(27)"]}, {"code": 655, "indent": 1, "parameters": ["$no = `${$e_name}-tongue-${num}`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer14]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3-9"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["$no = `${$e_name}-cum`"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 1, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_9-12"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_13"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_14"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_15"]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 313, "indent": 1, "parameters": [0, 1, 1, 2]}, {"code": 326, "indent": 1, "parameters": [0, 1, 0, 0, 10]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 542]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_16"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["戦闘中に司祭が魅了された"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************"]}, {"code": 408, "indent": 0, "parameters": ["司祭が感度倍増状態"]}, {"code": 408, "indent": 0, "parameters": ["*************"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [72]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_map_pgad9\")"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_map_pgad100\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["感度倍増状態のときに戦闘終了したら本来イベント発生するんですが、"]}, {"code": 401, "indent": 1, "parameters": ["現在のバージョンではイベントがスキップされます。ごめんね。"]}, {"code": 401, "indent": 1, "parameters": ["This event occurs when combat is finished and <PERSON><PERSON> has PGAD. "]}, {"code": 401, "indent": 1, "parameters": ["Currently the event is skipped for the demo."]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["旦那、こいつはマズイですぜ。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["この症状はどう見ても感度倍増の症状だ。"]}, {"code": 401, "indent": 1, "parameters": ["全身がクリトリスみたいに敏感になっちまうんでさぁ。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["\\c[2]めちゃくちゃオス力の高い人間の精液を子宮に入れなきゃ"]}, {"code": 401, "indent": 1, "parameters": ["なおんねぇ病気ですわ。\\c[0]"]}, {"code": 118, "indent": 1, "parameters": ["選択肢"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["ご存知の通り、アッシはちんぽには自信がありやす。"]}, {"code": 401, "indent": 1, "parameters": ["もちろんこの症状の女を何度も助けたことがあるんですが……"]}, {"code": 401, "indent": 1, "parameters": ["どうしやす？"]}, {"code": 102, "indent": 1, "parameters": [["自分でセックスする", "\\n[21]にセックスさせる if(v[31])", "今はこのままにしておく"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "自分でセックスする"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 2, "parameters": ["……わかりやした。"]}, {"code": 401, "indent": 2, "parameters": ["それじゃアッシは陰ながら応援してやすんで。"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\n[21]にセックスさせる if(v[31])"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 2, "parameters": ["わかりやした。"]}, {"code": 401, "indent": 2, "parameters": ["それじゃあ早速……"]}, {"code": 111, "indent": 2, "parameters": [1, 83, 1, 82, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["ま、待ってください！"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["今セックスなんてされたら死んでしまいます！"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["そ、それにっ……"]}, {"code": 401, "indent": 3, "parameters": ["私は\\v[50]のおちんぽ様のほうが……！"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["奥さん、旦那も苦渋の決断なんだ。"]}, {"code": 401, "indent": 3, "parameters": ["わかってやってくだせぇ。"]}, {"code": 401, "indent": 3, "parameters": ["でぇじょうぶです。アッシはこういうのには慣れてやすから。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["……\\v[50]……"]}, {"code": 401, "indent": 3, "parameters": ["ち、治療といえど……"]}, {"code": 401, "indent": 3, "parameters": ["ほ、本当に\\n[21]さんに中出しさせるおつもりですか……？"]}, {"code": 102, "indent": 3, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["……わかり……ました……"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 4, "parameters": ["……ほっ。"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 3, "parameters": ["はぁっ……はぁっ……お、お願い……します……"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 221, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "今はこのままにしておく"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 2, "parameters": ["いいんですかい？"]}, {"code": 401, "indent": 2, "parameters": ["奥さん、かなりツラそうですがね……"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "司祭戦闘エロ関係系", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭イく"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 117, "indent": 0, "parameters": [73]}, {"code": 231, "indent": 0, "parameters": [15, "stand-heroine-a<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-squirt", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 231, "indent": 0, "parameters": [16, "stand-heroine-a<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-cum", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["マジアクメ値の上昇"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 6, 3]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_hibikiwataru\")"]}, {"code": 122, "indent": 1, "parameters": [420, 420, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_state_down\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_state_akume\")"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 5 2開始\")"]}, {"code": 108, "indent": 0, "parameters": ["***************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["もがいて解放"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["シールドバッシュによる解放"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 10]}, {"code": 119, "indent": 1, "parameters": ["司祭"]}, {"code": 355, "indent": 1, "parameters": ["$shield_bash = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 11, 6, 10]}, {"code": 119, "indent": 1, "parameters": ["バーバリアン"]}, {"code": 355, "indent": 1, "parameters": ["$shield_bash = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 10, 6, 10]}, {"code": 119, "indent": 1, "parameters": ["ポーションメーカー"]}, {"code": 355, "indent": 1, "parameters": ["$shield_bash = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭"]}, {"code": 111, "indent": 0, "parameters": [1, 270, 0, 2, 0]}, {"code": 118, "indent": 1, "parameters": ["司祭"]}, {"code": 108, "indent": 1, "parameters": ["司祭拘束初期化"]}, {"code": 111, "indent": 1, "parameters": [0, 63, 0]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 111, "indent": 2, "parameters": [12, "$trait_death_penis == 1"]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 6]}, {"code": 119, "indent": 3, "parameters": ["司祭グラフィック表示"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 5]}, {"code": 108, "indent": 2, "parameters": ["***************************************"]}, {"code": 408, "indent": 2, "parameters": ["ここからグラフィック表示処理"]}, {"code": 118, "indent": 2, "parameters": ["司祭グラフィック表示"]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [331]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 10]}, {"code": 111, "indent": 2, "parameters": [4, 2, 6, 11]}, {"code": 313, "indent": 3, "parameters": [0, 2, 1, 11]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [63, 63, 1]}, {"code": 121, "indent": 2, "parameters": [64, 64, 1]}, {"code": 121, "indent": 2, "parameters": [65, 65, 1]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 231, "indent": 2, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["バーバリアン"]}, {"code": 111, "indent": 0, "parameters": [1, 270, 0, 9, 0]}, {"code": 118, "indent": 1, "parameters": ["バーバリアン"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [56]}, {"code": 117, "indent": 1, "parameters": [84]}, {"code": 313, "indent": 1, "parameters": [0, 11, 1, 10]}, {"code": 111, "indent": 1, "parameters": [4, 11, 6, 11]}, {"code": 313, "indent": 2, "parameters": [0, 11, 1, 11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["ポーションメーカー"]}, {"code": 111, "indent": 0, "parameters": [1, 270, 0, 10, 0]}, {"code": 118, "indent": 1, "parameters": ["ポーションメーカー"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [56]}, {"code": 117, "indent": 1, "parameters": [85]}, {"code": 313, "indent": 1, "parameters": [0, 10, 1, 10]}, {"code": 111, "indent": 1, "parameters": [4, 10, 6, 11]}, {"code": 313, "indent": 2, "parameters": [0, 10, 1, 11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 111, "indent": 1, "parameters": [12, "$gameActors.actor(8).isStateAffected(10)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["$chinira -= 5"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if ($chinira <= 0){"]}, {"code": 655, "indent": 0, "parameters": ["  $chinira = 0"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 117, "indent": 0, "parameters": [389]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 5 2終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 1}, {"id": 6, "name": "各モンスターごとのセックス処理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["一般"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["ゴブリン"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["スライム"]}, {"code": 122, "indent": 0, "parameters": [151, 151, 0, 0, 4]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 119, 255, 255], 20, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 119, 255, 255], 20, true]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 15]}, {"code": 108, "indent": 1, "parameters": ["感度倍増になっている場合はダメージを受ける。"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime4\")"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 2, "parameters": [65]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_sex_slime_cum3\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_sex_slime_cum4\")"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime5\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime6\")"]}, {"code": 311, "indent": 2, "parameters": [0, 2, 1, 0, 2, true]}, {"code": 111, "indent": 2, "parameters": [4, 2, 6, 1]}, {"code": 117, "indent": 3, "parameters": [95]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_battle_sex_slime_cum1\")"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_battle_sex_slime_cum2\")"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 3, "parameters": [65]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 181]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["感度倍増になっていない場合は感度倍増になる。"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime1\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime2\")"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Down4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 15]}, {"code": 122, "indent": 1, "parameters": [188, 188, 0, 0, 10]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_sex_slime3\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 343]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 483]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["アンノウン１"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["***********************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["***********************"]}, {"code": 122, "indent": 0, "parameters": [28, 28, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 17]}, {"code": 117, "indent": 0, "parameters": [65]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_swallow\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif1a\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif2a\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif2b\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 28, 0, 3, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3a\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3b\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3c\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 1, "parameters": [28, 28, 1, 0, 1]}, {"code": 117, "indent": 1, "parameters": [65]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3d\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3e\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_swallow_serif3f\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 311, "indent": 1, "parameters": [0, 0, 1, 0, 999, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 1}, {"id": 7, "name": "モンスター敗北エロイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["現在スライム敗北処理は未実装のためタイトル画面に戻ります。"]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["The scene go to the Title because Slime event is not implemented yet."]}, {"code": 354, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 1}, {"id": 8, "name": "戦闘開始時", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　戦闘時の司祭ドット絵グラフィックをイベント化"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 3, 7, 0, 0]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [12, "$invisible_priestess == 0"]}, {"code": 111, "indent": 1, "parameters": [6, -1, 2]}, {"code": 355, "indent": 2, "parameters": ["$direction = 2"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, -1, 4]}, {"code": 355, "indent": 2, "parameters": ["$direction = 4"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, -1, 6]}, {"code": 355, "indent": 2, "parameters": ["$direction = 6"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, -1, 8]}, {"code": 355, "indent": 2, "parameters": ["$direction = 8"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(\"フォロワーのキャラチップを生成　開始\")"]}, {"code": 355, "indent": 1, "parameters": ["let x = $gamePlayer.followers().follower(0).x;"]}, {"code": 655, "indent": 1, "parameters": ["let y = $gamePlayer.followers().follower(0).y;"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["d= $direction"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["add_event = add_event(x, y, 2, 26, d)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["console.log(add_event)"]}, {"code": 655, "indent": 1, "parameters": ["$pixe_pri_id = 0"]}, {"code": 655, "indent": 1, "parameters": ["//$pixel_pri_id = add_event.id"]}, {"code": 355, "indent": 1, "parameters": ["console.log(\"フォロワーのキャラチップを生成　終了\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["今はもう使ってないでござんすよ！"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　習得スキルによって対応アイテムの取得（戦闘でアイテム選択でスキ"]}, {"code": 408, "indent": 0, "parameters": ["ル選択してるため）"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 121, "indent": 0, "parameters": [13, 13, 0]}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 108, "indent": 0, "parameters": ["*激励*"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 3, 31]}, {"code": 126, "indent": 1, "parameters": [201, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*強打*"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 3, 35]}, {"code": 126, "indent": 1, "parameters": [206, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*シールドバッシュ*"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(1).equips()[1] != null"]}, {"code": 108, "indent": 1, "parameters": ["防具でスキル覚えた場合条件分岐のスキル覚えてるかが反映されないの"]}, {"code": 408, "indent": 1, "parameters": ["でこの処理にしてる。"]}, {"code": 126, "indent": 1, "parameters": [207, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["【セカンダリスキル】"]}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["魔法使い"]}, {"code": 108, "indent": 0, "parameters": ["*ファイア・アロー*"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 3, 302]}, {"code": 126, "indent": 1, "parameters": [472, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭"]}, {"code": 408, "indent": 0, "parameters": ["*************************"]}, {"code": 108, "indent": 0, "parameters": ["*激励*"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 3, 101]}, {"code": 126, "indent": 1, "parameters": [271, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*ガニ股ハメ乞いダンス*"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 3, 105]}, {"code": 126, "indent": 1, "parameters": [275, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*ヒール*"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 3, 106]}, {"code": 126, "indent": 1, "parameters": [276, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*プロテクション*"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 3, 120]}, {"code": 126, "indent": 1, "parameters": [290, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　Traitの初期化"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["ファストTraitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_fast = 0"]}, {"code": 108, "indent": 0, "parameters": ["スロウTraitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_slow = 0"]}, {"code": 108, "indent": 0, "parameters": ["通常ダメージ無効Traitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_impervious = 0"]}, {"code": 108, "indent": 0, "parameters": ["ホードTraitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_horde = 0"]}, {"code": 108, "indent": 0, "parameters": ["再生Traitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_regeneration = 0"]}, {"code": 108, "indent": 0, "parameters": ["肉の盾Traitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_meat_shield = 0"]}, {"code": 108, "indent": 0, "parameters": ["豚Traitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_piggy = 0"]}, {"code": 108, "indent": 0, "parameters": ["拘束しながらの攻撃Traitを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_attack_w_bind = 0"]}, {"code": 108, "indent": 0, "parameters": ["ペニス所持判定を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_penis = 0"]}, {"code": 108, "indent": 0, "parameters": ["デスペニス所持判定を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$trait_death_penis = 0"]}, {"code": 108, "indent": 0, "parameters": ["弱点を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$weak_physical = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_magic = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_fire = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_water = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_earth = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_thunder = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_holy = 0"]}, {"code": 355, "indent": 0, "parameters": ["$weak_darkness = 0"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["* スキルやグラフィックタイプの初期化"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 108, "indent": 0, "parameters": ["アタックアニメーションタイプを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$attack_animation_type = 0"]}, {"code": 108, "indent": 0, "parameters": ["エロ敵の拘束グラフィックタイプを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$bind_type = 0"]}, {"code": 108, "indent": 0, "parameters": ["エネミーダメージ強化スイッチを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_charge_power = 0"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["* エネミーステートを初期化"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_stun = 0"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_poison = 0"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_paralysis = 0"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_blind = 0"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_sleep = 0"]}, {"code": 355, "indent": 0, "parameters": ["$enemy_state_temptation = 0"]}, {"code": 108, "indent": 0, "parameters": ["********************************"]}, {"code": 408, "indent": 0, "parameters": ["* プレイヤーステートを初期化"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 108, "indent": 0, "parameters": ["スキッププレイヤーターンを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$skip_player_turn=0"]}, {"code": 108, "indent": 0, "parameters": ["パニック値を初期化"]}, {"code": 122, "indent": 0, "parameters": [181, 182, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["プレイヤーターンスキップを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$player_turn_skip = false"]}, {"code": 108, "indent": 0, "parameters": ["プレイヤープロテクションを初期化"]}, {"code": 355, "indent": 0, "parameters": ["$player_protection = [0,0,0]"]}, {"code": 108, "indent": 0, "parameters": ["司祭がヘルプに行ってるターン数の初期化"]}, {"code": 355, "indent": 0, "parameters": ["$help_turn = 0"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　戦闘中の特殊イベントスイッチの初期化"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["戦闘中のNTR判定を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$ev_ntr_in_combat = 0"]}, {"code": 108, "indent": 0, "parameters": ["救助者判定を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$rescue_in_combat = 0"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 9, "name": "戦闘本処理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 9 1開始\")"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　戦闘開始前（戦闘中一回のみ処理のダイアログや状態変化"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["****"]}, {"code": 408, "indent": 0, "parameters": ["救助者がいる場合の開始処理"]}, {"code": 408, "indent": 0, "parameters": ["****"]}, {"code": 111, "indent": 0, "parameters": [0, 73, 0]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_battle_start_with_survivor\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 9 1終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["*　戦闘開始前（戦闘中一回のみ処理のTrait"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 111, "indent": 0, "parameters": [12, "$trait_fast == 1"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Skill1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"\\\\c[18]Fast Trait!\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_battle_start_trait_fast\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["$skip_player_turn = 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ラウンド開始時のTrait処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["*　Piggy Traitの場合、狂気の叫び声でパニック値上昇"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [12, "$trait_piggy == 1"]}, {"code": 111, "indent": 1, "parameters": [1, 159, 0, 0, 0]}, {"code": 108, "indent": 2, "parameters": ["*発動カウントを-1にすることで2度発生しないようにしてる。"]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_pig_give_birth1\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 250, "indent": 2, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 111, "indent": 2, "parameters": [1, 121, 0, 203, 0]}, {"code": 355, "indent": 3, "parameters": ["$sp3 = screen.pictures[0]"]}, {"code": 655, "indent": 3, "parameters": ["$sp3.show(\"enemy203-0-1\", 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_pig_give_birth2\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_pig_give_birth3\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_pig_give_birth4\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 250, "indent": 2, "parameters": [{"name": "Darkness5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["follower = $gameVariables.value(8)"]}, {"code": 655, "indent": 2, "parameters": ["num = -10"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(1,num)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(2,num)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["if ($gameVariables.value(8) >= 1) {"]}, {"code": 655, "indent": 2, "parameters": ["  tp_heal(follower,num)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_battle_pig_oink1\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_battle_pig_oink2\")"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Darkness5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["follower = $gameVariables.value(8)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(1,-5)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(2,-5)"]}, {"code": 655, "indent": 2, "parameters": ["if ($gameVariables.value(8) >= 1){"]}, {"code": 655, "indent": 2, "parameters": ["  tp_heal(follower,-5)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [159, 159, 2, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ラウンド開始時のプレイヤー側状態以上チェック処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭の状態異常チェック"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 1]}, {"code": 121, "indent": 2, "parameters": [65, 65, 0]}, {"code": 119, "indent": 2, "parameters": ["司祭の状態異常確認終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["司祭の状態異常確認終了"]}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["状態異常チェック"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 108, "indent": 0, "parameters": ["ノックダウン"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor($gameVariables.value(270)).isStateAffected(1);"]}, {"code": 355, "indent": 1, "parameters": ["$skip_turn = 1;"]}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 1, 0]}, {"code": 121, "indent": 2, "parameters": [62, 62, 0]}, {"code": 121, "indent": 2, "parameters": [70, 70, 0]}, {"code": 121, "indent": 2, "parameters": [71, 71, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 2, 0]}, {"code": 121, "indent": 2, "parameters": [65, 65, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 3, 1]}, {"code": 111, "indent": 2, "parameters": [1, 270, 0, 100, 4]}, {"code": 121, "indent": 3, "parameters": [69, 69, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["毒"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor($gameVariables.value(270)).isStateAffected(2);"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"resist_poison\""]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 1, 270]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 212, "indent": 2, "parameters": [-1, 40, false]}, {"code": 313, "indent": 2, "parameters": [1, 270, 1, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_damage_poison\")"]}, {"code": 212, "indent": 2, "parameters": [-1, 50, false]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 4, "$gameActors.actor($gameVariables.value(270)).mhp"]}, {"code": 122, "indent": 2, "parameters": [54, 54, 3, 0, 10]}, {"code": 122, "indent": 2, "parameters": [54, 54, 4, 0, 100]}, {"code": 355, "indent": 2, "parameters": ["var value = $gameVariables.value(54);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(54, <PERSON>.ceil(value));"]}, {"code": 311, "indent": 2, "parameters": [1, 270, 1, 1, 54, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["麻痺"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor($gameVariables.value(270)).isStateAffected(7);"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"resist_paralyze\""]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 1, 270]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 212, "indent": 2, "parameters": [-1, 40, false]}, {"code": 313, "indent": 2, "parameters": [1, 270, 1, 7]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$skip_turn = 1;"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_damage_paralyzed\")"]}, {"code": 212, "indent": 2, "parameters": [-1, 55, false]}, {"code": 111, "indent": 2, "parameters": [1, 270, 0, 1, 0]}, {"code": 121, "indent": 3, "parameters": [62, 62, 0]}, {"code": 121, "indent": 3, "parameters": [70, 70, 0]}, {"code": 121, "indent": 3, "parameters": [71, 71, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 270, 0, 2, 0]}, {"code": 121, "indent": 3, "parameters": [65, 65, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 270, 0, 3, 1]}, {"code": 111, "indent": 3, "parameters": [1, 270, 0, 100, 4]}, {"code": 121, "indent": 4, "parameters": [69, 69, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["睡眠"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor($gameVariables.value(270)).isStateAffected(6);"]}, {"code": 355, "indent": 1, "parameters": ["$skip_turn = 1;"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_damage_sleep\")"]}, {"code": 212, "indent": 1, "parameters": [-1, 54, false]}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 1, 0]}, {"code": 121, "indent": 2, "parameters": [62, 62, 0]}, {"code": 121, "indent": 2, "parameters": [70, 70, 0]}, {"code": 121, "indent": 2, "parameters": [71, 71, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 2, 0]}, {"code": 121, "indent": 2, "parameters": [65, 65, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 270, 0, 3, 1]}, {"code": 111, "indent": 2, "parameters": [1, 270, 0, 100, 4]}, {"code": 121, "indent": 3, "parameters": [69, 69, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [335]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["従者ヘルプ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 122, "indent": 0, "parameters": [41, 41, 0, 3, 7, 0, 0]}, {"code": 122, "indent": 0, "parameters": [42, 42, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [43, 43, 0, 3, 5, -1, 1]}, {"code": 355, "indent": 0, "parameters": ["$position_priestess_x = $gameVariables.value(42)"]}, {"code": 355, "indent": 0, "parameters": ["$position_priestess_y = $gameVariables.value(43)"]}, {"code": 355, "indent": 0, "parameters": ["$direction = 2"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 355, "indent": 1, "parameters": ["$position_priestess_y = $gameVariables.value(43)-2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 355, "indent": 1, "parameters": ["$position_priestess_x = $gameVariables.value(42)+2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 6]}, {"code": 355, "indent": 1, "parameters": ["$position_priestess_x = $gameVariables.value(42)-2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 355, "indent": 1, "parameters": ["$position_priestess_y = $gameVariables.value(43)+2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["変数作成処理"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 355, "indent": 0, "parameters": ["if $help_turn == null"]}, {"code": 655, "indent": 0, "parameters": ["  $help_turn = 0"]}, {"code": 655, "indent": 0, "parameters": ["end"]}, {"code": 355, "indent": 0, "parameters": ["if $ntr_in_combat == null"]}, {"code": 655, "indent": 0, "parameters": ["  $ntr_in_combat = 0"]}, {"code": 655, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["既に救援中の場合は帰ってくるかの処理"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [0, 66, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["ヘルプに出してから１ターン目"]}, {"code": 111, "indent": 1, "parameters": [12, "$help_turn == 1"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 6, 1]}, {"code": 355, "indent": 3, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 111, "indent": 3, "parameters": [0, 35, 1]}, {"code": 355, "indent": 4, "parameters": ["x = $position_priestess_x"]}, {"code": 655, "indent": 4, "parameters": ["y = $position_priestess_y"]}, {"code": 655, "indent": 4, "parameters": ["id = 1"]}, {"code": 655, "indent": 4, "parameters": ["add_event = add_event(x, y, id, 68, 2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 250, "indent": 3, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 95}]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 111, "indent": 3, "parameters": [1, 204, 0, 8, 1]}, {"code": 355, "indent": 4, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif1b\")"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 355, "indent": 4, "parameters": ["$ev_ntr_in_combat = 1"]}, {"code": 355, "indent": 4, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif1a\")"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_vfb\")"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_by\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヘルプに出してから２ターン目"]}, {"code": 111, "indent": 1, "parameters": [12, "$help_turn == 2"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 8, 1]}, {"code": 355, "indent": 3, "parameters": ["$ev_ntr_in_combat = 2"]}, {"code": 355, "indent": 3, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 111, "indent": 3, "parameters": [0, 35, 1]}, {"code": 355, "indent": 4, "parameters": ["x = $position_priestess_x"]}, {"code": 655, "indent": 4, "parameters": ["y = $position_priestess_y"]}, {"code": 655, "indent": 4, "parameters": ["id = 2"]}, {"code": 655, "indent": 4, "parameters": ["add_event = add_event(x, y, id, 68, 2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$ntr_in_combat = 2"]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 3, "parameters": [{"name": "notanomori_suitsuku2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif2\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_vfb\")"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_by\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヘルプに出してから３ターン目"]}, {"code": 111, "indent": 1, "parameters": [12, "$help_turn >= 3"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 10, 1]}, {"code": 355, "indent": 3, "parameters": ["$ev_ntr_in_combat = 3"]}, {"code": 355, "indent": 3, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 111, "indent": 3, "parameters": [0, 35, 1]}, {"code": 355, "indent": 4, "parameters": ["x = $position_priestess_x"]}, {"code": 655, "indent": 4, "parameters": ["y = $position_priestess_y"]}, {"code": 655, "indent": 4, "parameters": ["id = 3"]}, {"code": 655, "indent": 4, "parameters": ["add_event = add_event(x, y, id, 68, 2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$ntr_in_combat = 2"]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 3, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif3\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_vfb\")"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_by\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["***************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭が戻ってくる処理"]}, {"code": 408, "indent": 1, "parameters": ["***************************************************************"]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヘルプ中行ってフェラチオだけした"]}, {"code": 355, "indent": 1, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 111, "indent": 1, "parameters": [12, "$ntr_in_combat == 2"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 8, 1)"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif1b\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif2b\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif3b\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヘルプ中行ってセクハラだけされた"]}, {"code": 111, "indent": 1, "parameters": [12, "$ntr_in_combat == 1"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 8, 1)"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif1a\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif2a\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["ヘルプ中に何も起きていない"]}, {"code": 111, "indent": 1, "parameters": [12, "$ntr_in_combat == 0"]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 8, 1)"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_end_serif\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_pcb\")"]}, {"code": 121, "indent": 1, "parameters": [65, 65, 1]}, {"code": 121, "indent": 1, "parameters": [66, 66, 1]}, {"code": 355, "indent": 1, "parameters": ["$help_turn = 0"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭が行動できない場合はスキップ"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 122, "indent": 0, "parameters": [54, 54, 0, 3, 3, 2, 2]}, {"code": 111, "indent": 0, "parameters": [0, 63, 0]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 0, 2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["従者のHPが既に０の場合もスキップ"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 3, 3, 21, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 0, 2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["ヘルプかかるかの判定"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 100]}, {"code": 111, "indent": 0, "parameters": [1, 20, 1, 290, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Flash1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 8, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_ntr_in_c_serif_yarizo1a\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_ntr_in_c_serif_yarizo1b\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_call\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 10, 1]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 95}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif_p2\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [55]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_p_go1\")"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_p_leave\")"]}, {"code": 355, "indent": 2, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"ntr_in_c_sl1\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"ntr_in_c_sl2\")"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_serif_p1\")"]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_p_leave\")"]}, {"code": 355, "indent": 1, "parameters": ["del_event($pixel_pri_id)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 250, "indent": 1, "parameters": [{"name": "Flash1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 1, "parameters": [55]}, {"code": 121, "indent": 1, "parameters": [66, 66, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 6]}, {"code": 111, "indent": 1, "parameters": [4, 21, 0]}, {"code": 311, "indent": 2, "parameters": [0, 21, 1, 1, 20, true]}, {"code": 122, "indent": 2, "parameters": [54, 54, 0, 3, 3, 21, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["従者のHPが０以下ならダイアログ"]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_ntr_in_c_f_down\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["num = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": ["p \"HP:#{num}\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 111, "indent": 0, "parameters": [0, 66, 0]}, {"code": 355, "indent": 1, "parameters": ["$help_turn += 1"]}, {"code": 121, "indent": 1, "parameters": [65, 65, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$help_turn = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭のターン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭状態チェック"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 22]}, {"code": 111, "indent": 1, "parameters": [0, 63, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_battle_trauma_\\"]}, {"code": 655, "indent": 2, "parameters": ["charm_txt4\")"]}, {"code": 111, "indent": 2, "parameters": [4, 1, 6, 3]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_battle_trauma_\\"]}, {"code": 655, "indent": 3, "parameters": ["charm_txt6\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [155, 155, 0, 0, 275]}, {"code": 117, "indent": 2, "parameters": [37]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭スキル発動チェック"]}, {"code": 408, "indent": 0, "parameters": ["******************************"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(147) != 0"]}, {"code": 118, "indent": 1, "parameters": ["司祭スキル発動"]}, {"code": 355, "indent": 1, "parameters": ["$priestess_using_skill = 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [155, 155, 0, 1, 147]}, {"code": 117, "indent": 1, "parameters": [37]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 2]}, {"code": 111, "indent": 1, "parameters": [12, "$hazukashii == 1"]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [147, 147, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 3}, {"id": 10, "name": "[戦闘後閲覧]戦闘中にセクハラされた場合", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["戦闘中に司祭がセクハラもしくは寝取られックスしとる時"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(61, \"select_yes\")"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(62, \"select_no\")"]}, {"code": 111, "indent": 0, "parameters": [12, "$ev_ntr_in_combat == 3"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [195]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-3"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5-6"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 40, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["********************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が３"]}, {"code": 408, "indent": 0, "parameters": ["********************"]}, {"code": 111, "indent": 0, "parameters": [12, "$ev_ntr_in_combat == 1"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(53, 10, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が４"]}, {"code": 408, "indent": 0, "parameters": ["********************"]}, {"code": 111, "indent": 0, "parameters": [12, "$ev_ntr_in_combat == 2"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(53, 10, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が５（MAX)"]}, {"code": 408, "indent": 0, "parameters": ["********************"]}, {"code": 111, "indent": 0, "parameters": [12, "$ev_ntr_in_combat == 3"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(53, 10, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["$ev_ntr_in_combat = 0"]}, {"code": 122, "indent": 0, "parameters": [23, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 40, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が３のときのイベント"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["セクハラCG"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_4-12"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が４のときのイベント"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["グラフィック準備"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 108, "indent": 0, "parameters": ["セクハラCG"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-6"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_7-21"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [150]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22-29"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [100, "protagonist-in_fight_blur", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-31"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_32-56"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_57-78"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_79-88"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["関係性が５のときのイベント"]}, {"code": 408, "indent": 0, "parameters": ["*******************************************************"]}, {"code": 108, "indent": 0, "parameters": ["グラフィック準備"]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 0, "parameters": ["セクハラCG"]}, {"code": 250, "indent": 0, "parameters": [{"name": "kuchu1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-9"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 231, "indent": 0, "parameters": [100, "protagonist-in_fight_blur", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-11"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_12-14"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_15-35"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_36-44"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 231, "indent": 0, "parameters": [100, "protagonist-in_fight_blur", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_46-63"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_64-68"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_69-78"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "piss_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_79-80"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_81-84"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_85-87"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_88"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 11}, {"id": 11, "name": "従者死亡判定", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["難易度がハード以上なら従者は永久にロストする"]}, {"code": 111, "indent": 0, "parameters": [4, 21, 0]}, {"code": 111, "indent": 1, "parameters": [4, 21, 6, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(53, 11, 2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["※ヤリゾー"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [0, 502, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-4"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Down4", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5"]}, {"code": 121, "indent": 1, "parameters": [503, 503, 0]}, {"code": 313, "indent": 1, "parameters": [0, 21, 1, 1]}, {"code": 129, "indent": 1, "parameters": [21, 1, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_6-7"]}, {"code": 313, "indent": 1, "parameters": [0, 21, 1, 1]}, {"code": 311, "indent": 1, "parameters": [0, 21, 0, 0, 9999, false]}, {"code": 312, "indent": 1, "parameters": [0, 21, 0, 0, 9999]}, {"code": 129, "indent": 1, "parameters": [21, 1, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 3}, {"id": 12, "name": "プレイヤーターン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 12 1開始\")"]}, {"code": 108, "indent": 0, "parameters": ["プレイヤー側アタックデータ代入"]}, {"code": 355, "indent": 0, "parameters": ["$atk_name = $gameActors.actor(1).name();"]}, {"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["盾による防御を初期化"]}, {"code": 355, "indent": 0, "parameters": ["$guard_w_shield = 0"]}, {"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["物理攻撃時"]}, {"code": 355, "indent": 0, "parameters": ["$power = $gameVariables.value(106)"]}, {"code": 655, "indent": 0, "parameters": ["$pow_dice = $gameVariables.value(142)"]}, {"code": 355, "indent": 0, "parameters": ["$tar_def = $gameVariables.value(127)"]}, {"code": 655, "indent": 0, "parameters": ["$def_dice = $gameVariables.value(143)"]}, {"code": 355, "indent": 0, "parameters": ["$armor = $gameVariables.value(135)"]}, {"code": 122, "indent": 0, "parameters": [116, 116, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [118, 118, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["魔法攻撃時"]}, {"code": 355, "indent": 0, "parameters": ["$mpower = $gameVariables.value(108)"]}, {"code": 355, "indent": 0, "parameters": ["$tar_mdef = $gameVariables.value(128)"]}, {"code": 108, "indent": 0, "parameters": ["**************************"]}, {"code": 408, "indent": 0, "parameters": ["ディスプレイ用データ代入"]}, {"code": 122, "indent": 0, "parameters": [155, 155, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [153, 153, 0, 4, "$gameActors.actor(1).name();"]}, {"code": 122, "indent": 0, "parameters": [154, 154, 0, 1, 139]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 12 1終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 3}, {"id": 13, "name": "エネミーターン", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["コモン368へ移動"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["コモン367へ移動"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 13 3\")"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　ターン開始時に拘束中の処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [271, 271, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [375]}, {"code": 117, "indent": 0, "parameters": [335]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 13 4 エネミー行動判定開始\")"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　エネミー行動判定"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 108, "indent": 0, "parameters": ["肉の盾Traitの場合の処理"]}, {"code": 111, "indent": 0, "parameters": [12, "$trait_meat_shield == 1"]}, {"code": 111, "indent": 1, "parameters": [12, "$meat_shield_victim == 1"]}, {"code": 119, "indent": 2, "parameters": ["通常攻撃レンジ"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["オーガが肉の盾を失った場合"]}, {"code": 111, "indent": 0, "parameters": [1, 121, 0, 221, 0]}, {"code": 111, "indent": 1, "parameters": [12, "$meat_shield_victim == 0"]}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_battle_rampage\")"]}, {"code": 122, "indent": 2, "parameters": [140, 140, 0, 2, 9, 10]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*攻撃者がエネミー側でダメージ2倍スキルを使用中は"]}, {"code": 408, "indent": 0, "parameters": ["１００％通常攻撃をしてくる。"]}, {"code": 111, "indent": 0, "parameters": [12, "$atk_name != $gameActors.actor(1).name()"]}, {"code": 111, "indent": 1, "parameters": [12, "$enemy_charge_power >= 1"]}, {"code": 122, "indent": 2, "parameters": [140, 140, 0, 0, 1]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["パーティメンバー誰かがノックダウン中の場合、"]}, {"code": 408, "indent": 0, "parameters": ["エロ攻撃所持の敵はそいつをターゲッティングし、エロ攻撃を実行"]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["（実行処理一旦削除して封印している）"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.members().every(member => member.isStateAffected(1));"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["v[158]以下:通常攻撃"]}, {"code": 408, "indent": 0, "parameters": ["v[158]超え:特殊攻撃"]}, {"code": 408, "indent": 0, "parameters": ["10:エロ攻撃（拘束）"]}, {"code": 108, "indent": 0, "parameters": ["****************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["抽選開始"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************"]}, {"code": 118, "indent": 0, "parameters": ["行動抽選"]}, {"code": 118, "indent": 0, "parameters": ["通常攻撃レンジ"]}, {"code": 122, "indent": 0, "parameters": [140, 140, 0, 2, 1, 10]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"敵の行動:\" + $gameVariables.value(140));"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 13 4 エネミー行動判定終了\")"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　エネミーターン終了時の処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ステートの減算と解除"]}, {"code": 408, "indent": 0, "parameters": ["***********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["エネミーターンスタート時のステート処理（コモン368）で一括管理することに"]}, {"code": 111, "indent": 0, "parameters": [0, 620, 0]}, {"code": 111, "indent": 1, "parameters": [12, "$enemy_state_paralysis >= 1"]}, {"code": 355, "indent": 2, "parameters": ["$enemy_state_paralysis -= 1"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["if ($enemy_state_paralysis < 0){"]}, {"code": 655, "indent": 2, "parameters": ["  $enemy_state_paralysis = 0"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["if ($enemy_state_paralysis <= 0){"]}, {"code": 655, "indent": 2, "parameters": ["  set_mlog(\"_log_database_text_cure_paralyze\")"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$enemy_state_sleep >= 1"]}, {"code": 355, "indent": 2, "parameters": ["$enemy_state_sleep -= 1"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["if ($enemy_state_sleep < 0){"]}, {"code": 655, "indent": 2, "parameters": ["  $enemy_state_sleep = 0"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["if ($enemy_state_sleep <= 0){"]}, {"code": 655, "indent": 2, "parameters": ["  set_mlog(\"_log_database_text_cure_sleep\")"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["console.log(\"睡眠状態残りターン数：\" + $enemy_state_sleep);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 3}, {"id": 14, "name": "エネミーセックス固有イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["オーガ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["ループアニメ　早まる"]}, {"code": 122, "indent": 0, "parameters": [52, 52, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [174, 174, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [175, 175, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [703]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 122, "indent": 0, "parameters": [52, 52, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [174, 174, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [175, 175, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [703]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-8"]}, {"code": 122, "indent": 0, "parameters": [52, 52, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [174, 174, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [175, 175, 0, 0, 4]}, {"code": 108, "indent": 0, "parameters": ["アニメコモン内に射精フラッシュいれたらバグるときある"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 117, "indent": 0, "parameters": [703]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9-14"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 108, "indent": 0, "parameters": ["ループアニメ　終了"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 255], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 231, "indent": 0, "parameters": [100, "blood", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 311, "indent": 0, "parameters": [0, 1, 1, 0, 9999, true]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [27, 27, 1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 3}, {"id": 15, "name": "エネミーグラフィック変更", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [1, 121, 0, 221, 0]}, {"code": 355, "indent": 1, "parameters": ["var num = $gameVariables.value($meat_shield_victim); "]}, {"code": 655, "indent": 1, "parameters": ["var pic = `enemy251-${num}`;"]}, {"code": 655, "indent": 1, "parameters": ["console.log(num);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["var $sp3 = $gameScreen;"]}, {"code": 655, "indent": 1, "parameters": ["$sp3.showPicture(1, pic, 0, 0, 0, $s_w, $s_h, 255, 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 3}, {"id": 16, "name": "ヤリゾーのセクハラ１[ターン]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 16, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["浸食度増加処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_battle_follower_refuse\")"]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["その１　ケツアップ見て欲情"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sounds_from_back\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [55]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [61]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["その２　司祭の尻に精子をかける"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sounds_from_back\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [55]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [61]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 121, "indent": 0, "parameters": [179, 179, 0]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"尻\""]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 117, "indent": 0, "parameters": [61]}, {"code": 355, "indent": 0, "parameters": ["$log_window_end = 1"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 411]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 121, "indent": 0, "parameters": [179, 179, 1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-3"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["その３　いきなりケツ叩く"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_sounds_from_back\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [55]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 355, "indent": 0, "parameters": ["$no = \"#{$e_name}-yarizo\""]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer3]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_h,$s_w,255,0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 5}, {"id": 17, "name": "ヤリゾーのセクハラ２[ターン]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が２の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 108, "indent": 0, "parameters": ["！今のところレベル１のを呼び出してる"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 16, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["浸食度増加処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_battle_follower_refuse\")"]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 5}, {"id": 18, "name": "ヤリゾーのセクハラ３[ターン]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が３の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"---ヤリゾーターンセクハラLvl3開始---\")"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 111, "indent": 0, "parameters": [1, 186, 0, 100, 1]}, {"code": 108, "indent": 1, "parameters": ["司祭の興奮が100の場合はセックスに突入"]}, {"code": 408, "indent": 1, "parameters": ["10-12"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 10, 12]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["司祭の興奮が100未満ならセクハラ"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 2, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)"]}, {"code": 655, "indent": 0, "parameters": ["console.log(`ヤリセク番号${x}`)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 18, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["浸食度増加処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_battle_follower_refuse\")"]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　ちんぽ目隠し"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 6, 1)"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 2, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 2, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　擦り付け"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 13, 1)"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 117, "indent": 0, "parameters": [984]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 3, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 3, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　ケツの穴を広げられる"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 117, "indent": 0, "parameters": [984]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 4, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 4, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 4, 3, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 311, "indent": 0, "parameters": [0, 2, 1, 0, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ（司祭興奮１００）　種付けぷれす"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["弱ピストン"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 3, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 2, 1)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["ピストン待機"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 2, 2)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["強ピストン　射精"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 2, 3)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["値偏向"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,-50)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ（司祭興奮１００）　Ｉ字へこへこ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["弱ピストン"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 3, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 3, 1)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["ピストン待機"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 3, 2)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["強ピストン　射精"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 10, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(91, 3, 3)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["値偏向"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,-50)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ（司祭興奮１００）　乳揉みセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン１"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 12, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 12, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 13, 1)"]}, {"code": 117, "indent": 0, "parameters": [984]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン２"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 12, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 12, 11, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 10]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 13, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["シーン３"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 18, 12, 20, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 13, 1)"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["値偏向"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(2,-50)"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -50]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 5}, {"id": 19, "name": "ターンセクハラ４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　関係性が３の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["console.log(\"---ヤリゾーターンセクハラLvl4開始---\")"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 111, "indent": 0, "parameters": [1, 186, 0, 100, 1]}, {"code": 108, "indent": 1, "parameters": ["司祭の興奮が100の場合はセックスに突入"]}, {"code": 408, "indent": 1, "parameters": ["10-12"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 2, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["司祭の興奮が100未満ならセクハラ"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 2, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)"]}, {"code": 655, "indent": 0, "parameters": ["console.log(`ヤリセク番号${x}`)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 19, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["浸食度増加処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_battle_follower_refuse\")"]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　ちんぽ目隠し"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 6, 1)"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 19, 2, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 19, 2, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 311, "indent": 0, "parameters": [0, 2, 0, 0, 1, false]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー戦闘中のセクハラ　ちんぽ見せつけ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 117, "indent": 0, "parameters": [984]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 2, 1)"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 19, 3, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 19, 3, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 311, "indent": 0, "parameters": [0, 2, 0, 0, 1, false]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 5}, {"id": 20, "name": "ターンセクハラ５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 5}, {"id": 21, "name": "ヤリゾーのセクハラ[司祭詠唱中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 21, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_battle_cant_chant\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [334]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 108, "indent": 0, "parameters": ["淫欲が一定値以下であれば淫欲の上昇"]}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 30, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["浸食度の上昇"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["その１　まんこ臭嗅ぐ　（スカート装備中はスカートめくる）"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 211]}, {"code": 111, "indent": 0, "parameters": [4, 2, 5, 82]}, {"code": 122, "indent": 1, "parameters": [29, 29, 0, 0, 2]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip2", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["その３　詠唱中の司祭のケツを揉む"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 7, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 7}, {"id": 22, "name": "ヤリゾーのセクハラ[司祭詠唱中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が２の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 21, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_battle_cant_chant\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [334]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 108, "indent": 0, "parameters": ["淫欲が一定値以下であれば淫欲の上昇"]}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 30, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["浸食度の上昇"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 7}, {"id": 23, "name": "ヤリゾーのセクハラ[司祭詠唱中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が３の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 2]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 21, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_battle_cant_chant\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [334]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 108, "indent": 0, "parameters": ["淫欲が一定値以下であれば淫欲の上昇"]}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 30, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["浸食度の上昇"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 7}, {"id": 24, "name": "詠唱セクハラ４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が４の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 1, 1]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 24, x)"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [29, 29, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_battle_cant_chant\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [23, 28, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [334]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 108, "indent": 0, "parameters": ["淫欲が一定値以下であれば淫欲の上昇"]}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 30, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["浸食度の上昇"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ケツまんフィスト"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 2101]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 117, "indent": 0, "parameters": [984]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 24, 2, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 24, 2, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 24, 2, 3, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 24, 2, 4, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1616, 1616, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 24, 2, 5, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 7}, {"id": 25, "name": "詠唱セクハラ５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 7}, {"id": 26, "name": "ヤリゾーのセクハラ[司祭ダウン中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭詠唱中のセクハラ　関係性が１の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 170], 30, true]}, {"code": 117, "indent": 0, "parameters": [55]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 1]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了処理"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [387]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [690]}, {"code": 122, "indent": 0, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [23, 23, 1]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 9}, {"id": 27, "name": "ヤリゾーのセクハラ[司祭ダウン中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭ダウン中のセクハラ　関係性が２の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["MapEvent.call(53, 26, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 9}, {"id": 28, "name": "ヤリゾーのセクハラ[司祭ダウン中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭ダウン中のセクハラ　関係性が３の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 53, 28, 1, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 53, 28, 1, 3, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [384, 384, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": ["ChoiceVariableId 381"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["ダメージを食らってセクハラを阻む"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [153, 153, 0, 1, 139]}, {"code": 122, "indent": 1, "parameters": [270, 270, 0, 1, 121]}, {"code": 122, "indent": 1, "parameters": [271, 271, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [36]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理開始"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1682]}, {"code": 355, "indent": 1, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(53, 28, x)"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [387]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [690]}, {"code": 122, "indent": 1, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [23, 23, 1]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 122, "indent": 0, "parameters": [384, 384, 0, 0, 3]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ノックダウンしてる司祭の喉にちんぽ突っ込む"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 111, "indent": 0, "parameters": [0, 35, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["シークレットＮＴＲモードがオンの場合は非表示"]}, {"code": 121, "indent": 1, "parameters": [23, 23, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理"]}, {"code": 355, "indent": 1, "parameters": ["$xray = 1"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※射精　司祭頬をふくらんで白目　鼻から精子溢れる"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 355, "indent": 1, "parameters": ["$xray = 0"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 11]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 3]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [222]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-4"]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ノックダウンしてる司祭のおまんこにちんぽ突っ込む"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 111, "indent": 0, "parameters": [0, 35, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["シークレットＮＴＲモードがオンの場合は非表示"]}, {"code": 121, "indent": 1, "parameters": [23, 23, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [225]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 108, "indent": 1, "parameters": ["**************************"]}, {"code": 408, "indent": 1, "parameters": ["※射精　司祭頬をふくらんで白目　鼻から精子溢れる"]}, {"code": 408, "indent": 1, "parameters": ["**************************"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [24, 24, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [225]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3-4"]}, {"code": 117, "indent": 0, "parameters": [212]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ノックダウンしてる司祭のおまんこにちんぽ突っ込む"]}, {"code": 408, "indent": 0, "parameters": ["（リフトアップ）"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["シークレットＮＴＲモードがオンの場合は非表示"]}, {"code": 121, "indent": 1, "parameters": [23, 23, 0]}, {"code": 117, "indent": 1, "parameters": [55]}, {"code": 117, "indent": 1, "parameters": [56]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理"]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 5]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 9}, {"id": 29, "name": "ヤリゾーのセクハラ[司祭ダウン中]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー司祭ダウン中のセクハラ　関係性が４の時"]}, {"code": 408, "indent": 0, "parameters": ["(Root)"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": ["導入"]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 53, 28, 1, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 53, 28, 1, 3, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 122, "indent": 0, "parameters": [384, 384, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": ["ChoiceVariableId 381"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["ダメージを食らってセクハラを阻む"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [153, 153, 0, 1, 139]}, {"code": 122, "indent": 1, "parameters": [270, 270, 0, 1, 121]}, {"code": 122, "indent": 1, "parameters": [271, 271, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [36]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理開始"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20"]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 1]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 1, 1682]}, {"code": 355, "indent": 1, "parameters": ["x= $gameVariables.value(20)+1"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["MapEvent.call(53, 29, x)"]}, {"code": 108, "indent": 1, "parameters": ["**************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了処理"]}, {"code": 408, "indent": 1, "parameters": ["**************************************"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 117, "indent": 1, "parameters": [387]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [690]}, {"code": 122, "indent": 1, "parameters": [1602, 1604, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [23, 23, 1]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["$skip_turn = 1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 122, "indent": 0, "parameters": [384, 384, 0, 0, 3]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["腕つかみぶいんドギー"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1683]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 250, "indent": 0, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 0]}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["シークレットＮＴＲモードがオンの場合は非表示"]}, {"code": 121, "indent": 1, "parameters": [23, 23, 0]}, {"code": 117, "indent": 1, "parameters": [55]}, {"code": 117, "indent": 1, "parameters": [56]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["********************************"]}, {"code": 408, "indent": 1, "parameters": ["実処理"]}, {"code": 117, "indent": 1, "parameters": [984]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$womb = 0"]}, {"code": 655, "indent": 1, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 1, "parameters": [66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "piston_H_fast", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 29, 2, 1, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 29, 2, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$womb = 3"]}, {"code": 655, "indent": 0, "parameters": ["$guy_type = 1"]}, {"code": 117, "indent": 0, "parameters": [66]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 29, 2, 3, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(54, 53, 29, 2, 4, 0)"]}, {"code": 355, "indent": 0, "parameters": ["text = $gameVariables.value(54)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(`${text}`)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 9}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 9}, {"id": 31, "name": "ヤリゾー肉の盾", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["ダメージ前"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 1, 0]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 111, "indent": 2, "parameters": [4, 21, 0]}, {"code": 111, "indent": 3, "parameters": [4, 2, 6, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [4, 21, 6, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [0, 65, 1]}, {"code": 111, "indent": 6, "parameters": [0, 69, 1]}, {"code": 355, "indent": 7, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 111, "indent": 7, "parameters": [1, 204, 0, 2, 2]}, {"code": 122, "indent": 8, "parameters": [20, 20, 0, 2, 1, 10]}, {"code": 111, "indent": 8, "parameters": [1, 20, 0, 8, 1]}, {"code": 122, "indent": 9, "parameters": [30, 30, 0, 0, 0]}, {"code": 355, "indent": 9, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 9, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 9, "parameters": ["_event_data_base_text_1-2"]}, {"code": 250, "indent": 9, "parameters": [{"name": "Blow1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 9, "parameters": [56]}, {"code": 355, "indent": 9, "parameters": ["$e_name = \"yarizo-nikutate\""]}, {"code": 655, "indent": 9, "parameters": ["$no = `${$e_name}`"]}, {"code": 655, "indent": 9, "parameters": [""]}, {"code": 655, "indent": 9, "parameters": ["$picture = screen.pictures[89]"]}, {"code": 655, "indent": 9, "parameters": ["$picture.show($no,0,0,0,$s_w,$s_h,255,0)"]}, {"code": 101, "indent": 9, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 9, "parameters": ["_event_data_base_text_3"]}, {"code": 122, "indent": 9, "parameters": [271, 271, 0, 0, 21]}, {"code": 355, "indent": 9, "parameters": ["$gameVariables.setValue(154, $dataActors[21].name);"]}, {"code": 355, "indent": 9, "parameters": ["$yarizo_nikutate = 1"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 355, "indent": 7, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["ダメージ後"]}, {"code": 111, "indent": 0, "parameters": [12, "$yarizo_nikutate == 1"]}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 235, "indent": 1, "parameters": [89]}, {"code": 117, "indent": 1, "parameters": [387]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2-3"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [334]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["$yarizo_nikutate = 0"]}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 11}, {"id": 32, "name": "従者用拘束＆セックス", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"コモン53 32 1\")"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　敵ターン開始時に味方が拘束中の処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 0, "parameters": [271, 271, 0, 1, 8]}, {"code": 117, "indent": 0, "parameters": [375]}, {"code": 117, "indent": 0, "parameters": [335]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 3}, {"id": 33, "name": "スキル", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["激励"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 270, 0, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["司祭がパーティに居ない場合はスキップする"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 10, 0, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_failed\")"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["本処理"]}, {"code": 122, "indent": 1, "parameters": [19, 19, 0, 1, 83]}, {"code": 122, "indent": 1, "parameters": [83, 83, 2, 1, 82]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 100]}, {"code": 355, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_chant_skill201a\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 108, "indent": 1, "parameters": ["***"]}, {"code": 408, "indent": 1, "parameters": ["精神系のデバフになってないときの処理"]}, {"code": 408, "indent": 1, "parameters": ["***"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 21]}, {"code": 118, "indent": 2, "parameters": ["司祭の精神デバフ解除"]}, {"code": 108, "indent": 2, "parameters": ["***"]}, {"code": 408, "indent": 2, "parameters": ["精神系のデバフになってるときはここから処理"]}, {"code": 408, "indent": 2, "parameters": ["***"]}, {"code": 111, "indent": 2, "parameters": [1, 20, 1, 19, 2]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_success\")"]}, {"code": 355, "indent": 3, "parameters": ["overwrite_char(-2)"]}, {"code": 212, "indent": 3, "parameters": [-1, 40, true]}, {"code": 111, "indent": 3, "parameters": [4, 2, 6, 22]}, {"code": 224, "indent": 4, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 313, "indent": 4, "parameters": [0, 2, 1, 22]}, {"code": 122, "indent": 4, "parameters": [23, 23, 0, 0, 5]}, {"code": 355, "indent": 4, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 4, "parameters": ["(\"_log_database_text_chant_skill201b_charm\")"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 313, "indent": 4, "parameters": [0, 2, 1, 21]}, {"code": 122, "indent": 4, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 4, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 4, "parameters": ["(\"_log_database_text_chant_skill201b\")"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 63, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [0, 64, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 108, "indent": 5, "parameters": ["精神異常から回復した際に拘束もレイプもされていなければ"]}, {"code": 408, "indent": 5, "parameters": ["立ち絵が通常通りに戻る。"]}, {"code": 121, "indent": 5, "parameters": [65, 65, 1]}, {"code": 117, "indent": 5, "parameters": [55]}, {"code": 122, "indent": 5, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 5, "parameters": [60]}, {"code": 231, "indent": 5, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 355, "indent": 5, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 5, "parameters": ["(\"_log_database_text_chant_skill201c\")"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_failed\")"]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_chant_skill201d\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [4, 2, 6, 12]}, {"code": 119, "indent": 3, "parameters": ["司祭の精神デバフ解除"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 63, 1]}, {"code": 111, "indent": 4, "parameters": [0, 64, 1]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 8]}, {"code": 122, "indent": 5, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 5, "parameters": [60]}, {"code": 355, "indent": 5, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 5, "parameters": ["(\"_log_database_text_chant_skill201e\")"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 5, "parameters": [""]}, {"code": 121, "indent": 5, "parameters": [65, 65, 1]}, {"code": 122, "indent": 5, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 5, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 5, "parameters": [60]}, {"code": 355, "indent": 5, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 5, "parameters": ["(\"_log_database_text_chant_skill201f\")"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 355, "indent": 4, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 4, "parameters": ["(\"_log_database_text_chant_skill201z\")"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 4, "parameters": [""]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["司祭"]}, {"code": 111, "indent": 0, "parameters": [1, 270, 0, 2, 0]}, {"code": 108, "indent": 1, "parameters": ["司祭がパーティに居ない場合はスキップする"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 10, 0, 1, 0]}, {"code": 355, "indent": 3, "parameters": ["set_mlog(\"_log_database_text_failed\")"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["本処理"]}, {"code": 122, "indent": 1, "parameters": [147, 147, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 11]}, {"code": 111, "indent": 1, "parameters": [0, 222, 0]}, {"code": 111, "indent": 2, "parameters": [1, 82, 0, 50, 1]}, {"code": 117, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_skill_encourage_pri_lewd1\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 122, "indent": 3, "parameters": [23, 23, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_skill_encourage_pri_normal1\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog(\"_log_database_text_skill_encourage_pri_tutorial\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 111, "indent": 1, "parameters": [0, 75, 0]}, {"code": 122, "indent": 2, "parameters": [20, 20, 0, 2, 1, 200]}, {"code": 111, "indent": 2, "parameters": [1, 20, 1, 83, 2]}, {"code": 212, "indent": 3, "parameters": [-1, 40, false]}, {"code": 121, "indent": 3, "parameters": [62, 62, 1]}, {"code": 121, "indent": 3, "parameters": [70, 70, 0]}, {"code": 121, "indent": 3, "parameters": [71, 71, 0]}, {"code": 111, "indent": 3, "parameters": [4, 1, 6, 5]}, {"code": 313, "indent": 4, "parameters": [0, 1, 1, 5]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [4, 1, 6, 6]}, {"code": 313, "indent": 4, "parameters": [0, 1, 1, 6]}, {"code": 205, "indent": 4, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_chant_skill271b\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 3, "parameters": ["(\"_log_database_text_chant_skill271c\")"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 8]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill271d\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill271e\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ガニ股ダンス"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 12]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [63]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 22]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_temp\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_lewd\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_normal\")"]}, {"code": 355, "indent": 2, "parameters": ["$hazukashii = 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_chant_skill275\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [0, 61, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(171, \"hantei_sukebe_dance\")"]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [161, 161, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [162, 162, 0, 0, 6]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [163, 163, 0, 1, 128]}, {"code": 122, "indent": 1, "parameters": [164, 164, 1, 1, 129]}, {"code": 117, "indent": 1, "parameters": [13]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 111, "indent": 2, "parameters": [0, 620, 0]}, {"code": 108, "indent": 3, "parameters": ["敵が魅了ステートになる変数"]}, {"code": 408, "indent": 3, "parameters": ["ガニ股ダンスの仕様がシンプルにちんイラトークン上昇に変わったので"]}, {"code": 408, "indent": 3, "parameters": ["封印"]}, {"code": 355, "indent": 3, "parameters": ["$enemy_state_temptation = 1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_success\")"]}, {"code": 213, "indent": 2, "parameters": [0, 4, false]}, {"code": 355, "indent": 2, "parameters": ["num = $gameActors.actor(2).param(7)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$chinira += num"]}, {"code": 655, "indent": 2, "parameters": ["if ($chinira >= 10){"]}, {"code": 655, "indent": 2, "parameters": [" $chinira= 10"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 117, "indent": 2, "parameters": [389]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 50, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [387]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["靴を脱ぐ"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 10, 4)"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_chant_skill181\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [0, 61, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(171, \"hantei_temptation\")"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"temptation\""]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 1, 8]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_success\")"]}, {"code": 213, "indent": 2, "parameters": [0, 4, false]}, {"code": 355, "indent": 2, "parameters": ["id = $gameVariables.value(8)"]}, {"code": 655, "indent": 2, "parameters": ["num = $gameActors.actor(id).param(7);"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$chinira += num"]}, {"code": 655, "indent": 2, "parameters": ["if ($chinira >= 10){"]}, {"code": 655, "indent": 2, "parameters": [" $chinira= 10"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 117, "indent": 2, "parameters": [389]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [387]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ドスケベダンス"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 22]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 117, "indent": 0, "parameters": [63]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 22]}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_temp\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 1]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_lewd\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_normal\")"]}, {"code": 355, "indent": 2, "parameters": ["$hazukashii = 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_chant_skill275\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [0, 61, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(171, \"hantei_sukebe_dance\")"]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [161, 161, 0, 0, 5]}, {"code": 122, "indent": 1, "parameters": [162, 162, 0, 0, 6]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [163, 163, 0, 1, 128]}, {"code": 122, "indent": 1, "parameters": [164, 164, 0, 1, 129]}, {"code": 117, "indent": 1, "parameters": [13]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 111, "indent": 2, "parameters": [0, 620, 0]}, {"code": 108, "indent": 3, "parameters": ["敵が魅了ステートになる変数"]}, {"code": 408, "indent": 3, "parameters": ["ガニ股ダンスの仕様がシンプルにちんイラトークン上昇に変わったので"]}, {"code": 408, "indent": 3, "parameters": ["封印"]}, {"code": 355, "indent": 3, "parameters": ["$enemy_state_temptation = 1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_success\")"]}, {"code": 213, "indent": 2, "parameters": [0, 4, false]}, {"code": 355, "indent": 2, "parameters": ["num = $gameActors.actor(2).param(7)"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$chinira += num"]}, {"code": 655, "indent": 2, "parameters": ["if ($chinira >= 10){"]}, {"code": 655, "indent": 2, "parameters": [" $chinira= 10"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 117, "indent": 2, "parameters": [389]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 82, 0, 50, 4]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [387]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["****************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ハメ乞いポーズ（吸血姫）"]}, {"code": 408, "indent": 0, "parameters": ["****************************************************"]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 19, 4)"]}, {"code": 355, "indent": 0, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 0, "parameters": ["(\"_log_database_text_chant_skill255\")"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 111, "indent": 0, "parameters": [0, 61, 0]}, {"code": 355, "indent": 1, "parameters": ["val_in_database(171, \"hantei_temptation\")"]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"temptation\""]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 1, 8]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 250, "indent": 2, "parameters": [{"name": "se_ma<PERSON><PERSON><PERSON><PERSON>_se_heartbeat01", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_success\")"]}, {"code": 213, "indent": 2, "parameters": [0, 4, false]}, {"code": 355, "indent": 2, "parameters": ["id = $gameVariables.value(8)"]}, {"code": 655, "indent": 2, "parameters": ["num = $gameActors.actor(id).param(7);"]}, {"code": 655, "indent": 2, "parameters": [""]}, {"code": 655, "indent": 2, "parameters": ["$chinira += num"]}, {"code": 655, "indent": 2, "parameters": ["if ($chinira >= 10){"]}, {"code": 655, "indent": 2, "parameters": [" $chinira= 10"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 117, "indent": 2, "parameters": [389]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 2, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["set_mlog\\"]}, {"code": 655, "indent": 1, "parameters": ["(\"_log_database_text_chant_skill275_failed\")"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 122, "indent": 0, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [26, 26, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [387]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 11}, {"id": 34, "name": "!保険", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["console.log(\"マップ53 13 3\")"]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*　ターン開始時に拘束中の処理"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [271, 271, 0, 0, 2]}, {"code": 111, "indent": 0, "parameters": [0, 64, 0]}, {"code": 108, "indent": 1, "parameters": ["**"]}, {"code": 408, "indent": 1, "parameters": ["おめこ中ならおめこ処理"]}, {"code": 117, "indent": 1, "parameters": [375]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["console.log(\"拘束中の処理\")"]}, {"code": 108, "indent": 1, "parameters": ["**"]}, {"code": 408, "indent": 1, "parameters": ["じゃない場合はおめこするまでのターンカウント"]}, {"code": 122, "indent": 1, "parameters": [151, 151, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 2, -40, -20]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 355, "indent": 1, "parameters": ["id = $gameVariables.value(271)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(54)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["tp_heal(id, num)"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$fatigue = $gameVariables.value(271)"]}, {"code": 117, "indent": 1, "parameters": [335]}, {"code": 111, "indent": 1, "parameters": [12, "$gameActors.actor($fatigue).tp == 100;"]}, {"code": 117, "indent": 2, "parameters": [375]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [372]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 12}, {"id": 35, "name": "ヤリゾーのセクハラ１[戦闘終了後]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー　戦闘終了後のイベント"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["戦闘終了後にヤリゾーが毒だった場合"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["**************************************"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [72, 76, 0, 0, 0]}, {"code": 111, "indent": 0, "parameters": [4, 2, 3, 121]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 3, 3, 2, 3]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 1, 1]}, {"code": 122, "indent": 2, "parameters": [72, 72, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [8, 4]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["replaceCharacter -3"]}, {"code": 212, "indent": 0, "parameters": [-1, 50, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 111, "indent": 0, "parameters": [0, 32, 0]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 4, 0]}, {"code": 119, "indent": 2, "parameters": ["イベントレベル４"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 5, 0]}, {"code": 119, "indent": 2, "parameters": ["イベントレベル５"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 53, 35, 2, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, 53, 35, 2, 20, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, 53, 35, 2, 30, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(64, 53, 35, 2, 40, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[72]>=1)", "\\v[62] en(v[73]>=1)", "\\v[63] if(s[133])", "\\v[64]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[72]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["奇跡を使う"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 212, "indent": 1, "parameters": [-1, 40, true]}, {"code": 313, "indent": 1, "parameters": [0, 21, 1, 2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[73]>=1)"]}, {"code": 108, "indent": 1, "parameters": ["毒消し草を使う"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 212, "indent": 1, "parameters": [-1, 40, true]}, {"code": 313, "indent": 1, "parameters": [0, 21, 1, 2]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] if(s[133])"]}, {"code": 108, "indent": 1, "parameters": ["フェラチオさせる"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 2, 2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_31-32"]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["イベントレベル３"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\v[64]"]}, {"code": 108, "indent": 1, "parameters": ["ほっとこう"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_41-42"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントレベル３"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["イベントレベル３"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_301-302"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_310-311"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"手\""]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "handjob_low", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_320-324"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 5]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 122, "indent": 0, "parameters": [457, 457, 0, 0, 4]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_350-351"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 6]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(108, 2, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_352-353"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 313, "indent": 0, "parameters": [0, 21, 1, 2]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントレベル４"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["イベントレベル４"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_401"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 0, "parameters": [68]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_410-414"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [68]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_450-452"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 313, "indent": 0, "parameters": [0, 21, 1, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_490"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["イベントレベル５"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["イベントレベル５"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_501"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "BG-DirtCave", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 20]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 0, "parameters": [69]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_510-516"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_530"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "!cum_in_long2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [69]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_551"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 313, "indent": 0, "parameters": [0, 21, 1, 2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_590"]}, {"code": 119, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["******************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["******************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 13}, {"id": 36, "name": "戦闘時のセリフ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["********************************************"]}, {"code": 408, "indent": 0, "parameters": ["チュートリアル"]}, {"code": 408, "indent": 0, "parameters": ["********************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 235, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-4"]}, {"code": 121, "indent": 1, "parameters": [235, 235, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["戦闘開始時のセリフ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 108, "indent": 0, "parameters": ["オープニング"]}, {"code": 408, "indent": 0, "parameters": ["ならず者戦"]}, {"code": 111, "indent": 0, "parameters": [1, 902, 0, 12, 0]}, {"code": 111, "indent": 1, "parameters": [4, 1, 4, 99]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-14"]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 408, "indent": 0, "parameters": ["クエスト901002『地下下水の調査』　スライム戦の前のセリフ"]}, {"code": 408, "indent": 0, "parameters": ["*********************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 903, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [4, 1, 4, 99]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(false);"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_20-21"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 126, "indent": 1, "parameters": [62, 0, 0, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_25"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [30, 30, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 2]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [1, "stand-heroine-basic-weapon", 0, 1, 21, 22, 50, 50, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["$CSDM.SetVisible(true);"]}, {"code": 122, "indent": 1, "parameters": [903, 903, 0, 0, 5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 11}]}