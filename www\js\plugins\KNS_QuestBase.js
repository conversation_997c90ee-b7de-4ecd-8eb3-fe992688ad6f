/*:
 * @plugindesc ver.1.1.0 KNS_QuestListより上に設置してください
 * <AUTHOR>
 *
 * @param ver110
 * @text ver.1.1.0
 * 
 * @param RetireQuestCommonEvent
 * @text 中断処理コモンイベント
 * @parent ver110
 * @type common_event
 * @default 999
 *
 * @param RetireQuestIdVarId
 * @text 中断クエストID格納変数
 * @parent ver110
 * @type variable
 * @default 3
 *
 * @param RetireQuestVarIdVarId
 * @text 中断クエスト管理変数ID格納変数
 * @parent ver110
 * @type variable
 * @default 4
 *
 * @param QuestReleaseSwitchId
 * @text メニューコマンドに追加
 * @desc 指定のスイッチがONのときメニューコマンドにクエストを追加します。0の場合は常時追加。
 * @type switch
 * @default 0
 *
 * @param BASE_TERMS
 * @text 言語別用語集
 * @desc プラグイン中で使用する用語を言語ごとに指定します。
 * [メニューコマンド, 進行中ボタン, 完了ボタン, 解決済み表示]
 * @type string[][]
 * @default ["[\"クエスト\",\"進行中\",\"完了\",\"解決済み\"]","[\"Quest\",\"On progress\",\"Solved\",\"Complete!\"]","[\"PROBLEMA\",\"EN PROGRESO\",\"RESUELTO\",\"¡RESUELTO!\"]","[\"任务\",\"进行中\",\"完成\",\"完成任务\"]"]
 *
 * @help
 * ※このプラグインはKNS_CsvReader、KNS_RgssTranslatorの
 * 　下に設置してください。
 * 
 * 【ver. 1.1.1(2024-1-25)】
 * QuestList.csvのF列に中断を禁止するフラグを追加しました
 * （日本語の列はG列にずらしてください）。
 * クエストメニュー上から中断できないようにするクエストのF列に
 * trueと記述してください。
 * 
 * 【ver. 1.1.0(2023-7-30)】
 * ナビに表示させるクエストを登録できる機能を追加しました。
 * 以下の用語をlogsheet.csvに追加してください。
 * 
 * Quest Navi Bookmark,ナビ登録,Bookmark,,,,
 * Quest Navi Retire,クエスト破棄,Revert,,,,
 * Quest Navi Cancel,戻る,Back,,,,
 * Quest Navi Bookmark Description,"『%1』を
 * ナビに登録","Bookmark ""%1""
 * on Navi",,,,
 * Quest Navi Bookmarked,[ナビ],[Bookmarked],,,,
 * Quest Navi Bookmarked None,- 未設定 -,- None -,,,,
 * 
 * 
 * KNS_CsvReaderで読み取られたクエストリストのうち、
 * 進行中と完了済みのものを表示します。
 *
 * ver.1.0.0(2023-05-18)
 * - デモ
 * ver.1.1.0(2023-7-30)
 * - ナビ登録・中断機能を追加
 * ver.1.1.1(2024-1-25)
 * - 中断禁止機能を追加
 */

const KNS_QuestBase = {
	name: "KNS_QuestBase",
	param: null,
	unclassifiedQuest: "？？？？？",
	UnclassifiedQuestColor: ["#888888", "#000000"],
	StarQuestColor: ["#ffff00", "#666600"],
	MaxStarSize: 5,
	getTerm(index){ return this.BASE_TERMS[KNS_CsvReader.getLanguageId()][index]; },
	questProgressValue: function(id){
		const item = $csvQuestList[id];
		return item && item.var_id ? $gameVariables.value(item.var_id) : 0;
	},
	is_quest_unreleased: function(id){
		return this.questProgressValue(id) <= 0;
	},
	is_quest_done: function(id){
		return this.questProgressValue(id) >= 99;
	},
	parseAsPlaneText: function(str){
		return str.replace(/(\\\w{1,3}(?:\[\d+\])?)/g, "");
	},
	generateStarBitmap: function(rate, fontSize){
		let size = Math.floor(fontSize * 1.125);
		const bmp = new Bitmap(this.MaxStarSize * size, size);

		let str = "", colors;
		if (rate === this.MaxStarSize + 1){
			str = this.unclassifiedQuest;
			colors = this.UnclassifiedQuestColor;
		}else{
			for (let i = 0; i < this.MaxStarSize; i++){ str += i < rate ? "★" : "☆"; }
			colors = this.StarQuestColor;
		}
		bmp.fontSize = fontSize;
		[bmp.textColor, bmp.outlineColor] = colors;
		bmp.drawText(str, 0, 0, bmp.width, bmp.height, "center");
		return bmp;
	},
	getCurrentProgressIndex: function(item){
		const value = this.questProgressValue(item.id);
		const progress = item.progress;
		for (let i = 0; i < progress.length; i++){
			if (progress[i][0] > value){ return i - 1; }
		}
		return progress.length - 1;
	},
};
(function(){
	//====================================================
	// new KNS_QuestBase
	//====================================================
	this.param = PluginManager.parameters(this.name);
	this.BASE_TERMS = JsonEx.parse(this.param.BASE_TERMS).map(function(obj){
		return JsonEx.parse(obj);
	});
	this.QuestReleaseSwitchId = Math.floor(this.param.QuestReleaseSwitchId || 0);
	this.RetireQuestCommonEvent = Math.floor(this.param.RetireQuestCommonEvent);
	this.RetireQuestIdVarId = Math.floor(this.param.RetireQuestIdVarId);
	this.RetireQuestVarIdVarId = Math.floor(this.param.RetireQuestVarIdVarId);

	//====================================================
	// alias Window_MenuCommand
	//====================================================
	const _Window_MenuCommand_addMainCommands = Window_MenuCommand.prototype.addMainCommands;
	Window_MenuCommand.prototype.addMainCommands = function() {
		_Window_MenuCommand_addMainCommands.apply(this, arguments);
		const id = KNS_QuestBase.QuestReleaseSwitchId;
		if (id === 0 || $gameSwitches.value(id)){
			this.addCommand(KNS_QuestBase.getTerm(0), 'knsQuest');
		}
	};

	//====================================================
	// alias Scene_Menu
	//====================================================
	const _Scene_Menu_createCommandWindow = Scene_Menu.prototype.createCommandWindow;
	Scene_Menu.prototype.createCommandWindow = function(){
		_Scene_Menu_createCommandWindow.apply(this, arguments);
		this._commandWindow.setHandler('knsQuest', this.knsOnQuestOk.bind(this));
	}

	Scene_Menu.prototype.knsOnQuestOk = function(){
		SceneManager.push(Scene_KNSQuest);
	}
}).call(KNS_QuestBase);

//====================================================
// new Scene_KNSQuestBase
//====================================================
class Scene_KNSQuestBase extends Scene_MenuBase{
	create(){
		super.create();
		this.knsCreateCategoryWindow();
		this.knsCreateListWindow();
		this.knsCreateDetailWindow();
	}
	knsGetCategoryWindow(){ return new Window_KNSQuestCategory(); }
	knsCreateCategoryWindow(){
		this._knsCategoryWindow = this.knsGetCategoryWindow();
		this._knsCategoryWindow.setHandler("ok",     this.knsOnCategoryOk.bind(this));
		this._knsCategoryWindow.setHandler("cancel", this.popScene.bind(this));
		this.addWindow(this._knsCategoryWindow);
	}
	knsOnCategoryOk(){
		if (this._knsListWindow.index() === -1){ this._knsListWindow.select(0); }
		this._knsListWindow.activate();
	}
	knsGetListWindow(x, y, w, h){ return new Window_KNSQuestList(x, y, w, h); }
	knsCreateListWindow(){
		this._knsListWindow = this.knsGetListWindow(
			0, this._knsCategoryWindow.height,
			360, Graphics.boxHeight - this._knsCategoryWindow.height
		);
		this._knsListWindow.setHandler("ok", this.knsOnListWindowOk.bind(this));
		this._knsListWindow.setHandler("cancel",  this.knsOnListCancel.bind(this));
		this._knsCategoryWindow.setHelpWindow(this._knsListWindow);
		this.addWindow(this._knsListWindow);
	}
	knsOnListWindowOk(){
		this._knsListWindow.activate();
	}
	knsOnListCancel(){
		this._knsCategoryWindow.activate();
		this._knsDetailWindow.knsSetItem(null);
	}
	knsGetDetailWindow(x, y, w, h){ return new Window_KNSQuestDetail(x, y, w, h); }
	knsCreateDetailWindow(){
		this._knsDetailWindow = this.knsGetDetailWindow(
			this._knsListWindow.width,
			this._knsListWindow.y,
			Graphics.boxWidth - this._knsListWindow.width,
			this._knsListWindow.height
		);
		this._knsListWindow.setHelpWindow(this._knsDetailWindow);
		this.addWindow(this._knsDetailWindow);
	}
	knsGetItem(){
		return this._knsDetailWindow._data;
	}
}

//====================================================
// new Window_KNSQuestCategory
//====================================================
class Window_KNSQuestCategory extends Window_Selectable{
	initialize(){
		super.initialize(0, 0, Graphics.boxWidth, this.fittingHeight(1));
		this.select(0);
		this.activate();
		this.refresh();
	}
	maxItems(){ return 2; }
	maxCols(){ return this.maxItems(); }
	updateHelp(){
		this._helpWindow.knsSetCategory(this.index());
	}
	drawItem(index){
		const rect = this.itemRectForText(index);
		this.drawText(KNS_QuestBase.getTerm(1 + index),
			rect.x, rect.y, rect.width, "center"
		);
	}
}

//====================================================
// new Window_KNSQuestList
//====================================================
class Window_KNSQuestList extends Window_Selectable{
	initialize(x, y, w, h){
		this.knsGetAllData();
		this._knsBookmarkList = [];
		this._data = [];
		this._categoryId = -1;
		this._knsStarBitmaps = new Array(KNS_QuestBase.MaxStarSize + 1).fill().map(
			function(_, i){ return KNS_QuestBase.generateStarBitmap(i + 1, 18); }
		);
		super.initialize(x, y, w, h);
	}
	knsGetAllData(){
		this._allData = Object.values($csvQuestList);
	}
	knsSetCategory(id){
		if (this._categoryId !== id){
			this._categoryId = id;
			this.refresh();
			this.deselect();
		}
	}
	updateHelp(){
		this._helpWindow.knsSetItem(this._data[this.index()]);
	}
	makeItemList(){
		this._data = this._allData.filter(function(item){
			if (item.id === 0 || KNS_QuestBase.is_quest_unreleased(item.id)){
				return false;
			}
			const isDone = KNS_QuestBase.is_quest_done(item.id);
			return this._categoryId === 0 ? !isDone : isDone;
		}, this);
	}
	refresh(){
		this._knsBookmarkList = $gameSystem.knsGetQuestNaviList();
		this.makeItemList();
		this.createContents();
		this.drawAllItems();
	}
	maxItems(){ return this._data.length; }
	itemHeight(){ return 48; }
	isCurrentItemEnabled(){ return !!this._data[this.index()]; }
	drawItem(index){
		const item = this._data[index];
		if (item){
			const rect = this.itemRectForText(index);
			// bookmark
			if (this._knsBookmarkList.includes(item.id)){
				this.changeTextColor(this.textColor(2));
				this.contents.fontSize = 14;
				this.drawText($LogSheetCSV.Get("Quest Navi Bookmarked"),
					rect.x, rect.y + 22, rect.width
				);
				this.contents.fontSize = this.standardFontSize();
			}
			// star
			const bmp = this._knsStarBitmaps[item.difficulty - 1];
			this.contents.blt(bmp, 0, 0, bmp.width, bmp.height,
				rect.x + rect.width - bmp.width,
				rect.y + rect.height - bmp.height,
				bmp.width, bmp.height
			);
			this.drawTextEx(item.title, rect.x, rect.y);
		}
	}
}

//====================================================
// new Window_KNSQuestDetail
//====================================================
class Window_KNSQuestDetail extends Window_Base{
	initialize(x, y, w, h){
		this._knsRenderBitmap = new Bitmap(1500, 64);
		this._knsHeader = $csvQuestList[0];
		this._data = null;
		super.initialize(x, y, w, h);
	}
	knsSetItem(data){
		if (this._data !== data){
			this._data = data;
			this.refresh();
		}
	}
	refresh(){
		this.contents.clear();
		if (this._data){
			const bmp = KNS_QuestBase.generateStarBitmap(this._data.difficulty, 22);
			this.contents.blt(bmp, 0, 0, bmp.width, bmp.height,
				(this.contents.width - bmp.width) / 2, 54, bmp.width, bmp.height
			);
			this.knsDrawTitle();

			let y = 96;
			const headerWidth = this.contents.width * 1 / 4;
			const valueWidth = this.contents.width - headerWidth;
			["short_desc", "client", "map_name"].forEach(function(key){
				this.knsDrawHeader(key, y, headerWidth);
				y = this.knsDrawTextAutoline(this._data[key], headerWidth, y);
			}, this);
			this.knsDrawHeader("rewards", y, headerWidth);
			y = this.knsDrawRewards(headerWidth, y, valueWidth);

			y = this.knsDrawLine(y);
			y = this.knsDrawAllProgress(y, headerWidth, valueWidth);
			this.knsDrawTextAutoline(this._data.long_desc, 0, y);
		}
	}
	knsDrawAllProgress(y, headerWidth, valueWidth){
		this.knsDrawHeader("progress", y, headerWidth);
		if (KNS_QuestBase.is_quest_done(this._data.id)){
			this.changeTextColor(this.textColor(2));
			this.drawText(KNS_QuestBase.getTerm(3),
				headerWidth, y, valueWidth
			);
		}
		y = this.knsDrawProgress(y + this.lineHeight());
		y = this.knsDrawLine(y);
		return y;
	}
	knsDrawHeader(key, y, w){
		this.drawText(this._knsHeader[key], 0, y, w);
	}
	knsDrawTitle(){
		const old = this.contents;
		this._knsRenderBitmap.clear();
		this.contents = this._knsRenderBitmap;
		this.resetFontSettings();
		const tw = this.drawTextEx("\\{\\{" + this._data.title, 0, 0);
		const dw = Math.min(old.width, tw);
		old.blt(this._knsRenderBitmap, 0, 0, tw, this._knsRenderBitmap.height,
			(old.width - dw) / 2, 0, dw, this._knsRenderBitmap.height
		);
		this.contents = old;
	}
	knsDrawRewards(x, y, w){
		const rewards = this._data.rewards;
		if (rewards.length === 0){
			return y + this.lineHeight();
		}else{
			const nameWidth = 256;
			const numWidth = this.textWidth('×000');
			rewards.forEach(function(reward){
				if (typeof reward[0] === 'number'){
					this.drawTextEx(reward[0] + "\\c[16]" + TextManager.currencyUnit, x, y);
				}else{
					this.drawItemName(reward[0], x, y, nameWidth - 8);
					this.changeTextColor(this.systemColor());
					this.drawText("×", x + nameWidth, y, numWidth, 'left');
					this.changeTextColor(this.normalColor());
					this.drawText(reward[1], x + nameWidth, y, numWidth, 'right');
				}
				y += this.lineHeight();
			}, this);
			this.changeTextColor(this.normalColor());
		}
		return y;
	}
	knsDrawLine(y){
		const lh = this.lineHeight();
		this.contents.fillRect(0, y + lh / 2, this.contents.width, 3, "#ffffff88");
		return y + lh;
	}
	knsDrawProgress(y){
		const hw = 64;
		const max = KNS_QuestBase.getCurrentProgressIndex(this._data);
		const progress = this._data.progress;
		for (let i = 0; i <= max; i++){
			let text  = (i + 1) + ":" + progress[i][1];
			if (i !== max){
				text = "\\c[7]" + text;
			}
			y = this.knsDrawTextAutoline(text, hw, y);
		}
		this.changeTextColor(this.normalColor());
		return y;
	}
};

(function(){
	//=======================================================
	// alias Game_System
	//=======================================================
	Game_System.prototype.knsGetQuestNaviList = function(){
		if (!this._knsQuestNaviList){
			this._knsQuestNaviList = new Array(KNS_QuestHUD.param.maxQuestNumber);
			this._knsQuestNaviList.fill(0);
		}
		return this._knsQuestNaviList.map(function(quest_id){
			if (quest_id === 0 || KNS_QuestBase.is_quest_unreleased(quest_id)){
				return 0;
			}
			return KNS_QuestBase.is_quest_done(quest_id) ? 0 : quest_id;
		});
	}

	Game_System.prototype.knsSetQuestNaviList = function(index, quest_id){
		const list = this.knsGetQuestNaviList(); // init
		const last = list.indexOf(quest_id);
		if (last === index){
			quest_id = 0;
		}else if (last !== -1){
			list[last] = 0;
		}
		list[index] = quest_id;
		this._knsQuestNaviList = list;
	}
})();

// navi Commands
//====================================================
// new Scene_KNSQuest
//====================================================
class Scene_KNSQuest extends Scene_KNSQuestBase{
	create(){
		super.create();
		this.knsCreateQuestCommandWindow();
		this.knsCreateNaviListWindow();
	}
	knsCreateQuestCommandWindow(){
		this._knsQuestCommandWindow =  new Window_KnsQuestCommand(
			this._knsListWindow.x, this._knsListWindow.width
		);
		this._knsQuestCommandWindow.setHandler('bookmark', this.knsOnQuestCommandBookmark.bind(this));
		this._knsQuestCommandWindow.setHandler('retire', this.knsOnQuestCommandRetire.bind(this));
		this._knsQuestCommandWindow.setHandler('cancel', this.knsOnQuestCommandCancel.bind(this));
		this.addWindow(this._knsQuestCommandWindow);
	}
	knsOnQuestCommandBookmark(){
		const item = this.knsGetItem();
		this._knsQuestNaviListWindow.knsSetup(item.id);
		this._knsQuestCommandWindow.close();
	}
	knsOnQuestCommandRetire(){
		const item = this.knsGetItem();
		$gameVariables.setValue(KNS_QuestBase.RetireQuestIdVarId, item.id);
		$gameVariables.setValue(KNS_QuestBase.RetireQuestVarIdVarId, item.var_id);
		$gameTemp.reserveCommonEvent(KNS_QuestBase.RetireQuestCommonEvent);
		SceneManager.goto(Scene_Map);
	}
	knsOnQuestCommandCancel(){
		this._knsQuestCommandWindow.close();
		this._knsListWindow.activate();
	}

	knsCreateNaviListWindow(){
		this._knsQuestNaviListWindow = new Window_KnsQuestNaviList();
		this._knsQuestNaviListWindow.setHandler('ok', this.knsOnNaviListOk.bind(this));
		this._knsQuestNaviListWindow.setHandler('cancel', this.knsOnNaviListCancel.bind(this));
		this.addWindow(this._knsQuestNaviListWindow);
	}
	knsOnNaviListOk(){
		const item = this.knsGetItem();
		$gameSystem.knsSetQuestNaviList(
			this._knsQuestNaviListWindow.index(), item.id
		);

		this._knsQuestNaviListWindow.refresh();
		this._knsQuestNaviListWindow.activate();
		this._knsListWindow.refresh();
	}
	knsOnNaviListCancel(){
		this._knsQuestNaviListWindow.close();
		this._knsListWindow.activate();
	}

	knsOnListWindowOk(){
		const item = this.knsGetItem();
		if (item && !KNS_QuestBase.is_quest_done(item.id)){
			this._knsQuestCommandWindow.knsSetup(item);

			const cursor = this._knsListWindow._windowCursorSprite;
			let y = this._knsListWindow.y + cursor.y + cursor.height;
			if (y >= Graphics.height - this._knsQuestCommandWindow.height){
				this._knsQuestCommandWindow.y = (
					this._knsListWindow.y + 
					cursor.y - this._knsQuestCommandWindow.height
				);
			}else{
				this._knsQuestCommandWindow.y = y;
			}
		}else{
			super.knsOnListWindowOk();
		}
	}
}

//=======================================================
// new Window_KnsQuestCommand
//=======================================================
class Window_KnsQuestCommand extends Window_Command{
	initialize(x, width){
		this._knsQuest = null;
		this._knsWidth = width;
		super.initialize(x, 0);
		this.openness = 0;
		this.deactivate();
		this.select(0);
	}
	windowWidth(){ return this._knsWidth; }
	windowHeight(){ return this.fittingHeight(3); }
	knsSetup(item){
		this._knsQuest = item;
		this.refresh();
		this.open();
		this.activate();
	}
	makeCommandList(){
		this.addCommand($LogSheetCSV.Get("Quest Navi Bookmark"), 'bookmark');
		this.addCommand($LogSheetCSV.Get("Quest Navi Retire"), 'retire', this._knsQuest && !this._knsQuest.no_retire);
		this.addCommand($LogSheetCSV.Get("Quest Navi Cancel"), 'cancel');
	}
};

//=======================================================
// new Window_KnsQuestNaviList
//=======================================================
class Window_KnsQuestNaviList extends Window_Selectable{
	initialize(){
		const width  = Graphics.width * 0.5;
		const height = this.knsHeaderHeight() + this.fittingHeight(this.maxItems());
		super.initialize(
			(Graphics.width - width) / 2, (Graphics.height - height) / 2,
			width, height
		);
		this.openness = 0;
		this.deactivate();
	}
	knsHeaderHeight(){ return this.lineHeight() * 2; }
	itemRect(index){
		const rect = super.itemRect(index);
		rect.y += this.knsHeaderHeight();
		return rect;
	}
	maxItems(){ return KNS_QuestHUD.param.maxQuestNumber; }
	knsSetup(quest_id){
		this._knsQuestId = quest_id; 
		const list = $gameSystem.knsGetQuestNaviList();
		const index = list.indexOf(0);
		this.select(index === -1 ? 0 : index);
		this.refresh();
		this.open();
		this.activate();
	}
	drawAllItems(){
		this.contents.fillRect(0, this.knsHeaderHeight() - 2,
			this.contents.width, 1, this.systemColor()
		);
		$LogSheetCSV.Get("Quest Navi Bookmark Description").format(
			KNS_QuestBase.parseAsPlaneText($csvQuestList[this._knsQuestId].title)
		).split('\n').forEach(function(text, i){
			this.drawText(text, 0, this.lineHeight() * i,
				this.contents.width, 'center'
			);
		}, this);

		const list = $gameSystem.knsGetQuestNaviList();
		const topIndex = this.topIndex();
		const maxPageItems = this.maxPageItems();
		for (let i = Math.max(topIndex, 0); i < maxPageItems; i++) {
			const index = topIndex + i;
			if (index < this.maxItems()) { this.drawItem(index, list); }
		}
	}
	drawItem(index, list){
		const rect = this.itemRect(index);
		const item = $csvQuestList[list[index]];
		if (!item || item.id === 0){
			this.changeTextColor(this.textColor(8));
			this.drawText($LogSheetCSV.Get("Quest Navi Bookmarked None"),
				rect.x, rect.y, rect.width, 'center'
			);
			this.changeTextColor(this.normalColor());
		}else{
			this.drawTextEx(item.title, rect.x, rect.y, rect.width);
		}
	}
};