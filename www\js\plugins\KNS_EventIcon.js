/*:
 * @plugindesc キャラの頭上にアイコンを表示します。
 * <AUTHOR>
 * 
 * @help
 * ※このプラグインはKNS_EventGauge前後に設置してください。
 * 
 * キャラの頭上にアイコンを表示します。
 * 使用される画像はimg/pictures内に格納してください。
 * 
 * KNS_EventGaugeとのレイヤー関係はプラグイン管理上の位置で
 * 変更できます。
 * 
 * ■プラグインコマンド
 * KNS_EventIcon show 対象 画像名
 * 　キャラの頭上にアイコンを表示します。
 * 　対象に使用できる記述は以下の通りです。
 * 
 * ・数値     ... 数値で指定します。
 * ・\v[数値] ... 数値の変数IDから指定します。
 * ・this     ... 実行中のイベントを指定します。
 * ・all      ... 全てのイベントとプレイヤーを指定します。
 * ・player   ... プレイヤーを指定します。
 * 
 * 
 * KNS_EventIcon hide 対象
 * 　キャラの頭上に表示されたアイコンを非表示にします。
 * 
 */
const KNS_EventIcon = {
    name: 'KNS_EventIcon',
    param: null,
    parseEvents: function(eventId, key){
        switch (key){
            case 'player': return [$gamePlayer];
            case 'all':    return $gameMap.events().concat($gamePlayer);
            case 'this':{
                const ev = $gameMap.event(eventId);
                if (ev) {
                    return [ev];
                }else{
                    console.error(`[${KNS_EventIcon.name}]無効なイベントID=${eventId}`);
                    return [];
                }
            }
            default:{
                const eventId = this.parseVariable(key);
                const ev = $gameMap.event(eventId);
                if (ev) {
                    return [ev];
                }else{
                    console.error(`[${KNS_EventIcon.name}]無効なイベントID=${eventId}`);
                    return [];
                }
            }
        }
    },
    parseVariable: function(arg){
        if (/\\v\[(\d+)\]/.test(arg)){
            return $gameVariables.value(Number(RegExp.$1));
        }
        return Number(arg);
    },
};

(function(){
    //=============================================
    // alias Game_Interpreter
    //=============================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command === KNS_EventIcon.name) {
            switch (args[0]){
                case 'show':{
                    let [target, pictureName] = args.slice(1, args.length);

                    KNS_EventIcon.parseEvents(this._eventId, target).forEach(function(ev){
                        ev.knsShowEventIcon(pictureName);
                    });
                    break;
                }
                case 'hide':{
                    let [target] = args.slice(1, args.length);
                    KNS_EventIcon.parseEvents(this._eventId, target).forEach(function(ev){
                        ev.knsHideEventIcon();
                    });
                    break;
                }
            }
        }
    }


    //=============================================
    // alias Game_Character
    //=============================================
    Game_Character.prototype.knsGetEventIcon = function() {
        return this._knsEventIconName || null;
    }

    Game_Character.prototype.knsShowEventIcon = function(pictureName) {
        this._knsEventIconName = pictureName;
    }

    Game_Character.prototype.knsHideEventIcon = function() {
        this._knsEventIconName = null;
    }

    //=============================================
    // alias Sprite_Character
    //=============================================
    const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;
    Sprite_Character.prototype.initialize = function(){
        _Sprite_Character_initialize.apply(this, arguments);
        this._knsEventIcon = new KNS_EventIcon.Sprite_KnsEventIcon(this._character, this);
    }

    const _Sprite_Character_update = Sprite_Character.prototype.update;
    Sprite_Character.prototype.update = function(){
        _Sprite_Character_update.apply(this, arguments);
        if (this.parent && !this._knsEventIcon.parent){
            this.parent.parent.addChild(this._knsEventIcon);
        }
    }

    //=============================================
    // alias Sprite_KnsEventIcon
    //=============================================
    KNS_EventIcon.Sprite_KnsEventIcon = class extends Sprite {
        initialize(character, parentSprite) {
            super.initialize(null);
            this._knsCharacter = character;
            this._knsParent = parentSprite;

            this.knsUpdateEventIcon();
        }
        update() {
            super.update();
            this.knsUpdateEventIcon();
        }
        knsUpdateEventIcon() {
            const icon = this._knsCharacter && this._knsCharacter.knsGetEventIcon();
            if (icon) {
                this.bitmap = ImageManager.loadPicture(icon);

                if (this.bitmap.isReady()) {
                    this.visible = true;
                    const charX = this._knsParent.x;
                    const charY = this._knsParent.y - this._knsParent.height;
                    this.position.set(
                        Math.floor(charX - this.bitmap.width * 0.5),
                        Math.floor(charY - this.bitmap.height)
                    );
                } else {
                    this.visible = false;
                }
            } else {
                if (this.bitmap) {
                    this.bitmap = null;
                    this.visible = false;
                }
            }
        }
    };

})();