/*:
 * @plugindesc Modify the selling price of items based on a plugin parameter.
 * @help
 * This plugin modifies the selling price of items, weapons, and armors based on a plugin parameter.
 * 
 * @param Sell Percentage
 * @desc Percentage of the original price for selling. E.g., set 10 for 10% of the original price.
 * @default 50
 */

(function() {
    var parameters = PluginManager.parameters('TB_SellPriceModifier');
    var sellPercentage = Number(parameters['Sell Percentage'] || 50) / 100;

    Scene_Shop.prototype.sellingPrice = function() {
        return Math.floor(this._item.price * sellPercentage);
    };
})();
