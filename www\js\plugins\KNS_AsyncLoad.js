/*:
 * @plugindesc ver.1.0.3 - スクリプト中のshowPictureの表示されるタイミングを調整します
 * <AUTHOR>
 * 
 * @param NoWaitSwitch
 * @text ウェイト停止スイッチ
 * @type switch
 * @default 280
 * 
 * @help
 * 同フレームに実行されたshowPicture, Game_Picture.showを
 * 画像が読み込み完了するまでウェイトします。
 * 
 * ■更新履歴
 * ver.1.0.0(2024/03/19)
 * - デモ
 * ver.1.0.1(2024/03/22)
 * - コモンイベント内が画像読み込み中に終了した場合
 * 　画像が表示されない不具合を修正
 * ver.1.0.2(2024/03/23)
 * - 条件分岐終了時も読み込み待ちが発生し都度都度
 * 　ウェイトしてしまう不具合を修正
 * ver.1.0.3(2024/03/24)
 * - erasePictureもshowに合わせてウェイトする
 */
const KNS_AsyncLoad = {
    name: "KNS_AsyncLoad",
    param: null
};

(function(){
    this.param = PluginManager.parameters(this.name);
    this.param.NoWaitSwitch = Number(this.param.NoWaitSwitch);

    //======================================================
    // alias Game_Interpreter
    //======================================================
    const _Game_Interpreter_command355 = Game_Interpreter.prototype.command355;
    Game_Interpreter.prototype.command355 = function() {
        if (!$gameSwitches.value(KNS_AsyncLoad.param.NoWaitSwitch)){
            const interpreter = this;
            const old = Game_Picture.prototype.show;
            Game_Picture.prototype.show = function(name, origin, x, y, scaleX, scaleY, opacity, blendMode){
                interpreter.knsReserveShowPicture(
                    this, name, origin, x, y, scaleX, scaleY, opacity, blendMode
                );
            };
            const oldE = Game_Screen.prototype.erasePicture;
            Game_Screen.prototype.erasePicture = function(picId){
                interpreter.knsReserveErasePicture($gameScreen.realPictureId(picId));
            };

            const result = _Game_Interpreter_command355.apply(this, arguments);
            Game_Picture.prototype.show = old;
            Game_Screen.prototype.erasePicture = oldE;
            return result;
        }else{
            return _Game_Interpreter_command355.apply(this, arguments);
        }
    };

    Game_Interpreter.prototype.knsReserveShowPicture = function(
        pic, name, origin, x, y, scaleX, scaleY, opacity, blendMode
    ){
        if (!this._knsReservedShowPicture){
            this._knsReservedShowPicture = [];
        }
        this._knsReservedShowPicture.push({
            pic: pic, bmp: name ? ImageManager.loadPicture(name) : null,
            name: name, origin: origin, x: x, y: y,
            scaleX: scaleX, scaleY: scaleY, opacity: opacity, blendMode: blendMode
        });
    }

    Game_Interpreter.prototype.knsReserveErasePicture = function(picId){
        if (!this._knsReservedShowPicture){
            this._knsReservedShowPicture = [];
        }
        this._knsReservedShowPicture.push({ picId: picId, bmp: null, erase: true });
    }

    Game_Interpreter.prototype.knsIsReservedShowRequested = function(){
        return (
            this._knsReservedShowPicture &&
            this._knsReservedShowPicture.length > 0
        );
    }

    Game_Interpreter.prototype.knsIsReservedShowReady = function(){
        return this._knsReservedShowPicture.every(function(info){ return !info.bmp || info.bmp.isReady() });
    }

    Game_Interpreter.prototype.command0 = function(){
        // 末尾以外は待たない
        if (this._index + 1 !== this._list.length){
            return true;
        }
        if (this.knsIsReservedShowRequested()){
            if (this.knsIsReservedShowReady()){
                this.knsAsyncShowPictures();
            }else{
                return false;
            }
        }
        return true;
    }

    const _Game_Interpreter_update = Game_Interpreter.prototype.update;
    Game_Interpreter.prototype.update = function() {
        if (this.knsIsReservedShowRequested()){
            if (this.knsIsReservedShowReady()){
                this.knsAsyncShowPictures();
                _Game_Interpreter_update.apply(this, arguments);
            }
        }else{
            _Game_Interpreter_update.apply(this, arguments);
        }
    };

    Game_Interpreter.prototype.knsAsyncShowPictures = function(){
        this._knsReservedShowPicture.forEach(function(info){
            if (info.erase){
                if ($gameScreen._pictures[info.picId]){
                    $gameScreen._pictures[info.picId].erase();
                }
                return;
            }
            info.pic.show(
                info.name, info.origin, info.x, info.y,
                info.scaleX, info.scaleY, info.opacity, info.blendMode
            );
        }, this);
        this._knsReservedShowPicture = null;
    }

}).call(KNS_AsyncLoad);