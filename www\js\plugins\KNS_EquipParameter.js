
/*:
 * @plugindesc ver.1.3.1 装備に特殊なパラメータを追加し装備画面に追加します
 * <AUTHOR>
 *
 * @help
 * ■装備画面
 * 装備メモ欄に記述された以下の特殊なパラメータを
 * 装備画面に表示します。
 * - HIT
 * - FAT
 * - REQ
 * - DMG
 * - Evasion
 * - Armor
 * - M_Armor
 * 
 * ■用語設定
 * 上記のパラメータの名称はlogsheet.csvに「Status (項目名)」という
 * 行を追加することで指定可能です。
 * 例）
 * Status HIT,命中率,HIT,,,,
 * Status DMG,ダメージ,DMG,,,,
 * Status FAT,疲労値,FAT,,,,
 * Status REQ,要求能力,REQ,,,,
 * Status Evasion,回避率,Evasion,,,,
 * Status Armor,アーマー,Armor,,,,
 * Status M_Armor,魔法アーマー,Armor,,,,
 * 
 * ■プラグインコマンド
 * KNS_EquipParameter set (アクターID) (DMG|Armor) (変数ID)
 * 　変数IDに指定のアクターのDMG、Armor値を代入します。
 * 　代入されるのはダイスによる乱数計算が行われた後の合計値です。
 * 　変数値とアクターIDはv[n](nに自然数)と記述すると変数の値から
 * 　IDを指定できます。
 * 
 * KNS_EquipParameter set (アクターID) (DMG|Armor) (変数ID) (+|-) (装備タイプID)...
 * 　上記コマンドで特定のスロットを指定しパラメータを取得する際に使用します。
 * 　+を指定した場合、後に指定した装備スロットだけの
 * 　パラメータが取得されます。
 * 　-を指定した場合、全ての装備スロットから指定のものだけ除外し
 * 　パラメータが取得されます。
 * 
 * ■スクリプト
 * 以下のスクリプトで装備中のアイテムに指定された
 * 上記パラメータの合計値を取得できます。
 * 
 * const actor = $gameActor.actor(1);
 * actor.HIT
 * actor.FAT
 * actor.REQ
 * actor.DMG
 * actor.Evasion
 * actor.Armor
 * actor.M_DMG
 * actor.M_Armor
 *  
 * ■更新履歴
 * ver.1.0.0(2023-09-18)
 * - デモ
 * ver.1.1.0(2023-09-29)
 * - ダイス値を含むパラメータのステータス表示を改修
 * - ダイス値の合計値を取得するプラグインコマンドを実装
 * ver.1.2.0(2023-10-09)
 * - ステータス・アイテムウィンドウのUIを変更
 * - ダイス値が同一のパラメータを合計して表示するよう変更
 * - コンソールに計算過程を表示
 * - パラメータ「M_Armor」を追加
 * - プラグインコマンド「set」にスロット指定を追加
 * ver.1.2.1(2023-10-11)
 * - 疲労度、要求筋力の能力値の増減表示色を逆に
 * - パラメータ「M_DMG」を追加
 * ver.1.3.0(2023-3-30)
 * - ショップ画面にステータス追加
 * ver.1.3.1(2023-4-1)
 * - ショップ画面も「要求筋力」「疲労」のパラメータを負数化
 */

const KNS_EquipParameter = {
    name: "KNS_EquipParameter",
    specialParams: ["FAT", "REQ", "HIT", "Evasion", "DMG", "Armor", "M_DMG", "M_Armor"],
    oppositeColorParams: ["FAT", "REQ"],
    diceParams: ["HIT", "Evasion", "DMG", "Armor", "M_DMG", "M_Armor"],
    getSpecialParameter: function(item, key){
        if (item && item.meta[key]){
            return Math.floor(item.meta[key].split("d")[0]);
        }else{
            return 0;
        }
    },
    getDiceParameter: function(item, key){
        if (item && item.meta[key] && this.diceParams.contains(key)){
            return Math.floor(item.meta[key].split("d")[1] || 0);
        }else{
            return 0;
        }
    },
    getStatusName: function(key){
        return $LogSheetCSV.Get("Status " + key);
    },
    parseNumber: function(arg){
        return Number(/v\[(\d+)\]/i.test(arg) ?
            $gameVariables.value(RegExp.$1) : arg
        ) || 0;
    },
    getAllEquipTypes: function(){
        return $dataSystem.equipTypes.map(function(_, i){
            return i;
        });
    }
};

(function(){
    //===================================================
    // alias Game_Interpreter
    //===================================================
    const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
    Game_Interpreter.prototype.pluginCommand = function(command, args) {
        _Game_Interpreter_pluginCommand.apply(this, arguments);
        if (command !== KNS_EquipParameter.name){ return; }
        switch ((args[0] || "").toLowerCase()){
            case 'set':{
                const actor = $gameActors.actor(KNS_EquipParameter.parseNumber(args[1]));
                let tempTypes = args.slice(5, args.length).map(function(arg){
                    return Number(arg);
                });
                let types;
                if (args[4] === '+'){
                    types = tempTypes;
                }else{
                    types = KNS_EquipParameter.getAllEquipTypes().filter(
                        function(type){ return !tempTypes.contains(type); }
                    );
                }
                $gameVariables.setValue(
                    KNS_EquipParameter.parseNumber(args[3]),
                    actor.knsGetRandomSpecialParameter(args[2], types, true)
                );
                break;
            }
        }
    };

    //===================================================
    // alias Game_Battler
    //===================================================
    // 配列で元データを取得
    Game_Battler.prototype.knsSpecialParameter = function(item, obj){
        obj.re.lastIndex = 0;
        let note;
        if (!DataManager.isDXRPSItem(item) && typeof item.baseItemId === 'number'){
            if (DataManager.isWeapon(item)){
                note = $dataWeapons[item.baseItemId].note;
            }else if (DataManager.isArmor(item)){
                note = $dataArmors[item.baseItemId].note;
            }else{
                note = item.note;
            }
        }else{
            note = item.note;
        }
        while (obj.re.test(note)){
            const key = RegExp.$1;
            if (key.contains('d')){
                obj.array.push(key);
            }else{
                obj.number += Number(key);
            }
        }
    }

    Game_Battler.prototype.knsGetInitSpecialParameterObject = function(key){
        return {
            array: [],
            number: 0,
            re: new RegExp(`<\\s*${key}\\s*[:：]\\s*(.+?)\\s*>`, 'g')
        };
    }

    Game_Battler.prototype.knsGetSpecialParameterData = function(key){
        const obj = this.knsGetInitSpecialParameterObject(key);
        this.traitObjects().forEach(function(item){
            if (item){
                this.knsSpecialParameter(item, obj);
            }
        }, this);
        if (obj.number !== 0){ obj.array.unshift(String(obj.number)); }
        return obj.array;
    }

    // Game_Actor
    Game_Actor.prototype.knsGetSpecialParameterData = function(
        key, types=KNS_EquipParameter.getAllEquipTypes()
    ){
        const obj = this.knsGetInitSpecialParameterObject(key);
        const slots = this.equipSlots();
        this.equips().forEach(function(item, i){
            if (item && types.contains(slots[i])){
                this.knsSpecialParameter(item, obj);
            }
        }, this);
        if (obj.number !== 0){ obj.array.unshift(String(obj.number)); }
        return obj.array;
    }

    // 表記を取得
    Game_Battler.prototype.knsGetStringSpecialParameter = function(key){
        const array = this.knsGetSpecialParameterData(key);
        if (array.length === 0){
            return "0";
        }else if (KNS_EquipParameter.diceParams.contains(key)){
            return array.join("+");
        }else{
            return String(array.reduce(function(r, param){
                return r + Math.floor(param);
            }, 0));
        }
    }

    // ダイス最大値で計算
    Game_Battler.prototype.knsGetMaxSpecialParameter = function(key){
        return this.knsGetSpecialParameterData(key).reduce(function(r, param){
            const params = param.split("d");
            return r + Math.floor(params[0]) + (Math.floor(params[1]) || 0);
        }, 0);
    }

    // 乱数で計算
    Game_Battler.prototype.knsGetRandomSpecialParameter = function(key, types, show=false){
        const values  = [];
        const keys = this.knsGetSpecialParameterData(key, types);
        const result = keys.reduce(function(r, param){
            const [base, dice = 0] = param.split("d").map(s => Number(s));
            let value = 0;
            if (dice) {
                for (let i = 0; i < base; i++) {
                    const random = Math.randomInt(dice) + 1;
                    value += random;
                    values.push(random);
                }
            } else {
                value = base;
                values.push(value);
            }
            return r + value;
        }, 0);
        console.log(
`%c[${key}]${this.name()}:`,
"color:white; background-color:purple; padding:2px; border-radius:4px;",
`EquipTypes: ${types.join(', ')}`,
`\n[${keys.join(' + ')}]`,
`\n${values.join('+')} => Total: ${result}`
        );
        return result;
    }

    Object.defineProperties(Game_Battler.prototype, {
        HIT:       { get: function(){ return this.knsGetMaxSpecialParameter("HIT"); }, configurable: true },
        FAT:       { get: function(){ return this.knsGetMaxSpecialParameter("FAT"); }, configurable: true },
        REQ:       { get: function(){ return this.knsGetMaxSpecialParameter("REQ"); }, configurable: true },
        Evasion:   { get: function(){ return this.knsGetMaxSpecialParameter("Evasion"); }, configurable: true },
        DMG:       { get: function(){ return this.knsGetMaxSpecialParameter("DMG"); }, configurable: true },
        Armor:     { get: function(){ return this.knsGetMaxSpecialParameter("Armor"); }, configurable: true },
        M_DMG:     { get: function(){ return this.knsGetMaxSpecialParameter("M_DMG"); }, configurable: true },
        M_Armor:   { get: function(){ return this.knsGetMaxSpecialParameter("Armor"); }, configurable: true },

        R_HIT:     { get: function(){ return this.knsGetRandomSpecialParameter("HIT",     KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_FAT:     { get: function(){ return this.knsGetRandomSpecialParameter("FAT",     KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_REQ:     { get: function(){ return this.knsGetRandomSpecialParameter("REQ",     KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_Evasion: { get: function(){ return this.knsGetRandomSpecialParameter("Evasion", KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_DMG:     { get: function(){ return this.knsGetRandomSpecialParameter("DMG",     KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_Armor:   { get: function(){ return this.knsGetRandomSpecialParameter("Armor",   KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_M_DMG:   { get: function(){ return this.knsGetRandomSpecialParameter("M_DMG",   KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },
        R_M_Armor: { get: function(){ return this.knsGetRandomSpecialParameter("M_Armor", KNS_EquipParameter.getAllEquipTypes()); }, configurable: true },

        S_HIT:     { get: function(){ return this.knsGetStringSpecialParameter("HIT"); }, configurable: true },
        S_FAT:     { get: function(){ return this.knsGetStringSpecialParameter("FAT"); }, configurable: true },
        S_REQ:     { get: function(){ return this.knsGetStringSpecialParameter("REQ"); }, configurable: true },
        S_Evasion: { get: function(){ return this.knsGetStringSpecialParameter("Evasion"); }, configurable: true },
        S_DMG:     { get: function(){ return this.knsGetStringSpecialParameter("DMG"); }, configurable: true },
        S_Armor:   { get: function(){ return this.knsGetStringSpecialParameter("Armor"); }, configurable: true },
        S_M_DMG:   { get: function(){ return this.knsGetStringSpecialParameter("M_DMG"); }, configurable: true },
        S_M_Armor: { get: function(){ return this.knsGetStringSpecialParameter("M_Armor"); }, configurable: true },
    });

    //===================================================
    // alias Window_EquipStatus
    //===================================================
    const _Window_EquipStatus_numVisibleRows = Window_EquipStatus.prototype.numVisibleRows;
    Window_EquipStatus.prototype.windowHeight = function() {
        return Graphics.height - this.fittingHeight(2);
    };

    Window_EquipStatus.prototype.windowWidth = function() {
        return 400;
    };

    const _Window_EquipStatus_refresh = Window_EquipStatus.prototype.refresh;
    Window_EquipStatus.prototype.refresh = function() {
        _Window_EquipStatus_refresh.apply(this, arguments);
        if (this._actor) {
            const lh = this.lineHeight();
            let y = lh * _Window_EquipStatus_numVisibleRows.call(this);
            KNS_EquipParameter.specialParams.forEach(function(key){
                if (KNS_EquipParameter.diceParams.contains(key)){
                    this.knsDrawSpecialDiceParam(0, y, key);
                    y += lh * 2;
                }else{
                    this.knsDrawSpecialParam(0, y, key);
                    y += lh;
                }
            }, this);
        }
    };

    Window_EquipStatus.prototype.knsChangeSpecialParamTextColor = function(key, diff) {
        if (KNS_EquipParameter.oppositeColorParams.contains(key)){ diff *= -1; }
        this.changeTextColor(this.paramchangeTextColor(diff));
    };

    Window_EquipStatus.prototype.knsDrawSpecialParam = function(x, y, key) {
        this.changeTextColor(this.systemColor());
        this.drawText(KNS_EquipParameter.getStatusName(key), x + this.textPadding(), y, 120);
        
        const curValue = this._actor ? this._actor.knsGetMaxSpecialParameter(key) : 0;
        if (this._actor) {
            this.resetTextColor();
            this.drawText(curValue, x + 140, y, 48, 'right');
        }
        this.drawRightArrow(x + 188, y);
        if (this._tempActor) {
            const newValue = this._tempActor.knsGetMaxSpecialParameter(key);
            this.knsChangeSpecialParamTextColor(key, newValue - curValue);
            this.drawText(newValue, x + 222, y, 48, 'right');
        }
    };

    Window_EquipStatus.prototype.knsDrawSpecialDiceParam = function(x, y, key){
        this.changeTextColor(this.systemColor());
        this.drawText(KNS_EquipParameter.getStatusName(key), x + this.textPadding(), y, 120);
        
        let sx = x + 146;
        let sw = this.contents.width - sx;
        if (this._actor) {
            this.resetTextColor();
            this.drawText(this._actor.knsGetStringSpecialParameter(key), sx, y, sw);
        }
        y += this.lineHeight();
        this.drawRightArrow(x, y);
        if (this._tempActor) {
            this.knsChangeSpecialParamTextColor(key,
                this._tempActor.knsGetMaxSpecialParameter(key) - 
                this._actor.knsGetMaxSpecialParameter(key)
            );
            this.drawText(this._tempActor.knsGetStringSpecialParameter(key), sx, y, sw);
        }

    }


    //===================================================
    // alias Scene_Equip
    //===================================================
    const _Scene_Equip_createSlotWindow = Scene_Equip.prototype.createSlotWindow;
    Scene_Equip.prototype.createSlotWindow = function() {
        _Scene_Equip_createSlotWindow.apply(this, arguments);
        this._slotWindow.height = this._slotWindow.fittingHeight(8);
    };

    const _Scene_Equip_createItemWindow = Scene_Equip.prototype.createItemWindow;
    Scene_Equip.prototype.createItemWindow = function() {
        _Scene_Equip_createItemWindow.apply(this, arguments);
        let wy = this._slotWindow.y + this._slotWindow.height;
        this._itemWindow.x = this._slotWindow.x;
        this._itemWindow.width = this._slotWindow.width;
        this._itemWindow.y = wy;
        this._itemWindow.height = Graphics.height - wy;
    };

    //===================================================
    // alias Window_ShopStatus
    //===================================================
    Window_ShopStatus.prototype.pageSize = function() {
        return 3;
    };

    Window_ShopStatus.prototype.drawEquipInfo = function(x, y) {
        const paramWidth = 96;
        const width = (this.contents.width - x - paramWidth) / this.pageSize();
        let y2 = y + this.lineHeight() * 2;
        // draw params start
        this.changeTextColor(this.systemColor());
        for (let i = 0; i < 6; i++){
            this.drawText(TextManager.param(i), x, y2, 192);
            y2 += this.lineHeight();
        }
        KNS_EquipParameter.specialParams.forEach(function(key){
            this.drawText(KNS_EquipParameter.getStatusName(key), x, y2, 192);
            y2 += this.lineHeight();
        }, this);
        this.changeTextColor(this.normalColor());
        // draw params end
        x += paramWidth;
        const members = this.statusMembers();
        for (var i = 0; i < members.length; i++) {
            this.knsDrawActorEquipInfo(members[i], x, y, width);
            x += width;
        }
    };

    Window_ShopStatus.prototype.knsDrawActorEquipInfo = function(actor, x, y, width) {
        var enabled = actor.canEquip(this._item);
        this.changePaintOpacity(enabled);
        this.resetTextColor();
        this.drawText(actor.name(), x, y, 168);
        var item1 = this.currentEquippedItem(actor, this._item.etypeId);
        this.drawItemName(item1, x, y + this.lineHeight(), width);
        this.changePaintOpacity(true);
        if (enabled) {
            this.knsDrawActorParamChanges(actor, x, y + this.lineHeight() * 2, width);
        }
    };
    
    Window_ShopStatus.prototype.knsGetActorParams = function(actor){
        const params = [];
        for (let i = 0; i < 6; i++){
            let param = actor.param(i);
            params.push([param, param]);
        }
        KNS_EquipParameter.specialParams.forEach(function(key){
            let value = actor[key];
            if (KNS_EquipParameter.oppositeColorParams.contains(key)){ value *= -1; }
            params.push([value, actor["S_" + key]]);
        });
        return params;
    }

    Window_ShopStatus.prototype.knsDrawActorParamChanges = function(actor, x, y, width) {
        // change/restore equip
        let oldParam = this.knsGetActorParams(actor);
        let newParam = oldParam;
        if (this._item){
            const index = actor.equipSlots().indexOf(this._item.etypeId);
            if (index !== -1){
                const obj = actor._equips[index].object();
                actor._equips[index].setObject(this._item);
                newParam = this.knsGetActorParams(actor);
                actor._equips[index].setObject(obj);
            }
        }

        for (let i = 0; i < oldParam.length; i++){
            let change = newParam[i][0] - oldParam[i][0];
            this.changeTextColor(this.paramchangeTextColor(change));
            this.drawText(newParam[i][1], x, y, width - 3, 'right');
            y += this.lineHeight();
        }
    };
})();