/*:
 * @plugindesc ver.1.0.1 ループして表示されるふきだしを実装します
 * <AUTHOR>
 *
 * @help
 * ※このプラグインはKNS_RgssTranslator以降に設置してください。
 * 　移動ルート「スクリプト」で以下の記述をすることで
 * そのバルーンを表示することができます。
 *
 * @auto_balloon = バルーンID
 * this._knsAutoBalloon = バルーンID; // 同義
 *
 * 　ふきだしは主人公から離れるほど透明になっていきます。
 *
 * ver.1.0.0(2023-06-02)
 * - デモ
 * ver.1.0.1(2023-06-02)
 * - ページ切り替え時バルーンが消えない不具合を修正しました。
 */
//=====================================================
// new KNS_AutoBalloon
//=====================================================
(function(){
	KNS_RgssTranslator.addMovePattern(/@auto_balloon/g, "this._knsAutoBalloon");
	//=====================================================
	// alias Game_CharacterBase
	//=====================================================
	const _Game_CharacterBase_initMembers = Game_CharacterBase.prototype.initMembers;
	Game_CharacterBase.prototype.initMembers = function(){
		_Game_CharacterBase_initMembers.apply(this, arguments);
		this._knsAutoBalloon = 0;
	}

	Game_CharacterBase.prototype.knsGetDistanceFromPlayer = function(){
		return $gameMap.distance(this._realX, this._realY,
			$gamePlayer._realX, $gamePlayer._realY
		);
	}

	//=====================================================
	// alias Game_Event
	//=====================================================
	const _Game_Event_setupPage = Game_Event.prototype.setupPage;
	Game_Event.prototype.setupPage = function() {
		_Game_Event_setupPage.apply(this, arguments);
		this._knsAutoBalloon = 0;
	};

	//=====================================================
	// alias Sprite_Character
	//=====================================================
	const _Sprite_Character_updateBalloon = Sprite_Character.prototype.updateBalloon;
	Sprite_Character.prototype.updateBalloon = function() {
		_Sprite_Character_updateBalloon.apply(this, arguments);
		this.knsUpdateAutoBalloon();
	}

	Sprite_Character.prototype.knsIsAutoBalloonPlaying = function(){
		return this._character && this._character._knsAutoBalloon;
	}

	Sprite_Character.prototype.knsUpdateAutoBalloon = function(){
		if (this.knsIsAutoBalloonPlaying()){
			if (!this._knsAutoBalloonSprite){
				this._knsAutoBalloonSprite = new Sprite_KnsAutoBalloon();
				this.parent.addChild(this._knsAutoBalloonSprite);
			}
			this._knsAutoBalloonSprite.position.set(this.x, this.y - this.height);
			this._knsAutoBalloonSprite.opacity = this.isBalloonPlaying(
			) ? 0 : 255 - 50 * (this._character.knsGetDistanceFromPlayer() - 4);
			this._knsAutoBalloonSprite.setup(this._character._knsAutoBalloon || 0);
			this._knsAutoBalloonSprite.show();
		}else{
			if (this._knsAutoBalloonSprite){
				this._knsAutoBalloonSprite.hide();
			}
		}
	}
})();

//=====================================================
// alias Sprite_KnsAutoBalloon
//=====================================================
class Sprite_KnsAutoBalloon extends Sprite_Balloon{
	setup(balloonId){
		if (this._balloonId !== balloonId){
			this.knsSetBalloonId(balloonId);
			this.knsSetDuration(false);
		}
	}
	knsSetBalloonId(balloonId){
		this._balloonId = balloonId;
	}
	knsSetDuration(isRepeating){
		this._duration = (isRepeating ? 4 : 8) * this.speed() + 4;
	}
	waitTime(){ return 0; }
	update(){
		super.update();
		if (this._duration <= 0){
			this.knsSetDuration(true);
		}
	}
}