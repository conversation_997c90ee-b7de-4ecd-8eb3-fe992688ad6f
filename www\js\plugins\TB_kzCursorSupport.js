//=============================================================================
// ChoiceCursorCustomizer.js
//=============================================================================

/*:
 * @plugindesc ゲームのロード時に選択肢のカーソルに関する設定を行うプラグインです。
 *
 * @help
 * このプラグインは、ゲームのロード時に選択肢のカーソルに関する設定を行います。
 *
 * プラグインコマンドはありません。
 */

(function() {

    var _Scene_Load_onLoadSuccess = Scene_Load.prototype.onLoadSuccess;
    Scene_Load.prototype.onLoadSuccess = function() {
        _Scene_Load_onLoadSuccess.call(this);
        customizeChoiceCursor();
    };

    function customizeChoiceCursor() {
        // カーソル基本画像を設定
        //$gameSystem.ChoiceCursor.bmp = "cn";

        // カーソルのx補正値を設定
        $gameSystem.ChoiceCursor.x = 3;

        // カーソルのy補正値を設定
        $gameSystem.ChoiceCursor.y = 5;

        // カーソルアニメのフレーム数を設定
        $gameSystem.ChoiceCursor.maxFrame = 10;

        // カーソルアニメの再生間隔を設定
        $gameSystem.ChoiceCursor.interval = 20;
    }

})();
