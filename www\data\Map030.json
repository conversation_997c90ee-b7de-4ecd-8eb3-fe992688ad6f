{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "PerituneMaterial_Exotic2_loop", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "Sea", "pan": 0, "pitch": 100, "volume": 50}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 50, "note": "ビーチ\nロケーションID:21\n別行動可能\n公共の場\n\n夜固定BGM", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 18, "width": 60, "data": [3588, 3612, 3622, 1600, 1601, 3216, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3622, 1600, 1601, 1605, 3234, 3201, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1600, 1605, 3234, 3220, 3201, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1605, 3234, 3201, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3220, 3201, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3228, 3228, 3208, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2548, 2564, 3216, 3200, 3204, 3228, 3228, 3228, 3208, 3200, 3200, 3200, 3200, 3204, 3228, 3228, 3228, 3228, 3228, 3228, 3228, 3208, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 3200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2552, 3240, 3228, 3238, 2562, 2548, 2564, 3240, 3228, 3228, 3228, 3228, 3238, 2562, 2548, 2548, 2548, 2548, 2548, 2564, 3216, 3200, 3204, 3228, 3228, 3228, 3228, 3228, 3228, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2530, 2548, 2548, 2548, 2529, 2528, 2530, 2548, 2548, 2548, 2548, 2548, 2548, 2529, 2528, 2528, 2528, 2528, 2528, 2552, 3240, 3228, 3238, 2562, 2548, 2548, 2548, 2548, 2548, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2530, 2548, 2548, 2548, 2529, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2532, 2556, 2556, 2556, 2556, 2536, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2552, 3235, 3233, 3233, 3237, 2544, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2552, 3232, 3282, 3284, 3244, 2544, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2552, 3232, 3288, 3258, 3284, 2544, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2552, 3241, 3245, 3288, 3286, 2544, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2530, 2548, 2548, 2548, 2548, 2529, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 2528, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 153, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 864, 865, 866, 867, 868, 24, 25, 25, 25, 25, 25, 26, 41, 41, 41, 41, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 872, 873, 874, 875, 876, 32, 33, 33, 33, 33, 33, 34, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 865, 866, 867, 868, 0, 0, 0, 0, 0, 0, 0, 0, 880, 881, 882, 883, 884, 40, 41, 41, 41, 41, 41, 42, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 872, 873, 874, 875, 876, 614, 615, 0, 0, 0, 0, 0, 0, 888, 889, 890, 891, 892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 881, 882, 883, 884, 620, 621, 0, 0, 0, 0, 0, 0, 0, 869, 870, 871, 0, 0, 0, 0, 0, 8, 9, 10, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 889, 890, 891, 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 877, 878, 879, 0, 0, 156, 0, 0, 16, 17, 12, 9, 9, 9, 9, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 869, 870, 871, 0, 0, 0, 0, 0, 200, 166, 0, 0, 0, 885, 886, 887, 0, 0, 0, 0, 0, 24, 25, 25, 25, 25, 25, 25, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 877, 878, 879, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 33, 33, 33, 33, 33, 33, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 885, 886, 887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 41, 41, 41, 41, 41, 41, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 166, 0, 0, 0, 0, 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 864, 865, 866, 867, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 872, 873, 874, 875, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 880, 881, 882, 883, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 888, 889, 890, 891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 869, 870, 871, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 877, 878, 879, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 885, 886, 887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "初期イベント", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 1, 1, 10, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], 0, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 1, 1, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 1, 1, 200, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["ある"]}, {"code": 408, "indent": 1, "parameters": ["***********************"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 30, 1, 1, 110, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 30, 1, 1, 120, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(63, 30, 1, 1, 130, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_102"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["***********************"]}, {"code": 408, "indent": 2, "parameters": ["一人で"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 30, 1, 1, 113, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 30, 1, 1, 114, 0)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_111-112"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_115"]}, {"code": 213, "indent": 2, "parameters": [-1, 6, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["***********************"]}, {"code": 408, "indent": 2, "parameters": ["友達と"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_121-123"]}, {"code": 213, "indent": 2, "parameters": [-1, 6, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 2, "parameters": ["***********************"]}, {"code": 408, "indent": 2, "parameters": ["当時付き合ってた彼女と"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_131-135"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 184]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["***********************"]}, {"code": 408, "indent": 1, "parameters": ["ない"]}, {"code": 408, "indent": 1, "parameters": ["***********************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [722, 722, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 722, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 722, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(10)==3 &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameSwitches.value(722) == true;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var id = $gameParty.members()[2].actorId();"]}, {"code": 655, "indent": 0, "parameters": ["$gameParty.removeActor(id);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 0, "y": 0}, {"id": 2, "name": "場所移動", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 8, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [302]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 0}, {"id": 3, "name": "司祭・主人公[合流]", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 54, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$heroine", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameSwitches.value(97) === false;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [12]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 55, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$protagonist_dot", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["         !$gameSwitches.value(96);"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 3, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["         $gameParty.members().includes($gameActors.actor(2));"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(1052) == 3;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 9}, {"id": 4, "name": "採取", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [467]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 10, "y": 14}, {"id": 5, "name": "イベント仮置き（プライベートセックス）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 27, "y": 1}, {"id": 6, "name": "イベント仮置き　道", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 3}, {"id": 7, "name": "海　モブ男１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male3", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 9, "y": 7}, {"id": 8, "name": "海　モブ男２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 20, "y": 12}, {"id": 9, "name": "海　モブ男３", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 2, "pattern": 0, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 10"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_10\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 7}, {"id": 10, "name": "海　モブ男４", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male5", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 23, "y": 13}, {"id": 11, "name": "海　モブ男５", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male4", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 9}, {"id": 12, "name": "海　モブ女１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 8"], "indent": null}, {"code": 9, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(`_log_database_text_mob_ero_girl${num}`)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 356, "indent": 2, "parameters": ["replaceCharacter -2"]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 214, "indent": 2, "parameters": []}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 6}, {"id": 13, "name": "海　モブ女２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem3", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 8"], "indent": null}, {"code": 9, "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 1, 3]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["num = $gameVariables.value(20)"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(`_log_database_text_mob_ero_girl${num}`)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 356, "indent": 2, "parameters": ["replaceCharacter -2"]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 6]}, {"code": 117, "indent": 2, "parameters": [60]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 214, "indent": 2, "parameters": []}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 655, "indent": 1, "parameters": ["set_mlog(\"_log_database_text_mob_14\")"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [17]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 13, "y": 9}, {"id": 14, "name": "休む", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 14, 1, 1, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 5, 0, 0, 0]}, {"code": 111, "indent": 2, "parameters": [4, 2, 0]}, {"code": 111, "indent": 3, "parameters": [4, 1, 0]}, {"code": 108, "indent": 4, "parameters": ["**********************"]}, {"code": 408, "indent": 4, "parameters": ["二人ともパーティにいる"]}, {"code": 111, "indent": 4, "parameters": [0, 32, 0]}, {"code": 111, "indent": 5, "parameters": [4, 2, 5, 120]}, {"code": 355, "indent": 6, "parameters": ["MapEvent.call(0, 14, 3)"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 108, "indent": 4, "parameters": ["**********************"]}, {"code": 408, "indent": 4, "parameters": ["司祭ソロ"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["**********************"]}, {"code": 408, "indent": 3, "parameters": ["主人公ソロ"]}, {"code": 108, "indent": 3, "parameters": ["主人公逆ナン"]}, {"code": 111, "indent": 3, "parameters": [12, "$gameActors.actor(1).param(5) >= 9"]}, {"code": 355, "indent": 4, "parameters": ["MapEvent.call(0, 14, 2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["**********************"]}, {"code": 408, "indent": 1, "parameters": ["疲労回復"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(1,20)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(2,20)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************"]}, {"code": 408, "indent": 0, "parameters": ["主人公逆ナン"]}, {"code": 408, "indent": 0, "parameters": ["**********************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [16, 0, 4, 8, 4]}, {"code": 203, "indent": 0, "parameters": [17, 0, 4, 9, 4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-4"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 14, 2, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 14, 2, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Wind1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 231, "indent": 1, "parameters": [1, "event-pri-eye_gaze", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 250, "indent": 1, "parameters": [{"name": "Wind1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 213, "indent": 1, "parameters": [16, 8, false]}, {"code": 213, "indent": 1, "parameters": [17, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_13-15"]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [16, 0, 3, 18, 2]}, {"code": 203, "indent": 0, "parameters": [17, 0, 24, 20, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["**********************"]}, {"code": 408, "indent": 0, "parameters": ["二人いるときのイベント　砂で"]}, {"code": 408, "indent": 0, "parameters": ["**********************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [16, 0, 4, 8, 4]}, {"code": 203, "indent": 0, "parameters": [17, 0, 4, 9, 4]}, {"code": 203, "indent": 0, "parameters": [18, 0, 5, 8, 4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 41, "indent": null, "parameters": ["People1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People1", 0]}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 41, "indent": null, "parameters": ["People5", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People5", 0]}]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 41, "indent": null, "parameters": ["People7", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["People7", 0]}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 14, 3, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 14, 3, 200, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-102"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [1, 1, 0]}, {"code": 205, "indent": 1, "parameters": [18, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_103-106"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Sand", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Sand", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Sand", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 231, "indent": 1, "parameters": [17, "stand-heroine-laying1-sand", 0, 1, 21, 22, 100, 100, 255, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_108-116"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 319, "indent": 1, "parameters": [2, 5, 0]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_117-122"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 205, "indent": 1, "parameters": [17, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_123"]}, {"code": 108, "indent": 1, "parameters": ["*************************************"]}, {"code": 408, "indent": 1, "parameters": ["判定開始"]}, {"code": 408, "indent": 1, "parameters": ["*************************************"]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"lucky\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 2, "parameters": ["******************"]}, {"code": 408, "indent": 2, "parameters": ["成功"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [16, {"list": [{"code": 36, "indent": null, "parameters": []}, {"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_130"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 203, "indent": 2, "parameters": [0, 0, 3, 8, 4]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 35, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["sa<PERSON>u", 4]}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 319, "indent": 2, "parameters": [2, 5, 120]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_132-133"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 2, "parameters": [463, 463, 0, 0, 561]}, {"code": 122, "indent": 2, "parameters": [465, 465, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [466, 466, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [467, 467, 0, 0, 50]}, {"code": 122, "indent": 2, "parameters": [468, 468, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 108, "indent": 2, "parameters": ["******************"]}, {"code": 408, "indent": 2, "parameters": ["失敗"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_140"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 203, "indent": 2, "parameters": [16, 0, 3, 9, 8]}, {"code": 205, "indent": 2, "parameters": [16, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["character_swim_male3", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male3", 0]}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_141-150"]}, {"code": 108, "indent": 2, "parameters": ["タッチまんこ"]}, {"code": 250, "indent": 2, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_151-154"]}, {"code": 250, "indent": 2, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 245, "indent": 2, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_155-165"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 250, "indent": 2, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_166-170"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [16, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 319, "indent": 2, "parameters": [2, 5, 120]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_172-173"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [2, 1, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, 0]}, {"code": 129, "indent": 1, "parameters": [2, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["character_swim_fem3", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_fem3", 0]}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["character_swim_fem1", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_fem1", 0]}]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["character_swim_male5", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male5", 0]}]}, {"code": 203, "indent": 0, "parameters": [16, 0, 3, 18, 2]}, {"code": 203, "indent": 0, "parameters": [17, 0, 24, 20, 8]}, {"code": 203, "indent": 0, "parameters": [18, 0, 24, 19, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 8}, {"id": 15, "name": "海　モブ女3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem1", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 12, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 3}, {"id": 16, "name": "海中のモブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem3", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 18}, {"id": 17, "name": "海中のモブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem1", "direction": 8, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 20}, {"id": 18, "name": "海中のモブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male5", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 24, "y": 19}, {"id": 19, "name": "海中モブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Behavior4", "direction": 2, "pattern": 1, "characterIndex": 3}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 10"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 17}, {"id": 20, "name": "海中のモブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male4", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 16, "y": 17}, {"id": 21, "name": "海中モブ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_fem3", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 12, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 17, "y": 17}, {"id": 22, "name": "商人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vxr_cat_r2", "direction": 2, "pattern": 1, "characterIndex": 1}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 302, "indent": 0, "parameters": [0, 180, 0, 0, false]}, {"code": 605, "indent": 0, "parameters": [0, 181, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 183, 0, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 6, "y": 5}, {"id": 23, "name": "モブダークエルフ（エロ）", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "op_ad_de", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["モブダークエルフ（オイル塗り）"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1600]}, {"code": 122, "indent": 0, "parameters": [479, 479, 0, 0, 1600]}, {"code": 122, "indent": 0, "parameters": [473, 473, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [475, 478, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [480, 480, 0, 0, 13]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 23, 1, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 23, 1, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 30, 23, 1, 800, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_3"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61] en(v[445]<50)", "\\v[62]", "\\v[63]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61] en(v[445]<50)"]}, {"code": 108, "indent": 1, "parameters": ["***************************"]}, {"code": 408, "indent": 1, "parameters": ["ふざけるな"]}, {"code": 408, "indent": 1, "parameters": ["***************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101"]}, {"code": 122, "indent": 1, "parameters": [473, 473, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"strength\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 2, "parameters": ["成功"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 212, "indent": 2, "parameters": [23, 1, false]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 30, 23, 1, 103, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 30, 23, 1, 110, 0)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_102"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 3, "parameters": ["オスがみんな思い通りになると思うな～"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_104"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 3, "parameters": [3]}, {"code": 214, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 3, "parameters": ["敗者がどうなるかわかって～"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_111"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 323]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, -1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 201, "indent": 3, "parameters": [0, 30, 26, 2, 8, 0]}, {"code": 203, "indent": 3, "parameters": [0, 0, 26, 1, 2]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_112"]}, {"code": 108, "indent": 3, "parameters": ["********************モブセックススタート*********************"]}, {"code": 117, "indent": 3, "parameters": [512]}, {"code": 108, "indent": 3, "parameters": ["********************モブセックス終了*********************"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 3, "parameters": [3]}, {"code": 214, "indent": 3, "parameters": []}, {"code": 117, "indent": 3, "parameters": [58]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["失敗"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 212, "indent": 2, "parameters": [-1, 1, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "Earth3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$o0096012813123122260", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$o0096012813123122260", 0]}]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 1, 0, 999, false]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 445]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_151"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 214, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["***************************"]}, {"code": 408, "indent": 1, "parameters": ["塗り込みますね"]}, {"code": 408, "indent": 1, "parameters": ["***************************"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 445]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 30, 23, 1, 210, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 30, 23, 1, 220, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(63, 30, 23, 1, 230, 0)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-202"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]", "\\v[63]"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["***************************"]}, {"code": 408, "indent": 2, "parameters": ["おっぱいにも"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_211"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["***************************"]}, {"code": 408, "indent": 2, "parameters": ["ケツ肉にも"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_221"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 2, "parameters": ["***************************"]}, {"code": 408, "indent": 2, "parameters": ["おまんことアナル様にも"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_231"]}, {"code": 245, "indent": 2, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_232"]}, {"code": 108, "indent": 2, "parameters": ["エロ技量判定"]}, {"code": 122, "indent": 2, "parameters": [170, 170, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [163, 163, 0, 1, 465]}, {"code": 122, "indent": 2, "parameters": [164, 164, 0, 0, 0]}, {"code": 355, "indent": 2, "parameters": ["$test_content = \"ero_giryou\""]}, {"code": 117, "indent": 2, "parameters": [20]}, {"code": 355, "indent": 2, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 2, "parameters": [12, "$hantei >= $mokuhyou"]}, {"code": 108, "indent": 3, "parameters": ["成功"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_235-236"]}, {"code": 224, "indent": 3, "parameters": [[255, 255, 255, 255], 20, false]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 250, "indent": 3, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_237-239"]}, {"code": 355, "indent": 3, "parameters": ["var_from_sheet(61, 30, 23, 1, 250, 0)"]}, {"code": 355, "indent": 3, "parameters": ["var_from_sheet(62, 30, 23, 1, 280, 0)"]}, {"code": 102, "indent": 3, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 4, "parameters": ["ありがたき"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 203, "indent": 4, "parameters": [0, 0, 26, 1, 2]}, {"code": 201, "indent": 4, "parameters": [0, 30, 26, 2, 8, 0]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_251"]}, {"code": 108, "indent": 4, "parameters": ["********************モブセックススタート*********************"]}, {"code": 117, "indent": 4, "parameters": [512]}, {"code": 108, "indent": 4, "parameters": ["********************モブセックス終了*********************"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 122, "indent": 4, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 4, "parameters": [3]}, {"code": 214, "indent": 4, "parameters": []}, {"code": 117, "indent": 4, "parameters": [58]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["終了"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 4, "parameters": ["時間ないから"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_281"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 108, "indent": 3, "parameters": ["失敗"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_291-292"]}, {"code": 355, "indent": 3, "parameters": ["show_map_log_window"]}, {"code": 212, "indent": 3, "parameters": [-1, 1, false]}, {"code": 311, "indent": 3, "parameters": [0, 1, 1, 0, 5, false]}, {"code": 313, "indent": 3, "parameters": [0, 0, 0, 30]}, {"code": 101, "indent": 3, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["***************************"]}, {"code": 408, "indent": 1, "parameters": ["無視する"]}, {"code": 408, "indent": 1, "parameters": ["***************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_801"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_900"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(1,-10)"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "op_ad_de", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(2));"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [475, 478, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [480, 480, 0, 0, 10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 2, "y": 11}, {"id": 24, "name": "ナンパ男１", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male2", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 11"], "indent": null}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["海のナンパ野郎"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [25, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 205, "indent": 0, "parameters": [25, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 24, 1, 100, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 24, 1, 200, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(63, 30, 24, 1, 300, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-6"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[83]>=v[82])", "\\v[63]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************"]}, {"code": 408, "indent": 1, "parameters": ["ガンシカ"]}, {"code": 408, "indent": 1, "parameters": ["*****************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_101-107"]}, {"code": 111, "indent": 1, "parameters": [1, 82, 1, 83, 3]}, {"code": 111, "indent": 2, "parameters": [1, 82, 0, 150, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_120-125"]}, {"code": 355, "indent": 3, "parameters": ["MapEvent.call(0, 24, 4)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["無視する"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["無視する"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_110"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[83]>=v[82])"]}, {"code": 108, "indent": 1, "parameters": ["*****************************"]}, {"code": 408, "indent": 1, "parameters": ["ノロケ話"]}, {"code": 408, "indent": 1, "parameters": ["*****************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_201-212"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_220-227"]}, {"code": 213, "indent": 1, "parameters": [24, 8, false]}, {"code": 213, "indent": 1, "parameters": [25, 8, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_230-236"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_240-243"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 214, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(25).erase();"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************"]}, {"code": 408, "indent": 1, "parameters": ["セックス"]}, {"code": 408, "indent": 1, "parameters": ["*****************************"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_301"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 250, "indent": 1, "parameters": [{"name": "Run", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_302"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 214, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(25).erase();"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "character_swim_male2", "direction": 6, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 213, "indent": 0, "parameters": [0, 8, false]}, {"code": 213, "indent": 0, "parameters": [25, 8, true]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 20, "variableValid": true, "variableValue": 9999999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["海ナンパセックス"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 0, "parameters": [462, 462, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [463, 463, 0, 0, 561]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 0, 5]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(464, $gameVariables.value(465) + $gameVariables.value(466));"]}, {"code": 122, "indent": 0, "parameters": [467, 467, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 0, 3]}, {"code": 122, "indent": 0, "parameters": [468, 468, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["***************************************"]}, {"code": 408, "indent": 0, "parameters": ["本処理"]}, {"code": 408, "indent": 0, "parameters": ["***************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 203, "indent": 0, "parameters": [0, 0, 25, 4, 6]}, {"code": 203, "indent": 0, "parameters": [25, 0, 27, 4, 4]}, {"code": 201, "indent": 0, "parameters": [0, 30, 26, 4, 8, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["……"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 108, "indent": 0, "parameters": ["ダブルフェラ"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_2-6"]}, {"code": 108, "indent": 0, "parameters": ["手コキ側見る"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_8-10"]}, {"code": 108, "indent": 0, "parameters": ["再度フェラチオ側見る"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_11-14"]}, {"code": 108, "indent": 0, "parameters": ["舌出る"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_16-20"]}, {"code": 108, "indent": 0, "parameters": ["射精　口に精子"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22-27"]}, {"code": 108, "indent": 0, "parameters": ["Bにフェラ"]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_strong_30", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_29-35"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [3, 2, true]}, {"code": 122, "indent": 0, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_37-40"]}, {"code": 108, "indent": 0, "parameters": ["Bも射精"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"口\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_42-50"]}, {"code": 108, "indent": 0, "parameters": ["*********************************"]}, {"code": 408, "indent": 0, "parameters": ["セックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 2)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [4]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_117"]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 2)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "touch_wet_pussy2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_119-139"]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 2)"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_141"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_142-155"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 420]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_157-160"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(109, 1, 2)"]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_162-166"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [195]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_168-175"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 428]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 561]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 581]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 2]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 428]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [23, 30, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 40, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male2", 0]}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["character_swim_male6", 0]}]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公視点"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 204, "indent": 0, "parameters": [2, 5, 5]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [3, 8, true]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 24, 4, 210, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 24, 4, 220, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_200"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["司祭を待つ"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 204, "indent": 1, "parameters": [8, 5, 6]}, {"code": 201, "indent": 1, "parameters": [0, 30, 26, 10, 8, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 214, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(25).erase();"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_212-213"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_214-216"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["司祭を探す"]}, {"code": 204, "indent": 1, "parameters": [8, 5, 5]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [-1, 4, false]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "indent": null, "parameters": [3]}, {"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 29, "indent": null, "parameters": [4]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "indent": null, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "indent": null, "parameters": [4]}]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 4, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_222-223"]}, {"code": 213, "indent": 1, "parameters": [3, 1, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 245, "indent": 1, "parameters": [{"name": "heart", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_224"]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 205, "indent": 1, "parameters": [25, {"list": [{"code": 16, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null, "parameters": []}]}, {"code": 213, "indent": 1, "parameters": [24, 1, false]}, {"code": 213, "indent": 1, "parameters": [25, 1, true]}, {"code": 213, "indent": 1, "parameters": [24, 8, false]}, {"code": 213, "indent": 1, "parameters": [25, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_225-226"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 214, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(25).erase();"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_227-228"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 246, "indent": 1, "parameters": [1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*********************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*********************************"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 1]}, {"code": 122, "indent": 1, "parameters": [821, 821, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.size() === 3\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 14, "y": 12}, {"id": 25, "name": "ナンパ男２", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "character_swim_male6", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 4"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 24, 1)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "character_swim_male6", "direction": 4, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 355, "indent": 0, "parameters": ["MapEvent.call(0, 24, 2)"]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.size() === 3\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 15, "y": 12}, {"id": 26, "name": "PMイベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ビーチ　疑似フェラ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン２　ビーチ PMマイクロビキニ on ビーチ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [1646, 1646, 0, 0, 1]}, {"code": 203, "indent": 0, "parameters": [0, 0, 12, 11, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 26, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 26, 1, 20, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-12"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_22"]}, {"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン３　フェードアウトイン"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-32"]}, {"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン４　フェードアウトイン"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 26, 1, 50, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_51-56"]}, {"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン５　グラフィック表示　アイスで疑似フェラ"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 13)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "bj_dankyu_middle", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_60-83"]}, {"code": 108, "indent": 0, "parameters": ["*************************************"]}, {"code": 408, "indent": 0, "parameters": ["※シーン６　射精"]}, {"code": 408, "indent": 0, "parameters": ["*************************************"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(116, 5, 13)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_90-97"]}, {"code": 108, "indent": 0, "parameters": ["************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 122, "indent": 0, "parameters": [1646, 1646, 0, 0, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 242, "indent": 0, "parameters": [3]}, {"code": 246, "indent": 0, "parameters": [3]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 245]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 10, 10, 17, 2, 0]}, {"code": 121, "indent": 1, "parameters": [1124, 1124, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 59, "y": 30}, {"id": 27, "name": "イベント - 司祭泳ぐ", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(10)==2"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["司祭泳ぐ"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 30, 1, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 30, 1, 3, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-2"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 111, "indent": 1, "parameters": [1, 8, 0, 21, 0]}, {"code": 108, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["従者でヤリゾーを連れてきている"]}, {"code": 408, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 203, "indent": 2, "parameters": [28, 0, 19, 13, 2]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 0]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_11"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_12"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_13"]}, {"code": 102, "indent": 2, "parameters": [["\\n[1]", "\\n[21]"], -1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\n[1]"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["主人公が助けに行く"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["体力判定"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 3, "parameters": ["$test_content = \"tairyokugiryou\""]}, {"code": 122, "indent": 3, "parameters": [170, 170, 0, 0, 3]}, {"code": 117, "indent": 3, "parameters": [20]}, {"code": 355, "indent": 3, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 216, "indent": 3, "parameters": [0]}, {"code": 111, "indent": 3, "parameters": [0, 80, 0]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_110-112"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_120-122"]}, {"code": 222, "indent": 4, "parameters": []}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["_event_data_base_text_123-124"]}, {"code": 221, "indent": 4, "parameters": []}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 311, "indent": 4, "parameters": [0, 1, 1, 0, 5, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\n[21]"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["ヤリゾーが助けに行く"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_201-202"]}, {"code": 111, "indent": 3, "parameters": [0, 36, 0]}, {"code": 117, "indent": 4, "parameters": [993]}, {"code": 102, "indent": 4, "parameters": [["Lvl 3", "Lvl 4", "Lvl 5", "Current Lvl"], -1, 0, 2, 0]}, {"code": 402, "indent": 4, "parameters": [0, "Lvl 3"]}, {"code": 119, "indent": 5, "parameters": ["lvl3"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "Lvl 4"]}, {"code": 119, "indent": 5, "parameters": ["lvl4"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [2, "Lvl 5"]}, {"code": 119, "indent": 5, "parameters": ["lvl5"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [3, "Current Lvl"]}, {"code": 119, "indent": 5, "parameters": ["Current lvl"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 118, "indent": 4, "parameters": ["Current lvl"]}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 3, 2]}, {"code": 118, "indent": 5, "parameters": ["lvl3"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 27, 2)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 5, "parameters": ["lvl4"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 27, 3)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 5, "parameters": ["lvl5"]}, {"code": 355, "indent": 5, "parameters": ["MapEvent.call(0, 27, 4)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["従者がヤリゾーではない"]}, {"code": 408, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 355, "indent": 2, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(1,-10)"]}, {"code": 655, "indent": 2, "parameters": ["tp_heal(2,-10)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了"]}, {"code": 408, "indent": 1, "parameters": ["****************************************************************"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 203, "indent": 1, "parameters": [28, 0, 1, 8, 2]}, {"code": 122, "indent": 1, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["溺れる司祭　関係度3以下"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウト　フェードイン　離島"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 4, 42, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 1, "parameters": [3, 0, 4, 42, 0]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 203, "indent": 1, "parameters": [28, 0, 5, 42, 4]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_40-44"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭意識なくてもヤリゾーシバく"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 212, "indent": 1, "parameters": [28, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-55"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["司祭意識なくてもヤリゾーシバく"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 212, "indent": 1, "parameters": [28, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_60-62"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["オマンコに人工呼吸シーン"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 250, "indent": 1, "parameters": [{"name": "chupon_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_70-82"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["オマンコに手マンシーン"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90-95"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["潮吹き"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 1"]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-105"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公と合流する"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 30, 18, 13, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 19, 13, 4]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-143"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["溺れる司祭　関係度4"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウト　フェードイン　離島"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 4, 42, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 1, "parameters": [3, 0, 4, 42, 0]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 203, "indent": 1, "parameters": [28, 0, 5, 42, 4]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-34"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["オマンコに手マンシーン"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_35-50"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["潮吹き"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 1"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_60-68"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["挿入"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 0"]}, {"code": 122, "indent": 1, "parameters": [25, 25, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_69-93"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100-104"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公と合流する"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 30, 18, 13, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 19, 13, 4]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_140-143"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["溺れる司祭　関係度5"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウト　フェードイン　離島"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [0, 35, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 4, 42, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 1, "parameters": [3, 0, 4, 42, 0]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine_sleep", 0]}]}, {"code": 203, "indent": 1, "parameters": [28, 0, 5, 42, 4]}, {"code": 117, "indent": 1, "parameters": [23]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_30-33"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["ヤリゾーのキス"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_35-40"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["アクメ"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 1"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_42-46"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["種付けプレス"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 250, "indent": 1, "parameters": [{"name": "touch_wet_pussy1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 0"]}, {"code": 122, "indent": 1, "parameters": [27, 27, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [28, 28, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [54, 54, 0, 0, 120]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 23, 3)"]}, {"code": 245, "indent": 1, "parameters": [{"name": "piston_H_fast", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_62-63"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["潮吹き"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 250, "indent": 1, "parameters": [{"name": "squirting3", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 1, "parameters": [18, 18, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$squirt = 1"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 23, 3)"]}, {"code": 117, "indent": 1, "parameters": [958]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_64-75"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["射精"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 122, "indent": 1, "parameters": [40, 40, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(109, 23, 3)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_in_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_90-91"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["フェードアウトフェードイン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_92"]}, {"code": 108, "indent": 0, "parameters": ["司祭ダウン画像"]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [195]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_100-101"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["主人公と合流する"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 18, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null, "parameters": []}]}, {"code": 201, "indent": 0, "parameters": [0, 30, 18, 13, 2, 0]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 19, 13, 4]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_120-122"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_130"]}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [97]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 18, "y": 14}, {"id": 28, "name": "従者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(8)==21"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$p_maker", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(8)==10"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(8)==11"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(8)==12"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "vampire", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(8)==19"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [1, 5, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, {"id": 29, "name": "海水浴", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 29, 1, 1, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[80]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************"]}, {"code": 408, "indent": 1, "parameters": ["入浴開始"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Water1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protag-naked", 0]}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 212, "indent": 1, "parameters": [-1, 69, true]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(1,-10)"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$protagonist_dot", 0]}]}, {"code": 111, "indent": 2, "parameters": [4, 1, 6, 30]}, {"code": 313, "indent": 3, "parameters": [0, 1, 1, 30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(2,-10)"]}, {"code": 122, "indent": 2, "parameters": [1701, 2600, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 70]}, {"code": 122, "indent": 2, "parameters": [90, 90, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 71]}, {"code": 122, "indent": 2, "parameters": [91, 91, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 72]}, {"code": 122, "indent": 2, "parameters": [92, 92, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 73]}, {"code": 122, "indent": 2, "parameters": [93, 93, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 74]}, {"code": 122, "indent": 2, "parameters": [94, 94, 0, 0, 0]}, {"code": 313, "indent": 2, "parameters": [0, 2, 1, 75]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 0]}, {"code": 111, "indent": 2, "parameters": [4, 2, 6, 31]}, {"code": 313, "indent": 3, "parameters": [0, 2, 1, 31]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 4, "indent": null, "parameters": []}, {"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null, "parameters": []}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[80]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 12, "y": 15}, {"id": 30, "name": "イベント - バレーボール", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(10) === 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["バレーボール"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["海上　主人公の後をついていく司祭"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 30, 1, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 30, 1, 3, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 111, "indent": 1, "parameters": [1, 8, 0, 21, 0]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["分岐　従者ヤリゾーの場合はラベル　ヤリゾー　にジャンプ"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 201, "indent": 2, "parameters": [0, 30, 10, 11, 4, 0]}, {"code": 203, "indent": 2, "parameters": [28, 0, 9, 10, 2]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 203, "indent": 2, "parameters": [3, 0, 8, 11, 6]}, {"code": 205, "indent": 2, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 205, "indent": 2, "parameters": [28, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 216, "indent": 2, "parameters": [1]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(61, 30, 30, 1,11, 0)"]}, {"code": 355, "indent": 2, "parameters": ["var_from_sheet(62, 30, 30, 1,20, 0)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_10"]}, {"code": 102, "indent": 2, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_12-16"]}, {"code": 119, "indent": 3, "parameters": ["ヤリゾーボール遊び"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_21"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["終了"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 119, "indent": 3, "parameters": ["終了"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["ヤリゾーボール遊び"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["司祭がヤリゾーをハブろうとする"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_30-39"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["司祭の後ろをヤリゾーが通る"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 205, "indent": 2, "parameters": [28, {"list": [{"code": 2, "indent": null, "parameters": []}, {"code": 2, "indent": null, "parameters": []}, {"code": 1, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 2, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null, "parameters": []}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_50-51"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["衣装はぎ取る"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip3", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 117, "indent": 2, "parameters": [23]}, {"code": 213, "indent": 2, "parameters": [-1, 1, false]}, {"code": 213, "indent": 2, "parameters": [3, 1, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_60-62"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [23]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["分岐　従者居ない場合はＡＰ－１　体力-30　純愛+1で終了"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 3]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(1,-30)"]}, {"code": 355, "indent": 2, "parameters": ["tp_heal(2,-30)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 1, "parameters": ["終了"]}, {"code": 408, "indent": 1, "parameters": ["*****************************************************************"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 1, 8, 2]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 8, "y": 11}, {"id": 31, "name": "イベント - 日焼け止め", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(10)==2"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーポロリン"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$mm = 30"]}, {"code": 655, "indent": 0, "parameters": ["$ee = 31"]}, {"code": 655, "indent": 0, "parameters": ["$pp = 1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $mm, $ee, $pp, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, $mm, $ee, $pp, 10, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 111, "indent": 1, "parameters": [1, 8, 0, 21, 0]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾー連れてきてる"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["ヤリゾーフェードイン"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 203, "indent": 2, "parameters": [28, 0, 26, 13, 2]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 205, "indent": 2, "parameters": [28, {"list": [{"code": 37, "indent": null, "parameters": []}, {"code": 25, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null, "parameters": []}]}, {"code": 505, "indent": 2, "parameters": [{"code": 25, "indent": null, "parameters": []}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_30-33"]}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["分岐　浸食レベル２以下"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 2, 2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_100-104"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["浸食レベル２なら　興奮+10"]}, {"code": 111, "indent": 3, "parameters": [1, 204, 0, 2, 0]}, {"code": 122, "indent": 4, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 4, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 4, "parameters": [2]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["分岐　浸食レベル３"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 3, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_200-201"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["興奮+20"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 20]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["分岐　浸食レベル４－５"]}, {"code": 408, "indent": 2, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 2, "parameters": [1, 204, 0, 4, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_300-303"]}, {"code": 108, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 3, "parameters": ["興奮+50"]}, {"code": 408, "indent": 3, "parameters": ["*****************************************************************"]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 3, "parameters": [60, 60, 0, 0, 50]}, {"code": 117, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 1, 8, 2]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 26, "y": 13}, {"id": 32, "name": "ウォーターボール", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": true, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "People5", "direction": 2, "pattern": 1, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "indent": null, "parameters": ["@auto_balloon = 8"]}, {"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<SAN_ExtendedEventPage:{"]}, {"code": 408, "indent": 0, "parameters": ["  \"trigger\":\"$gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["             $gameParty.size() === 2\""]}, {"code": 408, "indent": 0, "parameters": ["}>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ウォーターボール"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 32, 1, 10, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 32, 1, 20, 0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21"]}, {"code": 119, "indent": 1, "parameters": ["末"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ウォーターバルーン導入（従者セット）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 30, 7, 10, 6, 0]}, {"code": 216, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["$heroine", 0]}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 7, 9, 6]}, {"code": 203, "indent": 0, "parameters": [28, 0, 9, 12, 8]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null, "parameters": []}]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 21, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_100"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": ["PM"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 10, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_110-114"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ウォーターバルーン開始"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_300-306"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, 30, 32, 1, 320, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, 30, 32, 1, 500, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62]"], -1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"technique\""]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 108, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["成功"]}, {"code": 118, "indent": 2, "parameters": ["成功"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_350-356"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_360-361"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 119, "indent": 2, "parameters": ["終了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["****************************************************************"]}, {"code": 408, "indent": 2, "parameters": ["失敗"]}, {"code": 118, "indent": 2, "parameters": ["失敗"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_400"]}, {"code": 122, "indent": 2, "parameters": [456, 456, 0, 0, 1000]}, {"code": 122, "indent": 2, "parameters": [463, 463, 0, 0, 999]}, {"code": 122, "indent": 2, "parameters": [465, 465, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [466, 466, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [464, 464, 0, 1, 465]}, {"code": 122, "indent": 2, "parameters": [464, 464, 1, 1, 466]}, {"code": 122, "indent": 2, "parameters": [467, 467, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [831]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_401-406"]}, {"code": 119, "indent": 2, "parameters": ["失敗後"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 1, "parameters": ["何もしない"]}, {"code": 119, "indent": 1, "parameters": ["失敗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["失敗後"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["従者が居る場合（失敗したり何もしなかった場合）"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 21, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1000-1003"]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 21]}, {"code": 117, "indent": 1, "parameters": [831]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1010-1015"]}, {"code": 111, "indent": 1, "parameters": [1, 204, 0, 2, 1]}, {"code": 108, "indent": 2, "parameters": ["絆レベルが２以上なら"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["絆レベルが１以下なら"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, -10]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["**********************************"]}, {"code": 408, "indent": 0, "parameters": ["PM"]}, {"code": 111, "indent": 0, "parameters": [1, 8, 0, 10, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1110-1113"]}, {"code": 212, "indent": 1, "parameters": [3, 69, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1120-1121"]}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["従者が居ない場合"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null, "parameters": []}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9000"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 38, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null, "parameters": []}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "indent": null, "parameters": ["", 0]}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "indent": null, "parameters": ["", 0]}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 26, 9, 2]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null, "parameters": []}, {"code": 0, "indent": null, "parameters": []}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null, "parameters": []}]}, {"code": 203, "indent": 0, "parameters": [28, 0, 1, 8, 2]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 216, "indent": 0, "parameters": [0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["末"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 11, "y": 10}, {"id": 33, "name": "配置用モブＡ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Animal", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.AddToCurrentScene();"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.SetVisible(true);"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.SetActor($gameActors._data[1]); "]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.Name.Text = $gameActors._data[1]._name;"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.draw();"]}, {"code": 355, "indent": 0, "parameters": ["let obj = { _states: [] };    // creates actor simulation"]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(7);         // gives it state 7 "]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(8); "]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(9); "]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(10); "]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(3); "]}, {"code": 655, "indent": 0, "parameters": ["obj._states.push(4); "]}, {"code": 655, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowEnemy.SetActor(obj);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Animal", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [132, 132, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.AddToCurrentScene();"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.SetVisible(true);"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.SetActor($gameActors._data[1]); "]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.Name.Text = $gameActors._data[1]._name;"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.MP_StatusPoints.Max = $gameActors.actor(1).param(1)"]}, {"code": 355, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowMC.draw();"]}, {"code": 355, "indent": 0, "parameters": ["v = [1,2]"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(132,v)"]}, {"code": 355, "indent": 0, "parameters": ["v = $gameVariables.value(132)"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["let obj = { _states: v};    // creates actor simulation"]}, {"code": 655, "indent": 0, "parameters": ["$CombatStatusDisplayManager.WindowEnemy.SetActor(obj);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 15}, {"id": 34, "name": "クエスト『貝殻探し』", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People6", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(9) >= 2 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(5) == 1"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-9"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [1052, 1052, 0, 0, 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["val_in_database(54, \"quest_102001\")"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["set_mlog(\"_log_database_text_quest_start\")"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 15"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People6", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(9) >= 2 &&  $gameVariables.value(5) == 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(1052) == 1;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 8"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People6", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameParty.members().includes($gameActors.actor(1)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(9) >= 2 &&  $gameVariables.value(5) == 1 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(1052) == 2;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 355, "indent": 0, "parameters": ["$mm = 30"]}, {"code": 655, "indent": 0, "parameters": ["$ee = 34"]}, {"code": 655, "indent": 0, "parameters": ["$pp = 3"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $mm, $ee, $pp, 10, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(62, $mm, $ee, $pp, 20, 0)"]}, {"code": 655, "indent": 0, "parameters": ["var_from_sheet(63, $mm, $ee, $pp, 30, 0)"]}, {"code": 102, "indent": 0, "parameters": [["\\v[61]", "\\v[62] en(v[183]>=50)", "\\v[63] en(v[183]>=100)"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\v[61]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_11-14"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\v[62] en(v[183]>=50)"]}, {"code": 108, "indent": 1, "parameters": ["司祭とセックスする"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_21-22"]}, {"code": 250, "indent": 1, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 122, "indent": 1, "parameters": [39, 39, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [69]}, {"code": 245, "indent": 1, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_23-25"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\v[63] en(v[183]>=100)"]}, {"code": 108, "indent": 1, "parameters": ["女をナンパする"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_31"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 129, "indent": 1, "parameters": [2, 1, false]}, {"code": 122, "indent": 1, "parameters": [1052, 1052, 0, 0, 3]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 355, "indent": 1, "parameters": ["$test_content = \"koushou\""]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [170, 170, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [20]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [0, 80, 0]}, {"code": 108, "indent": 2, "parameters": ["成功"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_50-52"]}, {"code": 108, "indent": 2, "parameters": ["セックスシーン開始"]}, {"code": 245, "indent": 2, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 122, "indent": 2, "parameters": [30, 30, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [23, 23, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [478, 478, 0, 0, 8]}, {"code": 122, "indent": 2, "parameters": [24, 24, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [476, 476, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [477, 477, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [480, 480, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [479, 479, 0, 0, 1000]}, {"code": 122, "indent": 2, "parameters": [456, 456, 0, 0, 1000]}, {"code": 117, "indent": 2, "parameters": [662]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_53-57"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "!cum_in_long1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 2, "parameters": [95]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [662]}, {"code": 117, "indent": 2, "parameters": [513]}, {"code": 108, "indent": 2, "parameters": ["****カットイン呼び出し*****"]}, {"code": 122, "indent": 2, "parameters": [40, 40, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [468, 468, 0, 0, 2]}, {"code": 111, "indent": 2, "parameters": [1, 30, 0, 0, 0]}, {"code": 121, "indent": 3, "parameters": [25, 25, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 121, "indent": 3, "parameters": [25, 25, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["MapEvent.call(173, 1, 1)"]}, {"code": 108, "indent": 2, "parameters": ["****カットイン呼び出しここまで*****"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 117, "indent": 2, "parameters": [58]}, {"code": 117, "indent": 2, "parameters": [212]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_58"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["失敗"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_80-81"]}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [2, 0, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 126, "indent": 0, "parameters": [504, 1, 0, 1]}, {"code": 122, "indent": 0, "parameters": [1052, 1052, 0, 0, 98]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [46]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People6", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(1052) == 3;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["@auto_balloon = 12"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1052, "variableValid": true, "variableValue": 98}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 13}, {"id": 35, "name": "イベント - オイル", "note": "", "pages": [{"conditions": {"actorId": 2, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 2, "pattern": 1, "characterIndex": 4}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 108, "indent": 0, "parameters": ["<KNS_Trigger>"]}, {"code": 408, "indent": 0, "parameters": ["  return $gameVariables.value(10)==1 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameVariables.value(5)==0 &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameParty.members().includes($gameActors.actor(2)) &&"]}, {"code": 408, "indent": 0, "parameters": ["  $gameSwitches.value(32) == true;"]}, {"code": 408, "indent": 0, "parameters": ["</K<PERSON>_Trigger>"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$mm = 30"]}, {"code": 655, "indent": 0, "parameters": ["$ee = 35"]}, {"code": 655, "indent": 0, "parameters": ["$pp = 1"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(61, $mm, $ee, $pp, 2, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var_from_sheet(62, $mm, $ee, $pp, 10, 0)"]}, {"code": 108, "indent": 0, "parameters": ["イベントテストモード"]}, {"code": 111, "indent": 0, "parameters": [0, 36, 0]}, {"code": 117, "indent": 1, "parameters": [993]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 102, "indent": 1, "parameters": [["lvl1"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "lvl1"]}, {"code": 119, "indent": 2, "parameters": ["Lvl1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["Lvl2", "Lvl3", "Lvl4", "Lvl5", "Lvlx"], -1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Lvl2"]}, {"code": 119, "indent": 2, "parameters": ["Lvl2"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Lvl3"]}, {"code": 119, "indent": 2, "parameters": ["Lvl3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Lvl4"]}, {"code": 119, "indent": 2, "parameters": ["Lvl4"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Lvl5"]}, {"code": 119, "indent": 2, "parameters": ["Lvl5"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Lvlx"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　浸食レベル１"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 1, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl1"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(30, 35, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　浸食レベル２"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 2, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl2"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(30, 35, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　浸食レベル３"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 3, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl3"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(30, 35, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　浸食レベル４"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 4, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl4"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(30, 35, 5)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["分岐　浸食レベル５"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 204, 0, 5, 0]}, {"code": 118, "indent": 1, "parameters": ["Lvl5"]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(30, 35, 6)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル Lvl1"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-5"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル Lvl2"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["カンチョー"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 1, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5-7"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["背面ケツ穴開き"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [39, 39, 0, 0, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Water2", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [70]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_9-11"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル Lvl3"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"アナル\""]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ケツ揉み　塗り"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-12"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-22"]}, {"code": 108, "indent": 0, "parameters": ["ケツ穴に指つっこむ"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30"]}, {"code": 108, "indent": 0, "parameters": ["アクメ"]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1;"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_40-41"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル Lvl4"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-3"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["ガシマン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_crazy", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_10-13"]}, {"code": 108, "indent": 0, "parameters": ["アクメ"]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1;"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 5, 1)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_14-17"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": true, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ヤリゾーオイル Lvl5"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [456, 456, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [15, 15, 0, 0, 21]}, {"code": 122, "indent": 0, "parameters": [458, 458, 0, 4, "\"膣\""]}, {"code": 122, "indent": 0, "parameters": [465, 465, 0, 1, 1682]}, {"code": 122, "indent": 0, "parameters": [466, 466, 0, 1, 1683]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1-4"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["立ちセックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 20]}, {"code": 117, "indent": 0, "parameters": [69]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_5"]}, {"code": 250, "indent": 0, "parameters": [{"name": "!touch_wet2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [28, 28, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [69]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_6-9"]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["中継ぎシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 41, "parameters": ["$pixel_animation_doggyossan", 0], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$pixel_animation_doggyossan", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [33, {"list": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$protagonist_dot", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [33, 0, 21, 11, 6]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [33, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [33, 1, false]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["岩陰に隠れながらのセックスシーン"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [18, 18, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG_top1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_w,$s_h,255,0)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "pstion_A_middle", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-21"]}, {"code": 108, "indent": 0, "parameters": ["Xray"]}, {"code": 355, "indent": 0, "parameters": ["$no = `${$e_name}-BG_top1`"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$picture = screen.pictures[$layer20]"]}, {"code": 655, "indent": 0, "parameters": ["$picture.show($no,0,$gameVariables.value(21),"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(22),$s_w,$s_h,120,0)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_30-36"]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1;"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 3, 1)"]}, {"code": 117, "indent": 0, "parameters": [958]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_37-38"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 82]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 214]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 108, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 408, "indent": 0, "parameters": ["終了"]}, {"code": 408, "indent": 0, "parameters": ["*****************************************************************"]}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [33, 0, 59, 15, 6]}, {"code": 122, "indent": 0, "parameters": [6, 6, 2, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 99999}, "directionFix": false, "image": {"tileId": 0, "characterName": "ossan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 5, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 26, "y": 11}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor5", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["var actorId = 2; // ID of Actor 2"]}, {"code": 655, "indent": 0, "parameters": ["var actor = $gameActors.actor(actorId);"]}, {"code": 655, "indent": 0, "parameters": ["var weaponId = actor.weapons()[0] ? actor.weapons()[0].id : 0;"]}, {"code": 655, "indent": 0, "parameters": ["console.log(\"Weapon ID: \" + weaponId);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(54,weaponId)"]}, {"code": 356, "indent": 0, "parameters": ["KNS_EquipPrefixSuffixes GetPrefix W \\v[54] 52"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 12}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor4", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 122, "indent": 0, "parameters": [1602, 1602, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["$squirt = 1;"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 17, 1)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 10}, {"id": 38, "name": "休憩イベント", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 10, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "sa<PERSON>u", "direction": 6, "pattern": 1, "characterIndex": 7}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true, "list": [{"code": 117, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [945]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 2}]}