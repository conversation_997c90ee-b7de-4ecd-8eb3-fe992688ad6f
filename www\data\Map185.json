{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 100}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 80}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": false, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 17, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "マジック・ディルド", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["********************"]}, {"code": 408, "indent": 1, "parameters": ["司祭と主人公が居る場合"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["********************"]}, {"code": 408, "indent": 1, "parameters": ["司祭のみの場合"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.mapId() === 22"]}, {"code": 111, "indent": 2, "parameters": [0, 115, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["_event_data_base_text_1-2"]}, {"code": 119, "indent": 3, "parameters": ["開始"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-13"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["開始"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 255, 255], 20, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "teman_strong", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 108, "indent": 0, "parameters": ["なぜか二重にしないと表示されないため二重にしている"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 6, 1)"]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 6, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_20-26"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 117, "indent": 0, "parameters": [95]}, {"code": 250, "indent": 0, "parameters": [{"name": "squirting4", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 122, "indent": 0, "parameters": [40, 40, 0, 0, 1]}, {"code": 122, "indent": 0, "parameters": [23, 23, 0, 0, 2]}, {"code": 122, "indent": 0, "parameters": [25, 25, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(70, 6, 1)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_27-28"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 0, 0, 186]}, {"code": 122, "indent": 0, "parameters": [60, 60, 0, 0, -100]}, {"code": 117, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [57]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************"]}, {"code": 408, "indent": 0, "parameters": ["ソロ行動時に自室で主人公が部屋に居る場合"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.mapId() === 22"]}, {"code": 111, "indent": 1, "parameters": [0, 115, 1]}, {"code": 119, "indent": 2, "parameters": ["終幕の会話"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["********************"]}, {"code": 408, "indent": 0, "parameters": ["終幕の会話"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 118, "indent": 1, "parameters": ["終幕の会話"]}, {"code": 108, "indent": 1, "parameters": ["********************"]}, {"code": 408, "indent": 1, "parameters": ["司祭と主人公が居る場合"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_50-51"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(61, 185, 1, 1, 60, 0)"]}, {"code": 355, "indent": 1, "parameters": ["var_from_sheet(62, 185, 1, 1, 70, 0)"]}, {"code": 102, "indent": 1, "parameters": [["\\v[61]", "\\v[62]"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\v[61]"]}, {"code": 108, "indent": 2, "parameters": ["良かった"]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 122, "indent": 2, "parameters": [23, 30, 0, 0, 0]}, {"code": 117, "indent": 2, "parameters": [103]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_61"]}, {"code": 122, "indent": 2, "parameters": [58, 58, 0, 0, 83]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 117, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\v[62]"]}, {"code": 108, "indent": 2, "parameters": ["良くない"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_71"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 1, "y": 1}, {"id": 2, "name": "魔法のオナホ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 2, 0]}, {"code": 108, "indent": 1, "parameters": ["***"]}, {"code": 408, "indent": 1, "parameters": ["司祭がいる場合"]}, {"code": 118, "indent": 1, "parameters": ["司祭がいる場合"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-4"]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 213, "indent": 2, "parameters": [-1, 6, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["終了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.mapId() === 22"]}, {"code": 111, "indent": 2, "parameters": [0, 115, 1]}, {"code": 111, "indent": 3, "parameters": [1, 261, 0, 0, 2]}, {"code": 119, "indent": 4, "parameters": ["司祭がいる場合"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["***"]}, {"code": 408, "indent": 1, "parameters": ["主人公ソロの場合"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_10-11"]}, {"code": 122, "indent": 1, "parameters": [479, 479, 0, 0, 9000]}, {"code": 122, "indent": 1, "parameters": [456, 456, 0, 0, 9000]}, {"code": 122, "indent": 1, "parameters": [473, 473, 0, 0, 0]}, {"code": 122, "indent": 1, "parameters": [465, 465, 0, 0, 99]}, {"code": 122, "indent": 1, "parameters": [466, 466, 0, 0, 99]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [475, 478, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["********************モブセックススタート*********************"]}, {"code": 117, "indent": 1, "parameters": [512]}, {"code": 108, "indent": 1, "parameters": ["********************モブセックス終了*********************"]}, {"code": 213, "indent": 1, "parameters": [-1, 4, false]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-50)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["終了"]}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 3, "y": 1}, {"id": 3, "name": "リビング・オナホ", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 0, "indent": 0, "parameters": []}]}], "x": 5, "y": 1}, {"id": 4, "name": "ピンクの書", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "indent": null, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true, "list": [{"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 108, "indent": 1, "parameters": ["主人公だけで行動中"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 250, "indent": 1, "parameters": [{"name": "Book1", "pan": 0, "pitch": 100, "volume": 80}]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 213, "indent": 1, "parameters": [-1, 4, true]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 185]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["司祭だけで行動中"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1-4"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}]}], "x": 7, "y": 1}, {"id": 5, "name": "ミルキングポーション", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ミルキングポーション（Root)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["主人公"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(185, 5, 2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["司祭"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(185, 5, 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 21, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(185, 5, 4)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["ＰＭ"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["MapEvent.call(185, 5, 5)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ミルキングポーション（主人公)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 1, 0]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 212, "indent": 1, "parameters": [-1, 49, false]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_1"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(1,-100)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3"]}, {"code": 111, "indent": 1, "parameters": [8, 65]}, {"code": 126, "indent": 2, "parameters": [65, 1, 0, 1]}, {"code": 126, "indent": 2, "parameters": [43, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ミルキングポーション（司祭)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var actorId = 2;"]}, {"code": 655, "indent": 1, "parameters": ["var index = $gameParty.members().findIndex(actor => actor.actorId() === actorId);"]}, {"code": 655, "indent": 1, "parameters": [""]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(54,index)"]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["replaceCharacter -1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 3, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 212, "indent": 1, "parameters": [-1, 49, false]}, {"code": 111, "indent": 1, "parameters": [1, 54, 0, 0, 0]}, {"code": 356, "indent": 2, "parameters": ["replaceCharacter -1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 3, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["_event_data_base_text_1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_2"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 355, "indent": 1, "parameters": ["$milk = 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_3-4"]}, {"code": 117, "indent": 1, "parameters": [95]}, {"code": 250, "indent": 1, "parameters": [{"name": "cum_out_long2", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_5-6"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$milk = 0"]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["_event_data_base_text_7"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 122, "indent": 1, "parameters": [58, 58, 0, 0, 421]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 2, 200, 500]}, {"code": 117, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["tp_heal(2,-100)"]}, {"code": 111, "indent": 1, "parameters": [8, 65]}, {"code": 126, "indent": 2, "parameters": [65, 1, 0, 1]}, {"code": 126, "indent": 2, "parameters": [42, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ミルキングポーション（ヤリゾー)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["ヤリゾー"]}, {"code": 355, "indent": 0, "parameters": ["var actorId = 21;"]}, {"code": 655, "indent": 0, "parameters": ["var index = $gameParty.members().findIndex(actor => actor.actorId() === actorId);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(54,index)"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 212, "indent": 0, "parameters": [-1, 49, false]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$milk = 1;"]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(10,-100)"]}, {"code": 111, "indent": 0, "parameters": [8, 65]}, {"code": 126, "indent": 1, "parameters": [65, 1, 0, 1]}, {"code": 126, "indent": 1, "parameters": [43, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["$milk = 0;"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["************************************************************"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["ミルキングポーション（ＰＭ)"]}, {"code": 408, "indent": 0, "parameters": [""]}, {"code": 408, "indent": 0, "parameters": ["************************************************************"]}, {"code": 108, "indent": 0, "parameters": ["PM"]}, {"code": 355, "indent": 0, "parameters": ["var actorId = 10;"]}, {"code": 655, "indent": 0, "parameters": ["var index = $gameParty.members().findIndex(actor => actor.actorId() === actorId);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(54,index)"]}, {"code": 117, "indent": 0, "parameters": [58]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 212, "indent": 0, "parameters": [-1, 49, false]}, {"code": 111, "indent": 0, "parameters": [1, 54, 0, 1, 0]}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["replaceCharacter -3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 122, "indent": 0, "parameters": [30, 30, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$milk = 1;"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["MapEvent.call(214, 10, 5)"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "cum_out_long1", "pan": 0, "pitch": 100, "volume": 100}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["_event_data_base_text_1"]}, {"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 355, "indent": 0, "parameters": ["tp_heal(10,-100)"]}, {"code": 111, "indent": 0, "parameters": [8, 65]}, {"code": 126, "indent": 1, "parameters": [65, 1, 0, 1]}, {"code": 126, "indent": 1, "parameters": [181, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 355, "indent": 0, "parameters": ["$milk = 0;"]}, {"code": 355, "indent": 0, "parameters": ["hide_map_log_window"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 1}, {"id": 6, "name": "ステルスポーション", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["show_map_log_window"]}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 1, 0]}, {"code": 212, "indent": 1, "parameters": [-1, 31, false]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 24]}, {"code": 322, "indent": 1, "parameters": [1, "$protagonist_dot_stealth", 0, "", 0, ""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 2, 0]}, {"code": 212, "indent": 1, "parameters": [-1, 31, false]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 24]}, {"code": 322, "indent": 1, "parameters": [2, "$heroine_stealth", 0, "", 0, ""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 271, 0, 21, 0]}, {"code": 212, "indent": 1, "parameters": [-1, 31, false]}, {"code": 313, "indent": 1, "parameters": [0, 21, 0, 24]}, {"code": 322, "indent": 1, "parameters": [21, "ossan", 5, "", 0, ""]}, {"code": 355, "indent": 1, "parameters": ["hide_map_log_window"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["……奥さん。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["なんでアッシだけにステルスポーション使うんです？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 122, "indent": 1, "parameters": [23, 23, 0, 0, 3]}, {"code": 117, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[2]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["なるべく視界に入れたくないので！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[6][\\n[21]]\\c[0]"]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 355, "indent": 1, "parameters": ["show_map_log_window"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 1}]}